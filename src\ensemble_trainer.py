"""
集成学习训练器 - 方案3实现
整合分段专家模型、物理-数据双轨道、动态权重集成、残差递进建模
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import joblib
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from .ensemble_models import (
    WindSpeedExpertEnsemble,
    PhysicsDataDualTrack, 
    DynamicWeightEnsemble,
    ResidualProgressiveModel
)
from .lightgbm_optimizer import PhysicsConstrainedLightGBM, LightGBMHyperOptimizer, ModelEvaluator

class EnsembleTrainer:
    """
    集成学习训练器
    实现方案3的完整集成学习架构
    """
    
    def __init__(self, wind_speed_column: str = 'wind_speed_80m'):
        """
        初始化集成学习训练器
        
        Args:
            wind_speed_column: 主要风速列名
        """
        self.wind_speed_column = wind_speed_column
        
        # 各种集成模型
        self.expert_ensemble = None
        self.dual_track = None
        self.progressive_model = None
        self.dynamic_ensemble = None
        
        # 基线模型 (用于对比)
        self.baseline_model = None
        
        # 性能对比结果
        self.performance_comparison = {}
        
        # 最佳模型
        self.best_model = None
        self.best_model_name = None
        
    def train_all_models(self, X: pd.DataFrame, y: np.ndarray,
                        X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None) -> Dict:
        """
        训练所有集成模型
        """
        print("=" * 60)
        print("开始训练集成学习架构 - 方案3")
        print("=" * 60)
        
        # 确保有验证集
        if X_val is None or y_val is None:
            print("自动划分验证集...")
            split_idx = int(len(X) * 0.8)
            X_val = X.iloc[split_idx:]
            y_val = y[split_idx:]
            X = X.iloc[:split_idx]
            y = y[:split_idx]
            print(f"训练集: {len(X)} 样本, 验证集: {len(X_val)} 样本")
        
        # 1. 训练基线模型
        self._train_baseline_model(X, y, X_val, y_val)
        
        # 2. 训练分段专家模型
        self._train_expert_ensemble(X, y, X_val, y_val)
        
        # 3. 训练物理-数据双轨道
        self._train_dual_track(X, y, X_val, y_val)
        
        # 4. 训练残差递进模型
        self._train_progressive_model(X, y, X_val, y_val)
        
        # 5. 训练动态权重集成
        self._train_dynamic_ensemble(X, y, X_val, y_val)
        
        # 6. 性能对比和选择最佳模型
        self._compare_models(X_val, y_val)
        
        print("\n" + "=" * 60)
        print("集成学习架构训练完成!")
        print("=" * 60)
        
        return self.performance_comparison
    
    def _train_baseline_model(self, X: pd.DataFrame, y: np.ndarray,
                             X_val: pd.DataFrame, y_val: np.ndarray):
        """
        训练基线模型 (单一LightGBM)
        """
        print("\n1. 训练基线模型 (单一LightGBM)")
        print("-" * 40)
        
        try:
            # 使用超参数优化
            optimizer = LightGBMHyperOptimizer(cv_folds=3, n_trials=20, physics_constrained=True)
            optimization_results = optimizer.optimize(X, y)
            
            # 训练最终基线模型
            self.baseline_model = PhysicsConstrainedLightGBM()
            self.baseline_model.fit(X, y, X_val, y_val, **optimization_results['best_params'])
            
            # 评估基线模型
            y_pred_baseline = self.baseline_model.predict(X_val)
            baseline_evaluation = ModelEvaluator.evaluate_model(y_val, y_pred_baseline)
            
            self.performance_comparison['baseline'] = {
                'model': self.baseline_model,
                'evaluation': baseline_evaluation,
                'rmse': baseline_evaluation['overall_metrics']['rmse'],
                'description': '单一物理约束LightGBM基线模型'
            }
            
            print(f"✓ 基线模型训练完成")
            print(f"  RMSE: {baseline_evaluation['overall_metrics']['rmse']:.4f}")
            
        except Exception as e:
            print(f"✗ 基线模型训练失败: {e}")
            self.baseline_model = None
    
    def _train_expert_ensemble(self, X: pd.DataFrame, y: np.ndarray,
                              X_val: pd.DataFrame, y_val: np.ndarray):
        """
        训练分段专家模型
        """
        print("\n2. 训练分段专家模型系统")
        print("-" * 40)
        
        try:
            self.expert_ensemble = WindSpeedExpertEnsemble(self.wind_speed_column)
            self.expert_ensemble.fit(X, y, X_val, y_val)
            
            # 评估专家集成模型
            y_pred_expert = self.expert_ensemble.predict(X_val)
            expert_evaluation = ModelEvaluator.evaluate_model(y_val, y_pred_expert)
            
            self.performance_comparison['expert_ensemble'] = {
                'model': self.expert_ensemble,
                'evaluation': expert_evaluation,
                'rmse': expert_evaluation['overall_metrics']['rmse'],
                'description': '分段专家模型系统 (低/中/高风速专家)'
            }
            
            print(f"✓ 分段专家模型训练完成")
            print(f"  RMSE: {expert_evaluation['overall_metrics']['rmse']:.4f}")
            
        except Exception as e:
            print(f"✗ 分段专家模型训练失败: {e}")
            self.expert_ensemble = None
    
    def _train_dual_track(self, X: pd.DataFrame, y: np.ndarray,
                         X_val: pd.DataFrame, y_val: np.ndarray):
        """
        训练物理-数据双轨道
        """
        print("\n3. 训练物理-数据双轨道系统")
        print("-" * 40)
        
        try:
            self.dual_track = PhysicsDataDualTrack(self.wind_speed_column)
            self.dual_track.fit(X, y, X_val, y_val)
            
            # 评估双轨道模型
            y_pred_dual = self.dual_track.predict(X_val)
            dual_evaluation = ModelEvaluator.evaluate_model(y_val, y_pred_dual)
            
            self.performance_comparison['dual_track'] = {
                'model': self.dual_track,
                'evaluation': dual_evaluation,
                'rmse': dual_evaluation['overall_metrics']['rmse'],
                'description': '物理-数据双轨道架构'
            }
            
            print(f"✓ 物理-数据双轨道训练完成")
            print(f"  RMSE: {dual_evaluation['overall_metrics']['rmse']:.4f}")
            
        except Exception as e:
            print(f"✗ 物理-数据双轨道训练失败: {e}")
            self.dual_track = None
    
    def _train_progressive_model(self, X: pd.DataFrame, y: np.ndarray,
                               X_val: pd.DataFrame, y_val: np.ndarray):
        """
        训练残差递进模型
        """
        print("\n4. 训练残差递进建模系统")
        print("-" * 40)
        
        try:
            self.progressive_model = ResidualProgressiveModel(self.wind_speed_column)
            self.progressive_model.fit(X, y, X_val, y_val)
            
            # 评估递进模型
            y_pred_progressive = self.progressive_model.predict(X_val)
            progressive_evaluation = ModelEvaluator.evaluate_model(y_val, y_pred_progressive)
            
            self.performance_comparison['progressive_model'] = {
                'model': self.progressive_model,
                'evaluation': progressive_evaluation,
                'rmse': progressive_evaluation['overall_metrics']['rmse'],
                'description': '残差递进建模系统 (主趋势+残差+二次残差)'
            }
            
            print(f"✓ 残差递进模型训练完成")
            print(f"  RMSE: {progressive_evaluation['overall_metrics']['rmse']:.4f}")
            
        except Exception as e:
            print(f"✗ 残差递进模型训练失败: {e}")
            self.progressive_model = None
    
    def _train_dynamic_ensemble(self, X: pd.DataFrame, y: np.ndarray,
                              X_val: pd.DataFrame, y_val: np.ndarray):
        """
        训练动态权重集成
        """
        print("\n5. 训练动态权重集成系统")
        print("-" * 40)
        
        try:
            self.dynamic_ensemble = DynamicWeightEnsemble(self.wind_speed_column)
            
            # 添加可用的模型到集成系统
            if self.baseline_model is not None:
                self.dynamic_ensemble.add_model(self.baseline_model, 'baseline')
            
            if self.expert_ensemble is not None:
                self.dynamic_ensemble.add_model(self.expert_ensemble, 'expert_ensemble')
            
            if self.dual_track is not None:
                self.dynamic_ensemble.add_model(self.dual_track, 'dual_track')
            
            if self.progressive_model is not None:
                self.dynamic_ensemble.add_model(self.progressive_model, 'progressive_model')
            
            # 训练权重预测器
            if len(self.dynamic_ensemble.models) >= 2:
                self.dynamic_ensemble.fit_weight_predictor(X, y, X_val, y_val)
                
                # 评估动态集成模型
                y_pred_dynamic = self.dynamic_ensemble.predict(X_val)
                dynamic_evaluation = ModelEvaluator.evaluate_model(y_val, y_pred_dynamic)
                
                self.performance_comparison['dynamic_ensemble'] = {
                    'model': self.dynamic_ensemble,
                    'evaluation': dynamic_evaluation,
                    'rmse': dynamic_evaluation['overall_metrics']['rmse'],
                    'description': f'动态权重集成 ({len(self.dynamic_ensemble.models)}个子模型)'
                }
                
                print(f"✓ 动态权重集成训练完成")
                print(f"  RMSE: {dynamic_evaluation['overall_metrics']['rmse']:.4f}")
                print(f"  集成模型数: {len(self.dynamic_ensemble.models)}")
            else:
                print("✗ 可用模型不足，跳过动态权重集成")
                self.dynamic_ensemble = None
            
        except Exception as e:
            print(f"✗ 动态权重集成训练失败: {e}")
            self.dynamic_ensemble = None

    def _compare_models(self, X_val: pd.DataFrame, y_val: np.ndarray):
        """
        对比所有模型性能并选择最佳模型
        """
        print("\n6. 模型性能对比")
        print("-" * 40)

        if not self.performance_comparison:
            print("没有可用的模型进行对比")
            return

        # 按RMSE排序
        sorted_models = sorted(
            self.performance_comparison.items(),
            key=lambda x: x[1]['rmse']
        )

        print("模型性能排名 (按RMSE从低到高):")
        print("-" * 60)

        for i, (model_name, model_info) in enumerate(sorted_models, 1):
            rmse = model_info['rmse']
            description = model_info['description']

            # 计算相对于基线的改进
            if 'baseline' in self.performance_comparison:
                baseline_rmse = self.performance_comparison['baseline']['rmse']
                improvement = baseline_rmse - rmse
                improvement_pct = (improvement / baseline_rmse) * 100
                improvement_str = f" (改进: {improvement:.4f}, {improvement_pct:.1f}%)"
            else:
                improvement_str = ""

            print(f"{i}. {model_name}: RMSE={rmse:.4f}{improvement_str}")
            print(f"   描述: {description}")

            # 显示详细性能指标
            evaluation = model_info['evaluation']
            overall = evaluation['overall_metrics']
            print(f"   MAE: {overall['mae']:.4f}, R²: {overall['r2']:.4f}")
            print()

        # 选择最佳模型
        best_model_name, best_model_info = sorted_models[0]
        self.best_model = best_model_info['model']
        self.best_model_name = best_model_name

        print(f"🏆 最佳模型: {best_model_name}")
        print(f"   RMSE: {best_model_info['rmse']:.4f}")
        print(f"   描述: {best_model_info['description']}")

        # 如果最佳模型显著优于基线，显示成功信息
        if 'baseline' in self.performance_comparison:
            baseline_rmse = self.performance_comparison['baseline']['rmse']
            best_rmse = best_model_info['rmse']
            improvement = baseline_rmse - best_rmse
            improvement_pct = (improvement / baseline_rmse) * 100

            if improvement > 0:
                print(f"🎉 相比基线模型提升: {improvement:.4f} RMSE ({improvement_pct:.1f}%)")

                # 检查是否达到目标
                if best_rmse <= 10.0:
                    print(f"🎯 已达到目标RMSE ≤ 10.0!")
                else:
                    print(f"📈 距离目标RMSE=10.0还需改进: {best_rmse - 10.0:.4f}")
            else:
                print(f"⚠️  集成模型未能超越基线模型")

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        使用最佳模型进行预测
        """
        if self.best_model is None:
            raise ValueError("没有可用的最佳模型，请先训练模型")

        return self.best_model.predict(X)

    def get_model_info(self) -> Dict:
        """
        获取所有模型的详细信息
        """
        info = {
            'best_model_name': self.best_model_name,
            'performance_comparison': {},
            'model_details': {}
        }

        # 性能对比信息
        for model_name, model_info in self.performance_comparison.items():
            info['performance_comparison'][model_name] = {
                'rmse': model_info['rmse'],
                'description': model_info['description'],
                'overall_metrics': model_info['evaluation']['overall_metrics']
            }

        # 模型详细信息
        if self.expert_ensemble is not None:
            info['model_details']['expert_ensemble'] = self.expert_ensemble.get_expert_info()

        if self.dual_track is not None:
            info['model_details']['dual_track'] = self.dual_track.get_track_info()

        if self.progressive_model is not None:
            info['model_details']['progressive_model'] = self.progressive_model.get_progressive_info()

        if self.dynamic_ensemble is not None:
            info['model_details']['dynamic_ensemble'] = self.dynamic_ensemble.get_ensemble_info()

        return info

    def save_ensemble_models(self, save_dir: str = "ensemble_models") -> Dict:
        """
        保存所有集成模型
        """
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        saved_files = {}

        # 保存各个模型
        models_to_save = {
            'baseline': self.baseline_model,
            'expert_ensemble': self.expert_ensemble,
            'dual_track': self.dual_track,
            'progressive_model': self.progressive_model,
            'dynamic_ensemble': self.dynamic_ensemble
        }

        for model_name, model in models_to_save.items():
            if model is not None:
                model_file = save_path / f"{model_name}_{timestamp}.joblib"
                joblib.dump(model, model_file)
                saved_files[model_name] = str(model_file)

        # 保存最佳模型
        if self.best_model is not None:
            best_model_file = save_path / f"best_model_{timestamp}.joblib"
            joblib.dump(self.best_model, best_model_file)
            saved_files['best_model'] = str(best_model_file)

        # 保存模型信息
        info_file = save_path / f"ensemble_info_{timestamp}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(self.get_model_info(), f, ensure_ascii=False, indent=2, default=str)
        saved_files['model_info'] = str(info_file)

        # 保存性能对比
        performance_file = save_path / f"performance_comparison_{timestamp}.json"
        performance_data = {}
        for model_name, model_info in self.performance_comparison.items():
            performance_data[model_name] = {
                'rmse': model_info['rmse'],
                'description': model_info['description'],
                'evaluation': model_info['evaluation']
            }

        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, ensure_ascii=False, indent=2, default=str)
        saved_files['performance_comparison'] = str(performance_file)

        print(f"\n✓ 集成模型已保存到: {save_path}")
        print(f"  时间戳: {timestamp}")
        print(f"  保存文件数: {len(saved_files)}")

        return {
            'save_directory': str(save_path),
            'timestamp': timestamp,
            'saved_files': saved_files,
            'best_model_name': self.best_model_name
        }
