{"feature_names": ["时间", "理论功率 (MW)", "wind_speed_10m", "wind_speed_80m", "wind_direction_10m", "wind_direction_80m", "wind_gusts_10m", "temperature_2m", "apparent_temperature", "wind_speed_10m_weighted", "wind_speed_80m_weighted", "wind_direction_10m_weighted", "wind_direction_80m_weighted", "wind_gusts_10m_weighted", "temperature_2m_weighted", "rain_weighted", "apparent_temperature_weighted", "wind_speed_10m_gradient_ew", "wind_speed_10m_gradient_ns", "wind_speed_10m_gradient_magnitude", "wind_speed_80m_gradient_ew", "wind_speed_80m_gradient_ns", "wind_speed_80m_gradient_magnitude", "wind_direction_10m_gradient_ew", "wind_direction_10m_gradient_ns", "wind_direction_10m_gradient_magnitude", "wind_direction_80m_gradient_ew", "wind_direction_80m_gradient_ns", "wind_direction_80m_gradient_magnitude", "wind_gusts_10m_gradient_ew", "wind_gusts_10m_gradient_ns", "wind_gusts_10m_gradient_magnitude", "temperature_2m_gradient_ew", "temperature_2m_gradient_ns", "temperature_2m_gradient_magnitude", "rain_gradient_ew", "rain_gradient_ns", "rain_gradient_magnitude", "apparent_temperature_gradient_ew", "apparent_temperature_gradient_ns", "apparent_temperature_gradient_magnitude", "wind_speed_10m_spatial_mean", "wind_speed_10m_spatial_std", "wind_speed_10m_spatial_min", "wind_speed_10m_spatial_max", "wind_speed_10m_spatial_range", "wind_speed_10m_spatial_cv", "wind_speed_80m_spatial_mean", "wind_speed_80m_spatial_std", "wind_speed_80m_spatial_min", "wind_speed_80m_spatial_max", "wind_speed_80m_spatial_range", "wind_speed_80m_spatial_cv", "wind_direction_10m_spatial_mean", "wind_direction_10m_spatial_std", "wind_direction_10m_spatial_min", "wind_direction_10m_spatial_max", "wind_direction_10m_spatial_range", "wind_direction_10m_spatial_cv", "wind_direction_80m_spatial_mean", "wind_direction_80m_spatial_std", "wind_direction_80m_spatial_min", "wind_direction_80m_spatial_max", "wind_direction_80m_spatial_range", "wind_direction_80m_spatial_cv", "wind_gusts_10m_spatial_mean", "wind_gusts_10m_spatial_std", "wind_gusts_10m_spatial_min", "wind_gusts_10m_spatial_max", "wind_gusts_10m_spatial_range", "wind_gusts_10m_spatial_cv", "temperature_2m_spatial_mean", "temperature_2m_spatial_std", "temperature_2m_spatial_min", "temperature_2m_spatial_max", "temperature_2m_spatial_range", "temperature_2m_spatial_cv", "rain_spatial_mean", "rain_spatial_std", "rain_spatial_min", "rain_spatial_max", "rain_spatial_range", "rain_spatial_cv", "apparent_temperature_spatial_mean", "apparent_temperature_spatial_std", "apparent_temperature_spatial_min", "apparent_temperature_spatial_max", "apparent_temperature_spatial_range", "apparent_temperature_spatial_cv", "wind_speed_10m_diff_c_1", "wind_speed_10m_diff_c_2", "wind_speed_10m_diff_c_3", "wind_speed_10m_diff_c_4", "wind_speed_10m_diff_1_3", "wind_speed_10m_diff_2_4", "wind_speed_10m_diff_1_2", "wind_speed_10m_diff_3_4", "wind_speed_80m_diff_c_1", "wind_speed_80m_diff_c_2", "wind_speed_80m_diff_c_3", "wind_speed_80m_diff_c_4", "wind_speed_80m_diff_1_3", "wind_speed_80m_diff_2_4", "wind_speed_80m_diff_1_2", "wind_speed_80m_diff_3_4", "wind_direction_10m_diff_c_1", "wind_direction_10m_diff_c_2", "wind_direction_10m_diff_c_3", "wind_direction_10m_diff_c_4", "wind_direction_10m_diff_1_3", "wind_direction_10m_diff_2_4", "wind_direction_10m_diff_1_2", "wind_direction_10m_diff_3_4", "wind_direction_80m_diff_c_1", "wind_direction_80m_diff_c_2", "wind_direction_80m_diff_c_3", "wind_direction_80m_diff_c_4", "wind_direction_80m_diff_1_3", "wind_direction_80m_diff_2_4", "wind_direction_80m_diff_1_2", "wind_direction_80m_diff_3_4", "wind_gusts_10m_diff_c_1", "wind_gusts_10m_diff_c_2", "wind_gusts_10m_diff_c_3", "wind_gusts_10m_diff_c_4", "wind_gusts_10m_diff_1_3", "wind_gusts_10m_diff_2_4", "wind_gusts_10m_diff_1_2", "wind_gusts_10m_diff_3_4", "temperature_2m_diff_c_1", "temperature_2m_diff_c_2", "temperature_2m_diff_c_3", "temperature_2m_diff_c_4", "temperature_2m_diff_1_3", "temperature_2m_diff_2_4", "temperature_2m_diff_1_2", "temperature_2m_diff_3_4", "rain_diff_c_1", "rain_diff_c_2", "rain_diff_c_3", "rain_diff_c_4", "rain_diff_1_3", "rain_diff_2_4", "rain_diff_1_2", "rain_diff_3_4", "apparent_temperature_diff_c_1", "apparent_temperature_diff_c_2", "apparent_temperature_diff_c_3", "apparent_temperature_diff_c_4", "apparent_temperature_diff_1_3", "apparent_temperature_diff_2_4", "apparent_temperature_diff_1_2", "apparent_temperature_diff_3_4", "wind_speed_10m_spatial", "wind_speed_80m_spatial", "wind_direction_10m_spatial", "wind_direction_80m_spatial", "wind_gusts_10m_spatial", "temperature_2m_spatial", "rain_spatial", "apparent_temperature_spatial", "wind_speed_consistency", "wind_gradient_intensity", "wind_direction_variability", "wind_direction_consistency", "temperature_gradient_strength", "temperature_uniformity", "rain_concentration", "rain_coverage", "wind_shear_vertical", "wind_shear_ratio", "temperature_stability", "gust_factor", "turbulence_intensity", "temperature_front_strength", "combined_front_strength", "wind_direction_shear", "front_probability", "overall_spatial_consistency", "spatial_heterogeneity", "wind_temperature_coupling", "minute_15", "quarter_hour", "hour", "is_peak_hour", "time_period", "day", "month", "year", "dayofweek", "dayofyear", "weekofyear", "season", "is_weekend", "hour_sin", "hour_cos", "dayofyear_sin", "dayofyear_cos", "dayofweek_sin", "dayofweek_cos", "month_sin", "month_cos", "wind_speed_10m_cubed", "wind_speed_80m_cubed", "wind_speed_gradient", "wind_speed_ratio", "wind_shear_exponent", "effective_wind_10m", "effective_wind_80m", "wind_speed_category_10m", "wind_speed_category_80m", "wind_dir_10m_sin", "wind_dir_10m_cos", "wind_dir_80m_sin", "wind_dir_80m_cos", "wind_direction_difference", "wind_sector_10m", "wind_sector_80m", "temperature_difference", "estimated_air_density", "density_corrected_power_10m", "density_corrected_power_80m", "temperature_efficiency_factor", "theoretical_power_ratio", "power_curve_region", "distance_to_rated_speed", "wind_stability", "wind_resource_index", "generation_suitability", "power_expectation", "wind_speed_80m_anomaly_flag", "wind_speed_10m_anomaly_flag", "temperature_2m_anomaly_flag", "rain_anomaly_flag", "time_gap_flag", "wind_speed_80m_smoothed", "wind_speed_10m_smoothed", "temperature_2m_smoothed", "weibull_shape", "weibull_scale", "weibull_mean_wind", "weibull_wind_variance", "wind_weibull_percentile", "weibull_power_density", "reynolds_number", "reynolds_lift_drag_ratio", "reynolds_power_coefficient", "reynolds_criticality", "reynolds_stability", "tip_speed_ratio", "tsr_deviation", "tsr_efficiency", "tsr_power_coefficient", "tsr_stability", "wake_velocity_deficit", "effective_wind_speed", "wake_directional_effect", "dynamic_pressure", "power_density", "swept_area_power", "betz_limit_power", "wind_shear_power_coefficient", "attack_angle_effect", "stall_probability", "friction_velocity", "monin_obukhov_length", "atmospheric_stability", "turbulent_kinetic_energy", "boundary_layer_height", "height_in_boundary_layer", "daily_cycle_sin", "daily_cycle_cos", "weekly_cycle_sin", "weekly_cycle_cos", "monthly_cycle_sin", "monthly_cycle_cos", "yearly_cycle_sin", "yearly_cycle_cos", "daily_weekly_interaction", "daily_monthly_interaction", "weekly_monthly_interaction", "cycle_intensity", "cycle_phase", "temp_gradient_1h", "temp_gradient_3h", "temp_gradient_6h", "temp_change_rate", "temp_front_intensity", "cold_front_indicator", "warm_front_indicator", "wind_dir_change_1h", "wind_dir_change_3h", "wind_dir_change_6h", "wind_dir_change_intensity", "wind_dir_front", "weather_front_strength", "weather_front_detected", "is_high_wind", "high_wind_duration", "is_low_wind", "low_wind_duration", "wind_stability_1h", "wind_stability_3h", "wind_stability_6h", "wind_trend_1h", "wind_trend_3h", "wind_range_1h", "wind_range_3h", "wind_persistence_index", "weather_change_intensity", "weather_transition_point", "weather_pattern", "stable_period_length", "dominant_frequency", "spectral_energy", "spectral_entropy", "frequency_category", "spectral_stability", "trend_component", "seasonal_component", "residual_component", "trend_strength", "seasonal_strength", "residual_strength", "trend_stability", "seasonal_stability", "decomposition_quality", "wind_instantaneous_phase", "wind_instantaneous_amplitude", "wind_instantaneous_frequency", "wind_phase_derivative", "wind_phase_stability", "wind_speed_lag_1", "wind_speed_lead_1", "wind_speed_lag_2", "wind_speed_lead_2", "wind_speed_lag_4", "wind_speed_lead_4", "wind_speed_lag_8", "wind_speed_lead_8", "wind_speed_cumulative_change_1h", "wind_speed_cumulative_change_3h", "wind_velocity", "wind_acceleration", "wind_jerk", "wind_momentum", "wind_kinetic_energy_change", "predicted_wind_80m_from_profile", "wind_profile_prediction_error", "turbulence_scale", "energy_dissipation_rate", "integral_length_scale", "turbulence_intermittency", "estimated_temp_80m", "temperature_gradient", "thermal_effect_strength", "thermal_stratification_stability", "wind_speed_sqrt", "wind_speed_cbrt", "wind_speed_log", "wind_speed_exp", "wind_speed_low_regime", "wind_speed_mid_regime", "wind_speed_high_regime", "wind_speed_sin", "wind_speed_cos", "wind_lyapunov_approx", "wind_fractal_dimension", "wind_complexity", "wind_speed_80m_temperature_2m_product", "wind_speed_80m_temperature_2m_ratio", "wind_speed_80m_temperature_2m_diff", "wind_speed_80m_temperature_2m_correlation", "wind_speed_80m_rain_ratio", "wind_speed_80m_rain_diff", "temperature_2m_rain_ratio", "temperature_2m_rain_diff", "wind_speed_80m_temperature_2m_rain_weighted", "multivariate_pc1", "multivariate_pc2", "multi_wind_10m_mean", "multi_wind_10m_std", "multi_wind_10m_median", "multi_wind_10m_max", "multi_wind_10m_min", "multi_wind_10m_range", "multi_wind_10m_q25", "multi_wind_10m_q75", "multi_wind_10m_iqr", "multi_wind_10m_agreement", "multi_wind_10m_cv", "multi_wind_10m_confidence", "multi_wind_80m_mean", "multi_wind_80m_std", "multi_wind_80m_median", "multi_wind_80m_max", "multi_wind_80m_min", "multi_wind_80m_range", "multi_wind_80m_q25", "multi_wind_80m_q75", "multi_wind_80m_iqr", "multi_wind_80m_agreement", "multi_wind_80m_cv", "multi_wind_80m_confidence", "multi_gusts_10m_mean", "multi_gusts_10m_std", "multi_gusts_10m_median", "multi_gusts_10m_max", "multi_gusts_10m_min", "multi_gusts_10m_range", "multi_gusts_10m_q25", "multi_gusts_10m_q75", "multi_gusts_10m_iqr", "multi_gusts_10m_agreement", "multi_gusts_10m_cv", "multi_gusts_10m_confidence", "weighted_wind_10m", "weighted_wind_80m", "weighted_gusts_10m", "multi_power_density", "multi_wind_in_range", "multi_rated_proximity", "multi_generation_suitability", "multi_wind_shear", "multi_wind_gradient", "temporal_wind_10m_stability", "temporal_wind_10m_trend_agreement", "temporal_wind_10m_avg_correlation", "temporal_wind_80m_stability", "temporal_wind_80m_trend_agreement", "temporal_wind_80m_avg_correlation", "temporal_gusts_10m_stability", "temporal_gusts_10m_trend_agreement", "temporal_gusts_10m_avg_correlation", "gfs_wind_10m_mean", "jma_wind_10m_mean", "cma_wind_10m_mean", "icon_wind_10m_mean", "gem_wind_10m_mean", "meteofrance_wind_10m_mean", "ukmo_wind_10m_mean", "gfs_wind_80m_mean", "cma_wind_80m_mean", "icon_wind_80m_mean", "gem_wind_80m_mean", "meteofrance_wind_80m_mean", "gfs_gusts_10m_mean", "cma_gusts_10m_mean", "icon_gusts_10m_mean", "gem_gusts_10m_mean", "meteofrance_gusts_10m_mean", "ukmo_gusts_10m_mean", "huarui_theoretical_power", "goldwind_theoretical_power", "total_theoretical_power", "huarui_power_coefficient", "goldwind_power_coefficient", "weighted_power_coefficient", "huarui_thrust_coefficient", "goldwind_thrust_coefficient", "weighted_thrust_coefficient", "air_density_corrected", "density_corrected_theoretical_power", "wind_speed_effectiveness", "turbulence_power_loss", "turbulence_corrected_power", "minute", "high_error_period", "low_error_period", "time_dependency_factor", "night_evening_factor", "bias_corrected_theoretical_power", "power_range_correction", "range_corrected_theoretical_power", "distribution_skew_correction", "final_corrected_theoretical_power", "turbine_power_difference", "turbine_power_ratio", "power_coefficient_difference", "thrust_coefficient_difference", "huarui_advantage", "goldwind_advantage", "combined_efficiency", "wind_speed_80m_lag_1", "wind_speed_80m_lag_diff_1", "wind_speed_80m_lag_ratio_1", "wind_speed_80m_lag_2", "wind_speed_80m_lag_diff_2", "wind_speed_80m_lag_ratio_2", "wind_speed_80m_lag_3", "wind_speed_80m_lag_diff_3", "wind_speed_80m_lag_ratio_3", "wind_speed_80m_lag_4", "wind_speed_80m_lag_diff_4", "wind_speed_80m_lag_ratio_4", "wind_speed_80m_lag_6", "wind_speed_80m_lag_diff_6", "wind_speed_80m_lag_ratio_6", "wind_speed_80m_lag_8", "wind_speed_80m_lag_diff_8", "wind_speed_80m_lag_ratio_8", "wind_speed_80m_lag_12", "wind_speed_80m_lag_diff_12", "wind_speed_80m_lag_ratio_12", "wind_speed_80m_lag_24", "wind_speed_80m_lag_diff_24", "wind_speed_80m_lag_ratio_24", "wind_speed_10m_lag_1", "wind_speed_10m_lag_diff_1", "wind_speed_10m_lag_ratio_1", "wind_speed_10m_lag_2", "wind_speed_10m_lag_diff_2", "wind_speed_10m_lag_ratio_2", "wind_speed_10m_lag_3", "wind_speed_10m_lag_diff_3", "wind_speed_10m_lag_ratio_3", "wind_speed_10m_lag_4", "wind_speed_10m_lag_diff_4", "wind_speed_10m_lag_ratio_4", "wind_speed_10m_lag_6", "wind_speed_10m_lag_diff_6", "wind_speed_10m_lag_ratio_6", "wind_speed_10m_lag_8", "wind_speed_10m_lag_diff_8", "wind_speed_10m_lag_ratio_8", "wind_speed_10m_lag_12", "wind_speed_10m_lag_diff_12", "wind_speed_10m_lag_ratio_12", "wind_speed_10m_lag_24", "wind_speed_10m_lag_diff_24", "wind_speed_10m_lag_ratio_24", "temperature_2m_lag_1", "temperature_2m_lag_diff_1", "temperature_2m_lag_ratio_1", "temperature_2m_lag_2", "temperature_2m_lag_diff_2", "temperature_2m_lag_ratio_2", "temperature_2m_lag_3", "temperature_2m_lag_diff_3", "temperature_2m_lag_ratio_3", "temperature_2m_lag_4", "temperature_2m_lag_diff_4", "temperature_2m_lag_ratio_4", "temperature_2m_lag_6", "temperature_2m_lag_diff_6", "temperature_2m_lag_ratio_6", "temperature_2m_lag_8", "temperature_2m_lag_diff_8", "temperature_2m_lag_ratio_8", "temperature_2m_lag_12", "temperature_2m_lag_diff_12", "temperature_2m_lag_ratio_12", "temperature_2m_lag_24", "temperature_2m_lag_diff_24", "temperature_2m_lag_ratio_24", "day_of_week", "day_of_year", "is_high_error_hour", "is_low_error_hour", "hour_error_intensity", "is_night", "is_evening", "is_dawn", "minute_sin", "minute_cos", "time_error_weight", "wind_speed_80m_ma_3", "wind_speed_80m_std_3", "wind_speed_80m_ma_dev_3", "wind_speed_80m_ma_dev_norm_3", "wind_speed_80m_ma_6", "wind_speed_80m_std_6", "wind_speed_80m_ma_dev_6", "wind_speed_80m_ma_dev_norm_6", "wind_speed_80m_ma_12", "wind_speed_80m_std_12", "wind_speed_80m_ma_dev_12", "wind_speed_80m_ma_dev_norm_12", "wind_speed_80m_ma_24", "wind_speed_80m_std_24", "wind_speed_80m_ma_dev_24", "wind_speed_80m_ma_dev_norm_24", "wind_speed_10m_ma_3", "wind_speed_10m_std_3", "wind_speed_10m_ma_dev_3", "wind_speed_10m_ma_dev_norm_3", "wind_speed_10m_ma_6", "wind_speed_10m_std_6", "wind_speed_10m_ma_dev_6", "wind_speed_10m_ma_dev_norm_6", "wind_speed_10m_ma_12", "wind_speed_10m_std_12", "wind_speed_10m_ma_dev_12", "wind_speed_10m_ma_dev_norm_12", "wind_speed_10m_ma_24", "wind_speed_10m_std_24", "wind_speed_10m_ma_dev_24", "wind_speed_10m_ma_dev_norm_24", "temperature_2m_ma_3", "temperature_2m_std_3", "temperature_2m_ma_dev_3", "temperature_2m_ma_dev_norm_3", "temperature_2m_ma_6", "temperature_2m_std_6", "temperature_2m_ma_dev_6", "temperature_2m_ma_dev_norm_6", "temperature_2m_ma_12", "temperature_2m_std_12", "temperature_2m_ma_dev_12", "temperature_2m_ma_dev_norm_12", "temperature_2m_ma_24", "temperature_2m_std_24", "temperature_2m_ma_dev_24", "temperature_2m_ma_dev_norm_24", "wind_speed_80m_detrended", "wind_speed_10m_detrended", "temperature_2m_detrended", "wind_speed_80m_change_rate", "wind_speed_80m_stability", "wind_speed_80m_persistence", "wind_speed_10m_change_rate", "wind_speed_10m_stability", "wind_speed_10m_persistence", "consecutive_error_risk", "wind_gradient_stability", "quarter_hour_cycle", "hour_cycle_error_adjusted", "daily_cycle", "weekly_cycle", "seasonal_cycle", "hour_minute_interaction", "daily_seasonal_interaction", "error_adjusted_daily_cycle"], "feature_count": 633, "numeric_features": ["理论功率 (MW)", "wind_speed_10m", "wind_speed_80m", "wind_direction_10m", "wind_direction_80m", "wind_gusts_10m", "temperature_2m", "apparent_temperature", "wind_speed_10m_weighted", "wind_speed_80m_weighted", "wind_direction_10m_weighted", "wind_direction_80m_weighted", "wind_gusts_10m_weighted", "temperature_2m_weighted", "rain_weighted", "apparent_temperature_weighted", "wind_speed_10m_gradient_ew", "wind_speed_10m_gradient_ns", "wind_speed_10m_gradient_magnitude", "wind_speed_80m_gradient_ew", "wind_speed_80m_gradient_ns", "wind_speed_80m_gradient_magnitude", "wind_direction_10m_gradient_ew", "wind_direction_10m_gradient_ns", "wind_direction_10m_gradient_magnitude", "wind_direction_80m_gradient_ew", "wind_direction_80m_gradient_ns", "wind_direction_80m_gradient_magnitude", "wind_gusts_10m_gradient_ew", "wind_gusts_10m_gradient_ns", "wind_gusts_10m_gradient_magnitude", "temperature_2m_gradient_ew", "temperature_2m_gradient_ns", "temperature_2m_gradient_magnitude", "rain_gradient_ew", "rain_gradient_ns", "rain_gradient_magnitude", "apparent_temperature_gradient_ew", "apparent_temperature_gradient_ns", "apparent_temperature_gradient_magnitude", "wind_speed_10m_spatial_mean", "wind_speed_10m_spatial_std", "wind_speed_10m_spatial_min", "wind_speed_10m_spatial_max", "wind_speed_10m_spatial_range", "wind_speed_10m_spatial_cv", "wind_speed_80m_spatial_mean", "wind_speed_80m_spatial_std", "wind_speed_80m_spatial_min", "wind_speed_80m_spatial_max", "wind_speed_80m_spatial_range", "wind_speed_80m_spatial_cv", "wind_direction_10m_spatial_mean", "wind_direction_10m_spatial_std", "wind_direction_10m_spatial_min", "wind_direction_10m_spatial_max", "wind_direction_10m_spatial_range", "wind_direction_10m_spatial_cv", "wind_direction_80m_spatial_mean", "wind_direction_80m_spatial_std", "wind_direction_80m_spatial_min", "wind_direction_80m_spatial_max", "wind_direction_80m_spatial_range", "wind_direction_80m_spatial_cv", "wind_gusts_10m_spatial_mean", "wind_gusts_10m_spatial_std", "wind_gusts_10m_spatial_min", "wind_gusts_10m_spatial_max", "wind_gusts_10m_spatial_range", "wind_gusts_10m_spatial_cv", "temperature_2m_spatial_mean", "temperature_2m_spatial_std", "temperature_2m_spatial_min", "temperature_2m_spatial_max", "temperature_2m_spatial_range", "temperature_2m_spatial_cv", "rain_spatial_mean", "rain_spatial_std", "rain_spatial_min", "rain_spatial_max", "rain_spatial_range", "rain_spatial_cv", "apparent_temperature_spatial_mean", "apparent_temperature_spatial_std", "apparent_temperature_spatial_min", "apparent_temperature_spatial_max", "apparent_temperature_spatial_range", "apparent_temperature_spatial_cv", "wind_speed_10m_diff_c_1", "wind_speed_10m_diff_c_2", "wind_speed_10m_diff_c_3", "wind_speed_10m_diff_c_4", "wind_speed_10m_diff_1_3", "wind_speed_10m_diff_2_4", "wind_speed_10m_diff_1_2", "wind_speed_10m_diff_3_4", "wind_speed_80m_diff_c_1", "wind_speed_80m_diff_c_2", "wind_speed_80m_diff_c_3", "wind_speed_80m_diff_c_4", "wind_speed_80m_diff_1_3", "wind_speed_80m_diff_2_4", "wind_speed_80m_diff_1_2", "wind_speed_80m_diff_3_4", "wind_direction_10m_diff_c_1", "wind_direction_10m_diff_c_2", "wind_direction_10m_diff_c_3", "wind_direction_10m_diff_c_4", "wind_direction_10m_diff_1_3", "wind_direction_10m_diff_2_4", "wind_direction_10m_diff_1_2", "wind_direction_10m_diff_3_4", "wind_direction_80m_diff_c_1", "wind_direction_80m_diff_c_2", "wind_direction_80m_diff_c_3", "wind_direction_80m_diff_c_4", "wind_direction_80m_diff_1_3", "wind_direction_80m_diff_2_4", "wind_direction_80m_diff_1_2", "wind_direction_80m_diff_3_4", "wind_gusts_10m_diff_c_1", "wind_gusts_10m_diff_c_2", "wind_gusts_10m_diff_c_3", "wind_gusts_10m_diff_c_4", "wind_gusts_10m_diff_1_3", "wind_gusts_10m_diff_2_4", "wind_gusts_10m_diff_1_2", "wind_gusts_10m_diff_3_4", "temperature_2m_diff_c_1", "temperature_2m_diff_c_2", "temperature_2m_diff_c_3", "temperature_2m_diff_c_4", "temperature_2m_diff_1_3", "temperature_2m_diff_2_4", "temperature_2m_diff_1_2", "temperature_2m_diff_3_4", "rain_diff_c_1", "rain_diff_c_2", "rain_diff_c_3", "rain_diff_c_4", "rain_diff_1_3", "rain_diff_2_4", "rain_diff_1_2", "rain_diff_3_4", "apparent_temperature_diff_c_1", "apparent_temperature_diff_c_2", "apparent_temperature_diff_c_3", "apparent_temperature_diff_c_4", "apparent_temperature_diff_1_3", "apparent_temperature_diff_2_4", "apparent_temperature_diff_1_2", "apparent_temperature_diff_3_4", "wind_speed_10m_spatial", "wind_speed_80m_spatial", "wind_direction_10m_spatial", "wind_direction_80m_spatial", "wind_gusts_10m_spatial", "temperature_2m_spatial", "rain_spatial", "apparent_temperature_spatial", "wind_speed_consistency", "wind_gradient_intensity", "wind_direction_variability", "wind_direction_consistency", "temperature_gradient_strength", "temperature_uniformity", "rain_concentration", "rain_coverage", "wind_shear_vertical", "wind_shear_ratio", "temperature_stability", "gust_factor", "turbulence_intensity", "temperature_front_strength", "combined_front_strength", "wind_direction_shear", "front_probability", "overall_spatial_consistency", "spatial_heterogeneity", "wind_temperature_coupling", "minute_15", "quarter_hour", "hour", "is_peak_hour", "day", "month", "year", "dayofweek", "dayofyear", "weekofyear", "is_weekend", "hour_sin", "hour_cos", "dayofyear_sin", "dayofyear_cos", "dayofweek_sin", "dayofweek_cos", "month_sin", "month_cos", "wind_speed_10m_cubed", "wind_speed_80m_cubed", "wind_speed_gradient", "wind_speed_ratio", "wind_shear_exponent", "effective_wind_10m", "effective_wind_80m", "wind_dir_10m_sin", "wind_dir_10m_cos", "wind_dir_80m_sin", "wind_dir_80m_cos", "wind_direction_difference", "temperature_difference", "estimated_air_density", "density_corrected_power_10m", "density_corrected_power_80m", "temperature_efficiency_factor", "theoretical_power_ratio", "distance_to_rated_speed", "wind_resource_index", "generation_suitability", "power_expectation", "wind_speed_80m_anomaly_flag", "wind_speed_10m_anomaly_flag", "temperature_2m_anomaly_flag", "rain_anomaly_flag", "time_gap_flag", "wind_speed_80m_smoothed", "wind_speed_10m_smoothed", "temperature_2m_smoothed", "weibull_shape", "weibull_scale", "weibull_mean_wind", "weibull_wind_variance", "wind_weibull_percentile", "weibull_power_density", "reynolds_number", "reynolds_lift_drag_ratio", "reynolds_power_coefficient", "reynolds_criticality", "reynolds_stability", "tip_speed_ratio", "tsr_deviation", "tsr_efficiency", "tsr_power_coefficient", "tsr_stability", "wake_velocity_deficit", "effective_wind_speed", "wake_directional_effect", "dynamic_pressure", "power_density", "swept_area_power", "betz_limit_power", "wind_shear_power_coefficient", "attack_angle_effect", "stall_probability", "friction_velocity", "monin_obukhov_length", "atmospheric_stability", "turbulent_kinetic_energy", "boundary_layer_height", "height_in_boundary_layer", "daily_cycle_sin", "daily_cycle_cos", "weekly_cycle_sin", "weekly_cycle_cos", "monthly_cycle_sin", "monthly_cycle_cos", "yearly_cycle_sin", "yearly_cycle_cos", "daily_weekly_interaction", "daily_monthly_interaction", "weekly_monthly_interaction", "cycle_intensity", "cycle_phase", "temp_gradient_1h", "temp_gradient_3h", "temp_gradient_6h", "temp_change_rate", "temp_front_intensity", "cold_front_indicator", "warm_front_indicator", "wind_dir_change_1h", "wind_dir_change_3h", "wind_dir_change_6h", "wind_dir_change_intensity", "wind_dir_front", "weather_front_strength", "weather_front_detected", "is_high_wind", "high_wind_duration", "is_low_wind", "low_wind_duration", "wind_stability_1h", "wind_stability_3h", "wind_stability_6h", "wind_trend_1h", "wind_trend_3h", "wind_range_1h", "wind_range_3h", "wind_persistence_index", "weather_change_intensity", "weather_transition_point", "weather_pattern", "stable_period_length", "dominant_frequency", "spectral_energy", "spectral_entropy", "frequency_category", "spectral_stability", "trend_component", "seasonal_component", "residual_component", "trend_strength", "seasonal_strength", "residual_strength", "trend_stability", "seasonal_stability", "decomposition_quality", "wind_instantaneous_phase", "wind_instantaneous_amplitude", "wind_instantaneous_frequency", "wind_phase_derivative", "wind_phase_stability", "wind_speed_lag_1", "wind_speed_lead_1", "wind_speed_lag_2", "wind_speed_lead_2", "wind_speed_lag_4", "wind_speed_lead_4", "wind_speed_lag_8", "wind_speed_lead_8", "wind_speed_cumulative_change_1h", "wind_speed_cumulative_change_3h", "wind_velocity", "wind_acceleration", "wind_jerk", "wind_momentum", "wind_kinetic_energy_change", "predicted_wind_80m_from_profile", "wind_profile_prediction_error", "turbulence_scale", "energy_dissipation_rate", "integral_length_scale", "turbulence_intermittency", "estimated_temp_80m", "temperature_gradient", "thermal_effect_strength", "thermal_stratification_stability", "wind_speed_sqrt", "wind_speed_cbrt", "wind_speed_log", "wind_speed_exp", "wind_speed_low_regime", "wind_speed_mid_regime", "wind_speed_high_regime", "wind_speed_sin", "wind_speed_cos", "wind_lyapunov_approx", "wind_fractal_dimension", "wind_complexity", "wind_speed_80m_temperature_2m_product", "wind_speed_80m_temperature_2m_ratio", "wind_speed_80m_temperature_2m_diff", "wind_speed_80m_temperature_2m_correlation", "wind_speed_80m_rain_ratio", "wind_speed_80m_rain_diff", "temperature_2m_rain_ratio", "temperature_2m_rain_diff", "wind_speed_80m_temperature_2m_rain_weighted", "multivariate_pc1", "multivariate_pc2", "multi_wind_10m_mean", "multi_wind_10m_std", "multi_wind_10m_median", "multi_wind_10m_max", "multi_wind_10m_min", "multi_wind_10m_range", "multi_wind_10m_q25", "multi_wind_10m_q75", "multi_wind_10m_iqr", "multi_wind_10m_agreement", "multi_wind_10m_cv", "multi_wind_10m_confidence", "multi_wind_80m_mean", "multi_wind_80m_std", "multi_wind_80m_median", "multi_wind_80m_max", "multi_wind_80m_min", "multi_wind_80m_range", "multi_wind_80m_q25", "multi_wind_80m_q75", "multi_wind_80m_iqr", "multi_wind_80m_agreement", "multi_wind_80m_cv", "multi_wind_80m_confidence", "multi_gusts_10m_mean", "multi_gusts_10m_std", "multi_gusts_10m_median", "multi_gusts_10m_max", "multi_gusts_10m_min", "multi_gusts_10m_range", "multi_gusts_10m_q25", "multi_gusts_10m_q75", "multi_gusts_10m_iqr", "multi_gusts_10m_agreement", "multi_gusts_10m_cv", "multi_gusts_10m_confidence", "weighted_wind_10m", "weighted_wind_80m", "weighted_gusts_10m", "multi_power_density", "multi_wind_in_range", "multi_rated_proximity", "multi_generation_suitability", "multi_wind_shear", "multi_wind_gradient", "temporal_wind_10m_stability", "temporal_wind_10m_trend_agreement", "temporal_wind_10m_avg_correlation", "temporal_wind_80m_stability", "temporal_wind_80m_trend_agreement", "temporal_wind_80m_avg_correlation", "temporal_gusts_10m_stability", "temporal_gusts_10m_trend_agreement", "temporal_gusts_10m_avg_correlation", "gfs_wind_10m_mean", "jma_wind_10m_mean", "cma_wind_10m_mean", "icon_wind_10m_mean", "gem_wind_10m_mean", "meteofrance_wind_10m_mean", "ukmo_wind_10m_mean", "gfs_wind_80m_mean", "cma_wind_80m_mean", "icon_wind_80m_mean", "gem_wind_80m_mean", "meteofrance_wind_80m_mean", "gfs_gusts_10m_mean", "cma_gusts_10m_mean", "icon_gusts_10m_mean", "gem_gusts_10m_mean", "meteofrance_gusts_10m_mean", "ukmo_gusts_10m_mean", "huarui_theoretical_power", "goldwind_theoretical_power", "total_theoretical_power", "huarui_power_coefficient", "goldwind_power_coefficient", "weighted_power_coefficient", "huarui_thrust_coefficient", "goldwind_thrust_coefficient", "weighted_thrust_coefficient", "air_density_corrected", "density_corrected_theoretical_power", "wind_speed_effectiveness", "turbulence_power_loss", "turbulence_corrected_power", "minute", "high_error_period", "low_error_period", "time_dependency_factor", "night_evening_factor", "bias_corrected_theoretical_power", "power_range_correction", "range_corrected_theoretical_power", "distribution_skew_correction", "final_corrected_theoretical_power", "turbine_power_difference", "turbine_power_ratio", "power_coefficient_difference", "thrust_coefficient_difference", "huarui_advantage", "goldwind_advantage", "combined_efficiency", "wind_speed_80m_lag_1", "wind_speed_80m_lag_diff_1", "wind_speed_80m_lag_ratio_1", "wind_speed_80m_lag_2", "wind_speed_80m_lag_diff_2", "wind_speed_80m_lag_ratio_2", "wind_speed_80m_lag_3", "wind_speed_80m_lag_diff_3", "wind_speed_80m_lag_ratio_3", "wind_speed_80m_lag_4", "wind_speed_80m_lag_diff_4", "wind_speed_80m_lag_ratio_4", "wind_speed_80m_lag_6", "wind_speed_80m_lag_diff_6", "wind_speed_80m_lag_ratio_6", "wind_speed_80m_lag_8", "wind_speed_80m_lag_diff_8", "wind_speed_80m_lag_ratio_8", "wind_speed_80m_lag_12", "wind_speed_80m_lag_diff_12", "wind_speed_80m_lag_ratio_12", "wind_speed_80m_lag_24", "wind_speed_80m_lag_diff_24", "wind_speed_80m_lag_ratio_24", "wind_speed_10m_lag_1", "wind_speed_10m_lag_diff_1", "wind_speed_10m_lag_ratio_1", "wind_speed_10m_lag_2", "wind_speed_10m_lag_diff_2", "wind_speed_10m_lag_ratio_2", "wind_speed_10m_lag_3", "wind_speed_10m_lag_diff_3", "wind_speed_10m_lag_ratio_3", "wind_speed_10m_lag_4", "wind_speed_10m_lag_diff_4", "wind_speed_10m_lag_ratio_4", "wind_speed_10m_lag_6", "wind_speed_10m_lag_diff_6", "wind_speed_10m_lag_ratio_6", "wind_speed_10m_lag_8", "wind_speed_10m_lag_diff_8", "wind_speed_10m_lag_ratio_8", "wind_speed_10m_lag_12", "wind_speed_10m_lag_diff_12", "wind_speed_10m_lag_ratio_12", "wind_speed_10m_lag_24", "wind_speed_10m_lag_diff_24", "wind_speed_10m_lag_ratio_24", "temperature_2m_lag_1", "temperature_2m_lag_diff_1", "temperature_2m_lag_ratio_1", "temperature_2m_lag_2", "temperature_2m_lag_diff_2", "temperature_2m_lag_ratio_2", "temperature_2m_lag_3", "temperature_2m_lag_diff_3", "temperature_2m_lag_ratio_3", "temperature_2m_lag_4", "temperature_2m_lag_diff_4", "temperature_2m_lag_ratio_4", "temperature_2m_lag_6", "temperature_2m_lag_diff_6", "temperature_2m_lag_ratio_6", "temperature_2m_lag_8", "temperature_2m_lag_diff_8", "temperature_2m_lag_ratio_8", "temperature_2m_lag_12", "temperature_2m_lag_diff_12", "temperature_2m_lag_ratio_12", "temperature_2m_lag_24", "temperature_2m_lag_diff_24", "temperature_2m_lag_ratio_24", "day_of_week", "day_of_year", "is_high_error_hour", "is_low_error_hour", "hour_error_intensity", "is_night", "is_evening", "is_dawn", "minute_sin", "minute_cos", "time_error_weight", "wind_speed_80m_ma_3", "wind_speed_80m_std_3", "wind_speed_80m_ma_dev_3", "wind_speed_80m_ma_dev_norm_3", "wind_speed_80m_ma_6", "wind_speed_80m_std_6", "wind_speed_80m_ma_dev_6", "wind_speed_80m_ma_dev_norm_6", "wind_speed_80m_ma_12", "wind_speed_80m_std_12", "wind_speed_80m_ma_dev_12", "wind_speed_80m_ma_dev_norm_12", "wind_speed_80m_ma_24", "wind_speed_80m_std_24", "wind_speed_80m_ma_dev_24", "wind_speed_80m_ma_dev_norm_24", "wind_speed_10m_ma_3", "wind_speed_10m_std_3", "wind_speed_10m_ma_dev_3", "wind_speed_10m_ma_dev_norm_3", "wind_speed_10m_ma_6", "wind_speed_10m_std_6", "wind_speed_10m_ma_dev_6", "wind_speed_10m_ma_dev_norm_6", "wind_speed_10m_ma_12", "wind_speed_10m_std_12", "wind_speed_10m_ma_dev_12", "wind_speed_10m_ma_dev_norm_12", "wind_speed_10m_ma_24", "wind_speed_10m_std_24", "wind_speed_10m_ma_dev_24", "wind_speed_10m_ma_dev_norm_24", "temperature_2m_ma_3", "temperature_2m_std_3", "temperature_2m_ma_dev_3", "temperature_2m_ma_dev_norm_3", "temperature_2m_ma_6", "temperature_2m_std_6", "temperature_2m_ma_dev_6", "temperature_2m_ma_dev_norm_6", "temperature_2m_ma_12", "temperature_2m_std_12", "temperature_2m_ma_dev_12", "temperature_2m_ma_dev_norm_12", "temperature_2m_ma_24", "temperature_2m_std_24", "temperature_2m_ma_dev_24", "temperature_2m_ma_dev_norm_24", "wind_speed_80m_detrended", "wind_speed_10m_detrended", "temperature_2m_detrended", "wind_speed_80m_change_rate", "wind_speed_80m_stability", "wind_speed_80m_persistence", "wind_speed_10m_change_rate", "wind_speed_10m_stability", "wind_speed_10m_persistence", "consecutive_error_risk", "wind_gradient_stability", "quarter_hour_cycle", "hour_cycle_error_adjusted", "daily_cycle", "weekly_cycle", "seasonal_cycle", "hour_minute_interaction", "daily_seasonal_interaction", "error_adjusted_daily_cycle"], "time_column": "时间", "feature_groups": {"physics_core_turbine_features": ["total_theoretical_power", "final_corrected_theoretical_power", "weighted_power_coefficient", "weighted_thrust_coefficient"], "physics_bias_correction_features": ["systematic_bias_correction", "power_range_correction", "distribution_skew_correction"], "physics_error_pattern_features": ["high_error_period", "low_error_period", "time_dependency_factor", "night_evening_factor"], "physics_turbine_difference_features": ["turbine_power_difference", "power_coefficient_difference", "combined_efficiency"], "physics_physics_validation_features": ["air_density_corrected", "wind_speed_effectiveness", "turbulence_power_loss"], "temporal_core_temporal_features": ["hour_error_intensity", "time_error_weight", "autocorr_strength", "consecutive_error_risk"], "temporal_autocorr_features": ["wind_speed_80m_lag_1", "wind_speed_80m_lag_2", "wind_speed_80m_lag_4", "autocorr_weight_1"], "temporal_time_pattern_features": ["is_high_error_hour", "is_low_error_hour", "is_night", "is_evening"], "temporal_decorrelation_features": ["wind_speed_80m_ma_6", "wind_speed_80m_ma_dev_12", "wind_speed_80m_detrended"], "temporal_stability_features": ["wind_speed_80m_stability", "wind_speed_consistency", "wind_gradient_stability"], "temporal_cyclical_features": ["hour_sin", "hour_cos", "hour_cycle_error_adjusted", "error_adjusted_daily_cycle"]}, "config_snapshot": {"enable_turbine_features": true, "enable_physics_constraints": true, "enable_temporal_enhancement": true, "error_correction_features": true}}