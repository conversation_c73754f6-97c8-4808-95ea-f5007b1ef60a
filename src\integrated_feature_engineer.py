"""
集成特征工程管道
整合风机参数、物理约束和时序增强特征
确保训练和预测特征一致性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
import json
from pathlib import Path

from .turbine_parameters import TurbineParameterProcessor
from .physics_constrained_features import PhysicsConstrainedFeatureGenerator
from .temporal_enhanced_features import TemporalEnhancedFeatures

warnings.filterwarnings('ignore')

class IntegratedFeatureEngineer:
    """
    集成特征工程管道
    
    整合所有新的特征工程模块：
    1. 风机参数处理
    2. 物理约束特征生成
    3. 时序增强特征生成
    4. 特征一致性验证
    """
    
    def __init__(self, config_path: str = "config.json", time_col: str = '时间'):
        """
        初始化集成特征工程管道
        
        Args:
            config_path: 配置文件路径
            time_col: 时间列名
        """
        self.config_path = config_path
        self.time_col = time_col
        
        # 加载配置
        self.config = self._load_config()
        self.enhanced_config = self.config.get('feature_engineering_enhanced', {})
        
        # 初始化各个特征工程器
        self.turbine_processor = None
        self.physics_generator = None
        self.temporal_generator = None
        
        # 特征统计
        self.feature_stats = {}
        self.feature_groups = {}
        
        # 根据配置初始化模块
        self._initialize_modules()
        
        print("集成特征工程管道初始化完成")
        print(f"启用的模块: {self._get_enabled_modules()}")
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"警告：无法加载配置文件 {self.config_path}: {e}")
            return {}
    
    def _initialize_modules(self):
        """根据配置初始化模块"""
        # 风机参数处理器
        if self.enhanced_config.get('enable_turbine_features', True):
            try:
                self.turbine_processor = TurbineParameterProcessor(self.config_path)
                print("  风机参数处理器已启用")
            except Exception as e:
                print(f"  警告：风机参数处理器初始化失败: {e}")
        
        # 物理约束特征生成器
        if self.enhanced_config.get('enable_physics_constraints', True):
            try:
                self.physics_generator = PhysicsConstrainedFeatureGenerator(self.config_path)
                print("  物理约束特征生成器已启用")
            except Exception as e:
                print(f"  警告：物理约束特征生成器初始化失败: {e}")
        
        # 时序增强特征生成器
        if self.enhanced_config.get('enable_temporal_enhancement', True):
            try:
                self.temporal_generator = TemporalEnhancedFeatures(self.time_col)
                print("  时序增强特征生成器已启用")
            except Exception as e:
                print(f"  警告：时序增强特征生成器初始化失败: {e}")
    
    def _get_enabled_modules(self) -> List[str]:
        """获取已启用的模块列表"""
        enabled = []
        if self.turbine_processor is not None:
            enabled.append("风机参数")
        if self.physics_generator is not None:
            enabled.append("物理约束")
        if self.temporal_generator is not None:
            enabled.append("时序增强")
        return enabled
    
    def engineer_features(self, df: pd.DataFrame, 
                         is_training: bool = True,
                         save_feature_info: bool = True) -> pd.DataFrame:
        """
        执行集成特征工程
        
        Args:
            df: 输入数据框
            is_training: 是否为训练模式
            save_feature_info: 是否保存特征信息
            
        Returns:
            添加所有新特征的数据框
        """
        print("=" * 60)
        print("开始集成特征工程处理")
        print("=" * 60)
        
        df_result = df.copy()
        original_features = set(df_result.columns)
        
        # 记录处理步骤
        processing_steps = []
        
        # 1. 物理约束特征生成
        if self.physics_generator is not None:
            print("\n1. 物理约束特征生成")
            print("-" * 30)
            before_physics = set(df_result.columns)
            
            try:
                df_result = self.physics_generator.generate_all_physics_features(df_result)
                physics_features = set(df_result.columns) - before_physics
                
                self.feature_stats['physics_constrained'] = {
                    'count': len(physics_features),
                    'features': list(physics_features)
                }
                
                # 获取特征分组
                if hasattr(self.physics_generator, 'get_feature_importance_groups'):
                    physics_groups = self.physics_generator.get_feature_importance_groups()
                    self.feature_groups.update({f'physics_{k}': v for k, v in physics_groups.items()})
                
                processing_steps.append(f"物理约束特征: +{len(physics_features)}个")
                print(f"  完成，新增 {len(physics_features)} 个特征")
                
            except Exception as e:
                print(f"  错误：物理约束特征生成失败: {e}")
        
        # 2. 时序增强特征生成
        if self.temporal_generator is not None:
            print("\n2. 时序增强特征生成")
            print("-" * 30)
            before_temporal = set(df_result.columns)
            
            try:
                df_result = self.temporal_generator.generate_all_temporal_features(df_result)
                temporal_features = set(df_result.columns) - before_temporal
                
                self.feature_stats['temporal_enhanced'] = {
                    'count': len(temporal_features),
                    'features': list(temporal_features)
                }
                
                # 获取特征分组
                if hasattr(self.temporal_generator, 'get_feature_importance_groups'):
                    temporal_groups = self.temporal_generator.get_feature_importance_groups()
                    self.feature_groups.update({f'temporal_{k}': v for k, v in temporal_groups.items()})
                
                processing_steps.append(f"时序增强特征: +{len(temporal_features)}个")
                print(f"  完成，新增 {len(temporal_features)} 个特征")
                
            except Exception as e:
                print(f"  错误：时序增强特征生成失败: {e}")
        
        # 3. 特征后处理
        print("\n3. 特征后处理")
        print("-" * 30)
        df_result = self._post_process_features(df_result)
        
        # 4. 特征一致性验证
        print("\n4. 特征一致性验证")
        print("-" * 30)
        validation_results = self._validate_feature_consistency(df_result, is_training)
        
        # 5. 生成特征报告
        if save_feature_info:
            self._generate_feature_report(df, df_result, processing_steps, validation_results)
        
        # 6. 保存特征信息（用于预测时的一致性检查）
        if is_training and save_feature_info:
            self._save_feature_info(df_result)
        
        print("\n" + "=" * 60)
        print("集成特征工程处理完成!")
        print("=" * 60)
        
        total_new_features = len(set(df_result.columns) - original_features)
        print(f"总计新增特征: {total_new_features} 个")
        print(f"最终特征数量: {len(df_result.columns)} 个")
        
        return df_result
    
    def _post_process_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征后处理"""
        print("  执行特征后处理...")
        
        # 处理无穷大和NaN值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 填充NaN值
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != self.time_col:
                df[col] = df[col].fillna(df[col].median())
        
        # 移除常数特征
        constant_features = []
        for col in numeric_columns:
            if col != self.time_col and df[col].nunique() <= 1:
                constant_features.append(col)
        
        if constant_features:
            df = df.drop(columns=constant_features)
            print(f"    移除常数特征: {len(constant_features)} 个")
        
        # 特征范围检查
        extreme_features = []
        for col in df.select_dtypes(include=[np.number]).columns:
            if col != self.time_col:
                if df[col].abs().max() > 1e6:  # 检查极值
                    extreme_features.append(col)
        
        if extreme_features:
            print(f"    警告：发现极值特征: {extreme_features[:5]}...")
        
        print(f"    特征后处理完成")
        return df
    
    def _validate_feature_consistency(self, df: pd.DataFrame, is_training: bool) -> Dict:
        """验证特征一致性"""
        print("  验证特征一致性...")
        
        validation_results = {
            'total_features': len(df.columns),
            'numeric_features': len(df.select_dtypes(include=[np.number]).columns),
            'missing_values': df.isnull().sum().sum(),
            'infinite_values': np.isinf(df.select_dtypes(include=[np.number])).sum().sum(),
            'constant_features': sum(df[col].nunique() <= 1 for col in df.select_dtypes(include=[np.number]).columns),
            'validation_passed': True
        }
        
        # 如果是预测模式，检查与训练时的特征一致性
        if not is_training:
            training_feature_info = self._load_feature_info()
            if training_feature_info:
                current_features = set(df.columns)
                training_features = set(training_feature_info.get('feature_names', []))
                
                missing_features = training_features - current_features
                extra_features = current_features - training_features
                
                validation_results.update({
                    'missing_features': list(missing_features),
                    'extra_features': list(extra_features),
                    'feature_consistency': len(missing_features) == 0 and len(extra_features) == 0
                })
                
                if missing_features:
                    print(f"    警告：缺失训练时的特征: {len(missing_features)} 个")
                if extra_features:
                    print(f"    警告：存在额外特征: {len(extra_features)} 个")
        
        print(f"    验证完成，特征数量: {validation_results['total_features']}")
        return validation_results
    
    def _generate_feature_report(self, original_df: pd.DataFrame, 
                                processed_df: pd.DataFrame,
                                processing_steps: List[str],
                                validation_results: Dict):
        """生成特征报告"""
        print("  生成特征报告...")
        
        report = {
            'original_features': len(original_df.columns),
            'final_features': len(processed_df.columns),
            'new_features': len(processed_df.columns) - len(original_df.columns),
            'processing_steps': processing_steps,
            'feature_stats': self.feature_stats,
            'feature_groups': self.feature_groups,
            'validation_results': validation_results,
            'data_shape': processed_df.shape
        }
        
        # 保存报告
        try:
            with open('integrated_feature_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            print(f"    特征报告已保存: integrated_feature_report.json")
        except Exception as e:
            print(f"    警告：特征报告保存失败: {e}")
    
    def _save_feature_info(self, df: pd.DataFrame):
        """保存特征信息（用于预测时的一致性检查）"""
        feature_info = {
            'feature_names': list(df.columns),
            'feature_count': len(df.columns),
            'numeric_features': list(df.select_dtypes(include=[np.number]).columns),
            'time_column': self.time_col,
            'feature_groups': self.feature_groups,
            'config_snapshot': self.enhanced_config
        }
        
        try:
            with open('feature_consistency_info.json', 'w', encoding='utf-8') as f:
                json.dump(feature_info, f, indent=2, ensure_ascii=False)
            print(f"    特征一致性信息已保存: feature_consistency_info.json")
        except Exception as e:
            print(f"    警告：特征一致性信息保存失败: {e}")
    
    def _load_feature_info(self) -> Optional[Dict]:
        """加载特征信息"""
        try:
            with open('feature_consistency_info.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return None
    
    def get_feature_importance_ranking(self) -> Dict[str, List[str]]:
        """获取特征重要性排序"""
        ranking = {
            'critical_features': [],
            'high_priority_features': [],
            'medium_priority_features': [],
            'experimental_features': []
        }
        
        # 关键特征（核心物理和时序特征）
        if 'physics_core_turbine_features' in self.feature_groups:
            ranking['critical_features'].extend(self.feature_groups['physics_core_turbine_features'])
        if 'temporal_core_temporal_features' in self.feature_groups:
            ranking['critical_features'].extend(self.feature_groups['temporal_core_temporal_features'])
        
        # 高优先级特征
        high_priority_groups = [
            'physics_bias_correction_features',
            'temporal_autocorr_features',
            'temporal_time_pattern_features'
        ]
        for group in high_priority_groups:
            if group in self.feature_groups:
                ranking['high_priority_features'].extend(self.feature_groups[group])
        
        # 中优先级特征
        medium_priority_groups = [
            'physics_error_pattern_features',
            'temporal_decorrelation_features',
            'temporal_stability_features'
        ]
        for group in medium_priority_groups:
            if group in self.feature_groups:
                ranking['medium_priority_features'].extend(self.feature_groups[group])
        
        # 实验性特征
        experimental_groups = [
            'physics_turbine_difference_features',
            'temporal_cyclical_features'
        ]
        for group in experimental_groups:
            if group in self.feature_groups:
                ranking['experimental_features'].extend(self.feature_groups[group])
        
        return ranking
    
    def get_processing_summary(self) -> Dict:
        """获取处理摘要"""
        return {
            'enabled_modules': self._get_enabled_modules(),
            'feature_stats': self.feature_stats,
            'feature_groups': self.feature_groups,
            'config': self.enhanced_config
        }
