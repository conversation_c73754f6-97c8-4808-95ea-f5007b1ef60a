---
type: "agent_requested"
---

# 🚀 COMMAND EXECUTION

## 💻 PLATFORM COMMANDS

### File Operations
| Action | Windows | Mac/Linux |
|--------|---------|-----------|
| Create file | `echo. > file.ext` | `touch file.ext` |
| Create dir | `mkdir dir` | `mkdir -p dir` |
| List | `dir` | `ls -la` |
| Show | `type file.ext` | `cat file.ext` |
| Copy | `copy src dest` | `cp src dest` |
| Move | `move src dest` | `mv src dest` |
| Delete file | `del file.ext` | `rm file.ext` |
| Delete dir | `rmdir /s dir` | `rm -rf dir` |

### Development
| Action | Command |
|--------|---------|
| Install | `npm install` |
| Test | `npm test` |
| Build | `npm run build` |
| Start | `npm start` |
| Git status | `git status` |
| Git add | `git add .` |
| Git commit | `git commit -m "msg"` |

## ⚡ COMMAND CHAINING

### Windows
```powershell
# Create structure
New-Item -ItemType Directory -Path "src", "tests" -Force

# Install and start
npm install; npm start

# Git workflow
git add .; git commit -m "commit"; git push
```

### Mac/Linux
```bash
# Create structure
mkdir -p src tests docs

# Install and start
npm install && npm start

# Git workflow
git add . && git commit -m "commit" && git push
```

## 🔍 ERROR HANDLING

```mermaid
graph TD
    Cmd[Command] --> Check{Success?}
    Check -->|Yes| Continue[Continue]
    Check -->|No| Log[Log Error]
    Log --> Retry{Retry?}
    Retry -->|Yes| Cmd
    Retry -->|No| Abort[Abort]
```

## 📝 BEST PRACTICES

1. **Always verify** command success
2. **Use absolute paths** when possible
3. **Chain commands** efficiently
4. **Handle errors** gracefully
5. **Log operations** for debugging
