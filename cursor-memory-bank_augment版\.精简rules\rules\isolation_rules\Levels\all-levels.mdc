---
description: Unified level workflows and templates
globs: all-levels.mdc
alwaysApply: false
---
# 📈 ALL LEVELS UNIFIED

## 🎯 LEVEL WORKFLOWS

```mermaid
graph TD
    Task[Task] --> Assess{Assess Complexity}
    
    Assess -->|Quick| L1[Level 1: VAN→IMPLEMENT→REFLECT]
    Assess -->|Standard| L2[Level 2: VAN→PLAN→IMPLEMENT→REFLECT]
    Assess -->|Complex| L3[Level 3: VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT]
    Assess -->|Enterprise| L4[Level 4: VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT→ARCHIVE]
    
    L1 --> Done1[Quick Documentation]
    L2 --> Done2[Standard Documentation]
    L3 --> Done3[Detailed Documentation]
    L4 --> Done4[Comprehensive Documentation]
```

## 📋 LEVEL TEMPLATES

### Level 1: Quick Fix
```markdown
# QUICK FIX: [Issue Title]

## VAN PHASE
- **Problem**: [Brief description]
- **Impact**: [Who/what is affected]
- **Urgency**: [Timeline]

## IMPLEMENT PHASE
- **Solution**: [What was done]
- **Files Changed**: [List of files]
- **Commands Used**: [Key commands]

## REFLECT PHASE
- **Result**: [Outcome]
- **Verification**: [How tested]
- **Notes**: [Any observations]
```

### Level 2: Enhancement
```markdown
# ENHANCEMENT: [Feature Title]

## VAN PHASE
- **Requirement**: [What needs to be done]
- **Context**: [Background information]
- **Scope**: [Boundaries of work]

## PLAN PHASE
- **Approach**: [High-level strategy]
- **Steps**: [Ordered list of actions]
- **Resources**: [What's needed]

## IMPLEMENT PHASE
- **Progress**: [Track implementation]
- **Issues**: [Problems encountered]
- **Solutions**: [How issues were resolved]

## REFLECT PHASE
- **Outcome**: [Final result]
- **Testing**: [Verification performed]
- **Lessons**: [What was learned]
```

### Level 3: Feature
```markdown
# FEATURE: [Feature Title]

## VAN PHASE
- **Vision**: [What we want to achieve]
- **Stakeholders**: [Who is involved]
- **Success Criteria**: [How to measure success]

## PLAN PHASE
- **Strategy**: [Overall approach]
- **Phases**: [Implementation phases]
- **Dependencies**: [What we depend on]
- **Risks**: [Potential issues]

## CREATIVE PHASE
[Use creative-phase template from platform-creative.mdc]

## IMPLEMENT PHASE
- **Architecture**: [Technical design]
- **Implementation**: [Detailed progress]
- **Testing**: [Test strategy and results]

## REFLECT PHASE
- **Assessment**: [Quality evaluation]
- **Performance**: [Performance metrics]
- **Documentation**: [What was documented]
- **Next Steps**: [Future improvements]
```

### Level 4: Enterprise
```markdown
# ENTERPRISE: [Project Title]

## VAN PHASE
- **Business Case**: [Why this matters]
- **Stakeholders**: [All involved parties]
- **Success Metrics**: [Measurable outcomes]
- **Timeline**: [Project timeline]

## PLAN PHASE
- **Strategy**: [Comprehensive strategy]
- **Architecture**: [System architecture]
- **Phases**: [Detailed phases]
- **Resources**: [Team and tools needed]
- **Risk Management**: [Risk mitigation]

## CREATIVE PHASE
[Use comprehensive creative-phase template]

## IMPLEMENT PHASE
- **Development**: [Implementation tracking]
- **Quality Assurance**: [QA processes]
- **Integration**: [System integration]
- **Deployment**: [Deployment strategy]

## REFLECT PHASE
- **Evaluation**: [Comprehensive evaluation]
- **Metrics**: [Performance and business metrics]
- **Lessons Learned**: [Key insights]
- **Recommendations**: [Future recommendations]

## ARCHIVE PHASE
- **Documentation**: [Complete documentation]
- **Knowledge Transfer**: [Team knowledge sharing]
- **Maintenance**: [Ongoing maintenance plan]
```

## 🔄 ESCALATION RULES

If complexity increases during implementation:
1. **L1→L2**: Add PLAN phase before continuing
2. **L2→L3**: Add CREATIVE phase before IMPLEMENT
3. **L3→L4**: Add ARCHIVE phase after REFLECT

## 📊 QUICK REFERENCE

| Level | Time | Phases | Documentation |
|-------|------|--------|---------------|
| L1 | <30min | 3 | Minimal |
| L2 | 30min-2h | 4 | Standard |
| L3 | 2h-1day | 5 | Detailed |
| L4 | >1day | 6 | Comprehensive |
