"""
方案2：时间序列深度挖掘
实现多周期嵌套分析、气象锋面识别、风速持续性指标、天气转换点检测等高级时间特征
"""

import pandas as pd
import numpy as np
from scipy import signal, stats
from scipy.fft import fft, fftfreq
import warnings
warnings.filterwarnings('ignore')

class AdvancedTimeFeatures:
    """
    高级时间序列特征提取器
    基于深度时间序列分析的创新特征
    """
    
    def __init__(self, time_col: str = '时间'):
        self.time_col = time_col
        
    def create_all_advanced_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建所有高级时间特征
        """
        df = df.copy()
        
        print("创建高级时间序列特征...")
        
        # 1. 多周期嵌套分析
        df = self._create_multi_cycle_features(df)
        
        # 2. 气象锋面识别
        df = self._create_weather_front_features(df)
        
        # 3. 风速持续性指标
        df = self._create_wind_persistence_features(df)
        
        # 4. 天气转换点检测
        df = self._create_weather_transition_features(df)
        
        # 5. 频域分析特征
        df = self._create_frequency_domain_features(df)
        
        # 6. 时间序列分解特征
        df = self._create_decomposition_features(df)
        
        print(f" 高级时间序列特征创建完成")
        
        return df
    
    def _create_multi_cycle_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        多周期嵌套分析
        分析日周期、周周期、月周期、季节周期的复合模式
        """
        if self.time_col not in df.columns:
            return df
        
        time_series = pd.to_datetime(df[self.time_col])
        
        # 日周期特征 (24小时)
        hour_angle = 2 * np.pi * time_series.dt.hour / 24
        df['daily_cycle_sin'] = np.sin(hour_angle)
        df['daily_cycle_cos'] = np.cos(hour_angle)
        
        # 周周期特征 (7天)
        week_angle = 2 * np.pi * time_series.dt.dayofweek / 7
        df['weekly_cycle_sin'] = np.sin(week_angle)
        df['weekly_cycle_cos'] = np.cos(week_angle)
        
        # 月周期特征 (30天)
        month_angle = 2 * np.pi * time_series.dt.day / 30
        df['monthly_cycle_sin'] = np.sin(month_angle)
        df['monthly_cycle_cos'] = np.cos(month_angle)
        
        # 年周期特征 (365天)
        year_angle = 2 * np.pi * time_series.dt.dayofyear / 365
        df['yearly_cycle_sin'] = np.sin(year_angle)
        df['yearly_cycle_cos'] = np.cos(year_angle)
        
        # 复合周期特征
        df['daily_weekly_interaction'] = df['daily_cycle_sin'] * df['weekly_cycle_sin']
        df['daily_monthly_interaction'] = df['daily_cycle_sin'] * df['monthly_cycle_sin']
        df['weekly_monthly_interaction'] = df['weekly_cycle_sin'] * df['monthly_cycle_sin']
        
        # 周期强度指标
        df['cycle_intensity'] = np.sqrt(
            df['daily_cycle_sin']**2 + df['weekly_cycle_sin']**2 + 
            df['monthly_cycle_sin']**2 + df['yearly_cycle_sin']**2
        )
        
        # 周期相位
        df['cycle_phase'] = np.arctan2(
            df['daily_cycle_sin'] + df['weekly_cycle_sin'],
            df['daily_cycle_cos'] + df['weekly_cycle_cos']
        )
        
        return df
    
    def _create_weather_front_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        气象锋面识别
        通过气压、温度、风向的梯度变化识别天气系统
        """
        # 温度梯度特征
        if 'temperature_2m' in df.columns:
            temp = df['temperature_2m']
            
            # 温度梯度
            df['temp_gradient_1h'] = temp.diff(periods=4).fillna(0)  # 1小时梯度 (假设15分钟间隔)
            df['temp_gradient_3h'] = temp.diff(periods=12).fillna(0)  # 3小时梯度
            df['temp_gradient_6h'] = temp.diff(periods=24).fillna(0)  # 6小时梯度
            
            # 温度变化率
            df['temp_change_rate'] = np.abs(df['temp_gradient_1h'])
            
            # 温度锋面强度
            df['temp_front_intensity'] = np.abs(df['temp_gradient_3h']) / (np.std(temp) + 1e-6)
            
            # 冷锋/暖锋识别
            df['cold_front_indicator'] = (df['temp_gradient_3h'] < -2).astype(int)
            df['warm_front_indicator'] = (df['temp_gradient_3h'] > 2).astype(int)
        
        # 风向梯度特征
        if 'wind_direction_80m' in df.columns:
            wind_dir = df['wind_direction_80m']
            
            # 处理风向的环形特性
            def circular_diff(angles, periods=1):
                diff = angles.diff(periods=periods).fillna(0)
                # 处理360度跳跃
                diff = np.where(diff > 180, diff - 360, diff)
                diff = np.where(diff < -180, diff + 360, diff)
                return diff
            
            df['wind_dir_change_1h'] = circular_diff(wind_dir, 4)
            df['wind_dir_change_3h'] = circular_diff(wind_dir, 12)
            df['wind_dir_change_6h'] = circular_diff(wind_dir, 24)
            
            # 风向变化强度
            df['wind_dir_change_intensity'] = np.abs(df['wind_dir_change_3h'])
            
            # 风向锋面指标
            df['wind_dir_front'] = (df['wind_dir_change_intensity'] > 30).astype(int)
        
        # 综合锋面指标
        front_indicators = []
        if 'temp_front_intensity' in df.columns:
            front_indicators.append(df['temp_front_intensity'])
        if 'wind_dir_change_intensity' in df.columns:
            front_indicators.append(df['wind_dir_change_intensity'] / 30)  # 归一化
        
        if front_indicators:
            df['weather_front_strength'] = np.mean(front_indicators, axis=0)
            df['weather_front_detected'] = (df['weather_front_strength'] > 0.5).astype(int)
        else:
            df['weather_front_strength'] = 0
            df['weather_front_detected'] = 0
        
        return df
    
    def _create_wind_persistence_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        风速持续性指标
        分析风速的持续性和变化模式
        """
        if 'wind_speed_80m' not in df.columns:
            return df
        
        wind_speed = df['wind_speed_80m']
        
        # 风速持续性 - 连续高风速时间
        high_wind_threshold = 10.0  # 高风速阈值
        df['is_high_wind'] = (wind_speed >= high_wind_threshold).astype(int)
        
        # 计算连续高风速持续时间
        high_wind_groups = (df['is_high_wind'] != df['is_high_wind'].shift()).cumsum()
        df['high_wind_duration'] = df.groupby(high_wind_groups)['is_high_wind'].cumsum() * df['is_high_wind']
        
        # 连续低风速时间
        low_wind_threshold = 5.0
        df['is_low_wind'] = (wind_speed <= low_wind_threshold).astype(int)
        low_wind_groups = (df['is_low_wind'] != df['is_low_wind'].shift()).cumsum()
        df['low_wind_duration'] = df.groupby(low_wind_groups)['is_low_wind'].cumsum() * df['is_low_wind']
        
        # 风速稳定性指标
        df['wind_stability_1h'] = 1 / (1 + wind_speed.rolling(4).std().fillna(0))
        df['wind_stability_3h'] = 1 / (1 + wind_speed.rolling(12).std().fillna(0))
        df['wind_stability_6h'] = 1 / (1 + wind_speed.rolling(24).std().fillna(0))
        
        # 风速趋势
        df['wind_trend_1h'] = wind_speed.rolling(4).apply(lambda x: stats.linregress(range(len(x)), x)[0] if len(x) > 1 else 0).fillna(0)
        df['wind_trend_3h'] = wind_speed.rolling(12).apply(lambda x: stats.linregress(range(len(x)), x)[0] if len(x) > 1 else 0).fillna(0)
        
        # 风速变化幅度
        df['wind_range_1h'] = wind_speed.rolling(4).max() - wind_speed.rolling(4).min()
        df['wind_range_3h'] = wind_speed.rolling(12).max() - wind_speed.rolling(12).min()
        
        # 风速持续性指数
        df['wind_persistence_index'] = (
            df['wind_stability_3h'] * 0.4 + 
            (1 / (1 + df['wind_range_3h'])) * 0.3 +
            np.exp(-np.abs(df['wind_trend_3h'])) * 0.3
        )
        
        return df
    
    def _create_weather_transition_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        天气转换点检测
        识别天气模式的突变点
        """
        # 多变量变化点检测
        variables = ['wind_speed_80m', 'temperature_2m', 'rain']
        available_vars = [var for var in variables if var in df.columns]
        
        if not available_vars:
            return df
        
        # 计算多变量变化强度
        change_intensities = []
        
        for var in available_vars:
            data = df[var].fillna(df[var].mean())
            
            # 计算不同时间窗口的变化
            change_1h = np.abs(data.diff(periods=4).fillna(0))
            change_3h = np.abs(data.diff(periods=12).fillna(0))
            change_6h = np.abs(data.diff(periods=24).fillna(0))
            
            # 归一化变化强度
            std_val = data.std() + 1e-6
            normalized_change = (change_1h + change_3h + change_6h) / (3 * std_val)
            change_intensities.append(normalized_change)
        
        # 综合变化强度
        df['weather_change_intensity'] = np.mean(change_intensities, axis=0)
        
        # 变化点检测
        df['weather_transition_point'] = (df['weather_change_intensity'] > df['weather_change_intensity'].quantile(0.9)).astype(int)
        
        # 变化方向
        if 'wind_speed_80m' in df.columns and 'temperature_2m' in df.columns:
            wind_change = df['wind_speed_80m'].diff(periods=12).fillna(0)
            temp_change = df['temperature_2m'].diff(periods=12).fillna(0)
            
            # 天气模式分类
            df['weather_pattern'] = np.where(
                (wind_change > 0) & (temp_change > 0), 1,  # 升温增风
                np.where(
                    (wind_change > 0) & (temp_change < 0), 2,  # 降温增风
                    np.where(
                        (wind_change < 0) & (temp_change > 0), 3,  # 升温减风
                        4  # 降温减风
                    )
                )
            )
        else:
            df['weather_pattern'] = 0
        
        # 天气稳定期长度
        transition_groups = (df['weather_transition_point'] != df['weather_transition_point'].shift()).cumsum()
        df['stable_period_length'] = df.groupby(transition_groups).cumcount() + 1
        
        return df

    def _create_frequency_domain_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        频域分析特征
        通过FFT分析时间序列的频率特性
        """
        if 'wind_speed_80m' not in df.columns:
            return df

        wind_speed = df['wind_speed_80m'].fillna(df['wind_speed_80m'].mean())

        # 使用滑动窗口进行频域分析
        window_size = min(96, len(df) // 4)  # 24小时窗口 (假设15分钟间隔)

        dominant_frequencies = []
        spectral_energies = []
        spectral_entropies = []

        for i in range(len(df)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(df), i + window_size // 2)

            window_data = wind_speed.iloc[start_idx:end_idx].values

            if len(window_data) >= 32:  # 最小FFT长度
                # FFT分析
                fft_values = fft(window_data)
                frequencies = fftfreq(len(window_data), d=0.25)  # 15分钟 = 0.25小时

                # 功率谱密度
                power_spectrum = np.abs(fft_values) ** 2
                positive_freq_mask = frequencies > 0

                if np.sum(positive_freq_mask) > 0:
                    pos_frequencies = frequencies[positive_freq_mask]
                    pos_power = power_spectrum[positive_freq_mask]

                    # 主导频率
                    dominant_freq_idx = np.argmax(pos_power)
                    dominant_freq = pos_frequencies[dominant_freq_idx]
                    dominant_frequencies.append(dominant_freq)

                    # 谱能量
                    total_energy = np.sum(pos_power)
                    spectral_energies.append(total_energy)

                    # 谱熵 (频率分布的均匀性)
                    normalized_power = pos_power / (total_energy + 1e-10)
                    spectral_entropy = -np.sum(normalized_power * np.log(normalized_power + 1e-10))
                    spectral_entropies.append(spectral_entropy)
                else:
                    dominant_frequencies.append(0)
                    spectral_energies.append(0)
                    spectral_entropies.append(0)
            else:
                dominant_frequencies.append(0)
                spectral_energies.append(0)
                spectral_entropies.append(0)

        df['dominant_frequency'] = dominant_frequencies
        df['spectral_energy'] = spectral_energies
        df['spectral_entropy'] = spectral_entropies

        # 频率特征分类
        df['frequency_category'] = np.where(
            np.array(dominant_frequencies) > 1/6, 1,  # 高频 (< 6小时周期)
            np.where(
                np.array(dominant_frequencies) > 1/24, 2,  # 中频 (6-24小时周期)
                3  # 低频 (> 24小时周期)
            )
        )

        # 谱稳定性
        df['spectral_stability'] = 1 / (1 + np.abs(pd.Series(dominant_frequencies).diff().fillna(0)))

        return df

    def _create_decomposition_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        时间序列分解特征
        分解趋势、季节性和残差成分
        """
        if 'wind_speed_80m' not in df.columns:
            return df

        wind_speed = df['wind_speed_80m'].fillna(df['wind_speed_80m'].mean())

        # 简化的时间序列分解
        window_size = min(96, len(df) // 4)  # 24小时窗口

        trends = []
        seasonals = []
        residuals = []

        for i in range(len(df)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(df), i + window_size // 2)

            window_data = wind_speed.iloc[start_idx:end_idx]

            if len(window_data) >= 24:  # 至少6小时数据
                # 趋势成分 (移动平均)
                trend = window_data.rolling(window=12, center=True).mean().iloc[len(window_data)//2]
                if pd.isna(trend):
                    trend = window_data.mean()

                # 去趋势数据
                detrended = window_data - trend

                # 季节性成分 (简化的周期性检测)
                if len(detrended) >= 24:  # 至少6小时
                    # 使用24点 (6小时) 的周期性
                    seasonal_pattern = detrended.iloc[:24].values
                    seasonal = seasonal_pattern[i % 24] if len(seasonal_pattern) > i % 24 else 0
                else:
                    seasonal = 0

                # 残差成分
                residual = wind_speed.iloc[i] - trend - seasonal

                trends.append(trend)
                seasonals.append(seasonal)
                residuals.append(residual)
            else:
                current_value = wind_speed.iloc[i]
                trends.append(current_value)
                seasonals.append(0)
                residuals.append(0)

        df['trend_component'] = trends
        df['seasonal_component'] = seasonals
        df['residual_component'] = residuals

        # 成分强度
        df['trend_strength'] = np.abs(df['trend_component']) / (wind_speed.std() + 1e-6)
        df['seasonal_strength'] = np.abs(df['seasonal_component']) / (wind_speed.std() + 1e-6)
        df['residual_strength'] = np.abs(df['residual_component']) / (wind_speed.std() + 1e-6)

        # 成分稳定性
        df['trend_stability'] = 1 / (1 + np.abs(pd.Series(trends).diff().fillna(0)))
        df['seasonal_stability'] = 1 / (1 + np.abs(pd.Series(seasonals).diff().fillna(0)))

        # 分解质量指标
        reconstructed = np.array(trends) + np.array(seasonals)
        reconstruction_error = np.abs(wind_speed.values - reconstructed)
        df['decomposition_quality'] = 1 - reconstruction_error / (wind_speed.std() + 1e-6)
        df['decomposition_quality'] = np.clip(df['decomposition_quality'], 0, 1)

        return df
