"""
特征选择模块
基于特征重要性进行特征筛选，支持配置保留特征数
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import joblib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from .lightgbm_optimizer import LightGBMHyperOptimizer, PhysicsConstrainedLightGBM
from .config_manager import ConfigManager

class FeatureSelector:
    """
    特征选择器
    基于LightGBM特征重要性进行特征筛选
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化特征选择器
        
        Args:
            config_manager: 配置管理器，如果为None则创建新的
        """
        self.config = config_manager if config_manager else ConfigManager()
        
        # 特征选择配置
        self.enable_selection = self.config.get('feature_selection.enable_feature_selection', True)
        self.selected_features_count = self.config.get('feature_selection.selected_features_count', 100)
        self.selection_method = self.config.get('feature_selection.selection_method', 'importance')
        self.importance_threshold = self.config.get('feature_selection.importance_threshold', 0.001)
        
        # 存储选择结果
        self.selected_features_ = None
        self.feature_importance_ = None
        self.selection_stats_ = None
        
    def fit_select_features(self, X: pd.DataFrame, y: np.ndarray, 
                           target_column: str = '理论功率 (MW)') -> List[str]:
        """
        训练特征选择器并选择特征
        
        Args:
            X: 特征数据
            y: 目标变量
            target_column: 目标列名
            
        Returns:
            选择的特征列表
        """
        print("\n" + "=" * 50)
        print("开始特征选择")
        print("=" * 50)
        
        if not self.enable_selection:
            print("特征选择已禁用，使用所有特征")
            self.selected_features_ = list(X.columns)
            return self.selected_features_
        
        print(f"原始特征数量: {len(X.columns)}")

        # 动态调整目标特征数，不能超过原始特征数
        actual_target_count = min(self.selected_features_count, len(X.columns))
        if actual_target_count < self.selected_features_count:
            print(f"注意: 目标特征数 {self.selected_features_count} 超过原始特征数，调整为 {actual_target_count}")

        print(f"目标保留特征数: {actual_target_count}")
        print(f"选择方法: {self.selection_method}")
        
        # 确保数据类型正确
        X_clean = self._prepare_data(X)
        
        # 计算特征重要性
        self.feature_importance_ = self._calculate_feature_importance(X_clean, y)
        
        # 选择特征
        self.selected_features_ = self._select_top_features(actual_target_count)
        
        # 生成选择统计
        self._generate_selection_stats(X.columns)
        
        print(f"\n✓ 特征选择完成")
        print(f"选择特征数: {len(self.selected_features_)}")
        print(f"特征保留率: {len(self.selected_features_)/len(X.columns)*100:.1f}%")
        
        return self.selected_features_
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        应用特征选择到新数据
        
        Args:
            X: 输入特征数据
            
        Returns:
            选择后的特征数据
        """
        if self.selected_features_ is None:
            raise ValueError("特征选择器尚未训练，请先调用fit_select_features方法")
        
        # 检查特征是否存在
        missing_features = set(self.selected_features_) - set(X.columns)
        if missing_features:
            print(f"警告: 缺失特征 {missing_features}")
            # 只选择存在的特征
            available_features = [f for f in self.selected_features_ if f in X.columns]
            return X[available_features]
        
        return X[self.selected_features_]
    
    def _prepare_data(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        准备数据，处理数据类型和缺失值
        """
        X_clean = X.copy()
        
        # 处理数据类型
        for col in X_clean.columns:
            if X_clean[col].dtype == 'object' or X_clean[col].dtype.name == 'category':
                X_clean[col] = pd.to_numeric(X_clean[col], errors='coerce')
        
        # 处理缺失值
        X_clean = X_clean.fillna(X_clean.median())
        
        return X_clean
    
    def _calculate_feature_importance(self, X: pd.DataFrame, y: np.ndarray) -> pd.DataFrame:
        """
        计算特征重要性
        """
        print("计算特征重要性...")
        
        # 使用LightGBM计算特征重要性
        optimizer = LightGBMHyperOptimizer(
            cv_folds=3, 
            n_trials=10,  # 减少试验次数以加快速度
            physics_constrained=True
        )
        
        # 快速优化获取合理参数
        optimization_results = optimizer.optimize(X, y)
        
        # 获取特征重要性分析
        importance_analysis = optimizer.get_feature_importance_analysis(X, y)
        
        return importance_analysis['feature_importance']
    
    def _select_top_features(self, target_count: int) -> List[str]:
        """
        选择Top特征
        """
        if self.selection_method == 'importance':
            # 按重要性排序，选择前N个
            top_features = self.feature_importance_.head(target_count)
            return top_features['feature'].tolist()
        
        elif self.selection_method == 'threshold':
            # 按阈值筛选
            important_features = self.feature_importance_[
                self.feature_importance_['importance'] >= self.importance_threshold
            ]
            return important_features['feature'].tolist()
        
        else:
            raise ValueError(f"不支持的选择方法: {self.selection_method}")
    
    def _generate_selection_stats(self, original_features: List[str]):
        """
        生成特征选择统计信息
        """
        self.selection_stats_ = {
            'original_feature_count': len(original_features),
            'selected_feature_count': len(self.selected_features_),
            'selection_ratio': len(self.selected_features_) / len(original_features),
            'top_10_features': self.selected_features_[:10],
            'feature_importance_summary': {
                'max_importance': self.feature_importance_['importance'].max(),
                'min_importance': self.feature_importance_['importance'].min(),
                'mean_importance': self.feature_importance_['importance'].mean(),
                'selected_min_importance': self.feature_importance_.head(len(self.selected_features_))['importance'].min()
            }
        }
    
    def get_selection_summary(self) -> Dict:
        """
        获取特征选择摘要
        """
        if self.selection_stats_ is None:
            return {"error": "特征选择尚未执行"}
        
        return self.selection_stats_
    
    def save_selection_results(self, filepath: str):
        """
        保存特征选择结果
        """
        if self.selected_features_ is None:
            raise ValueError("特征选择器尚未训练")
        
        results = {
            'selected_features': self.selected_features_,
            'feature_importance': self.feature_importance_.to_dict('records'),
            'selection_stats': self.selection_stats_,
            'config': {
                'selected_features_count': self.selected_features_count,
                'selection_method': self.selection_method,
                'importance_threshold': self.importance_threshold
            }
        }
        
        # 保存为joblib格式
        joblib.dump(results, filepath)
        print(f"✓ 特征选择结果已保存到: {filepath}")
    
    def load_selection_results(self, filepath: str):
        """
        加载特征选择结果
        """
        if not Path(filepath).exists():
            raise FileNotFoundError(f"特征选择结果文件不存在: {filepath}")
        
        results = joblib.load(filepath)
        
        self.selected_features_ = results['selected_features']
        self.feature_importance_ = pd.DataFrame(results['feature_importance'])
        self.selection_stats_ = results['selection_stats']
        
        print(f"✓ 特征选择结果已加载: {len(self.selected_features_)} 个特征")
    
    def print_feature_importance_report(self, top_n: int = 20):
        """
        打印特征重要性报告
        """
        if self.feature_importance_ is None:
            print("特征重要性尚未计算")
            return
        
        print(f"\n特征重要性报告 (Top {top_n})")
        print("-" * 60)
        
        top_features = self.feature_importance_.head(top_n)
        for i, (_, row) in enumerate(top_features.iterrows(), 1):
            selected_mark = "✓" if row['feature'] in self.selected_features_ else " "
            print(f"{i:2d}. {selected_mark} {row['feature']:<40} {row['importance']:>10.4f}")
        
        if self.selection_stats_:
            print(f"\n选择统计:")
            print(f"  原始特征数: {self.selection_stats_['original_feature_count']}")
            print(f"  选择特征数: {self.selection_stats_['selected_feature_count']}")
            print(f"  选择比例: {self.selection_stats_['selection_ratio']*100:.1f}%")
