#!/usr/bin/env python3
"""
修复版本的score.py - 处理时间不匹配问题
"""

import pandas as pd
import numpy as np
import os
import glob
from sklearn.metrics import r2_score

# 常量定义
INSTALLED_CAPACITY = 201  # 装机容量 (MW)

def try_read_csv(filepath, encoding_list=['utf-8', 'gbk', 'gb2312', 'ansi']):
    """尝试用不同编码读取CSV文件"""
    for encoding in encoding_list:
        try:
            return pd.read_csv(filepath, encoding=encoding)
        except (UnicodeDecodeError, ValueError):
            continue
    raise ValueError(f"无法用常见编码读取文件: {filepath}")

def standardize_time_format(df, time_col):
    """标准化时间格式"""
    try:
        df[time_col] = pd.to_datetime(df[time_col])
        return df
    except Exception as e:
        print(f"时间格式转换失败: {e}")
        return df

def find_time_overlap(df1, df2, time_col1, time_col2):
    """查找两个数据集的时间重叠部分"""
    # 获取时间范围
    start1, end1 = df1[time_col1].min(), df1[time_col1].max()
    start2, end2 = df2[time_col2].min(), df2[time_col2].max()

    print(f"数据集1时间范围: {start1} 到 {end1}")
    print(f"数据集2时间范围: {start2} 到 {end2}")

    # 计算重叠范围
    overlap_start = max(start1, start2)
    overlap_end = min(end1, end2)

    if overlap_start <= overlap_end:
        print(f"时间重叠范围: {overlap_start} 到 {overlap_end}")
        return overlap_start, overlap_end
    else:
        print("❌ 没有时间重叠！")
        return None, None

def main():
    print("🔧 修复版本的风力发电功率预测评估")
    print("=" * 60)

    # 1. 查找最新的预测文件
    prediction_dir = 'daily_predictions'
    prediction_files = glob.glob(os.path.join(prediction_dir, '*.csv'))
    if not prediction_files:
        print(f"❌ 在 {prediction_dir} 中没有找到预测文件")
        return

    latest_file = max(prediction_files, key=os.path.getctime)
    print(f"📁 使用预测文件: {os.path.basename(latest_file)}")

    # 2. 读取预测文件
    try:
        df_pred = try_read_csv(latest_file)
        print(f"✅ 预测文件读取成功，形状: {df_pred.shape}")
        print(f"   列名: {list(df_pred.columns)}")

        # 标准化预测文件的时间
        df_pred = standardize_time_format(df_pred, '时间')

    except Exception as e:
        print(f"❌ 读取预测文件失败: {e}")
        return

    # 3. 读取真实值文件
    try:
        df_true = try_read_csv('真实值_实际功率限电修改.csv')
        print(f"✅ 真实值文件读取成功，形状: {df_true.shape}")
        print(f"   列名: {list(df_true.columns)}")

        # 处理列名（可能有编码问题）
        true_time_col = df_true.columns[0]  # 第一列是时间
        true_power_col = df_true.columns[1]  # 第二列是功率

        # 重命名列
        df_true.columns = ['时间', '答案']

        # 标准化真实值文件的时间
        df_true = standardize_time_format(df_true, '时间')

    except Exception as e:
        print(f"❌ 读取真实值文件失败: {e}")
        return

    # 4. 检查时间重叠
    overlap_start, overlap_end = find_time_overlap(df_pred, df_true, '时间', '时间')

    if overlap_start is None:
        print("\n💡 解决方案建议:")
        print("1. 检查预测文件和真实值文件的时间范围")
        print("2. 确保使用相同时间段的数据")
        print("3. 可能需要重新生成预测数据")

        # 显示样本数据
        print("\n📊 预测数据样本:")
        print(df_pred[['时间', 'pred']].head())
        print("\n📊 真实数据样本:")
        print(df_true[['时间', '答案']].head())
        return

    # 5. 筛选重叠时间段的数据
    df_pred_filtered = df_pred[
        (df_pred['时间'] >= overlap_start) &
        (df_pred['时间'] <= overlap_end)
    ].copy()

    df_true_filtered = df_true[
        (df_true['时间'] >= overlap_start) &
        (df_true['时间'] <= overlap_end)
    ].copy()

    print(f"📊 筛选后预测数据: {len(df_pred_filtered)} 条")
    print(f"📊 筛选后真实数据: {len(df_true_filtered)} 条")

    # 6. 合并数据
    df_merged = pd.merge(df_pred_filtered, df_true_filtered, on='时间', how='inner')
    print(f"📊 合并后数据: {len(df_merged)} 条")

    if len(df_merged) == 0:
        print("❌ 合并后没有数据，可能是时间格式不完全匹配")
        return

    # 7. 计算评估指标
    try:
        # 确保数据类型正确
        df_merged['答案'] = pd.to_numeric(df_merged['答案'], errors='coerce')
        df_merged['pred'] = pd.to_numeric(df_merged['pred'], errors='coerce')

        # 移除无效数据
        df_merged = df_merged.dropna(subset=['答案', 'pred'])
        n = len(df_merged)

        if n == 0:
            print("❌ 没有有效的数值数据")
            return

        print(f"✅ 有效数据点: {n}")

        # 计算指标
        errors = (df_merged['答案'] - df_merged['pred']) / INSTALLED_CAPACITY
        rmse = np.sqrt(np.mean(errors**2))
        r2 = r2_score(df_merged['答案'], df_merged['pred'])
        total_accuracy = 1 - rmse

        # 计算每日指标
        df_merged['日期'] = df_merged['时间'].dt.date
        daily_results = []

        for date, group in df_merged.groupby('日期'):
            if len(group) > 0:
                daily_errors = (group['答案'] - group['pred']) / INSTALLED_CAPACITY
                daily_rmse = np.sqrt(np.mean(daily_errors**2))
                daily_accuracy = 1 - daily_rmse
                daily_results.append({
                    '日期': date,
                    '数据点数': len(group),
                    '准确率': daily_accuracy
                })

    except Exception as e:
        print(f"❌ 计算指标失败: {e}")
        import traceback
        traceback.print_exc()
        return

    # 8. 输出结果
    print("\n" + "=" * 60)
    print("📊 评估结果")
    print("=" * 60)
    print(f"总样本数: {n}")
    print(f"总天数: {len(daily_results)}")
    print(f"RMSE (归一化): {rmse:.6f}")
    print(f"R²: {r2:.6f}")
    print(f"总准确率: {total_accuracy:.6f}")

    if daily_results:
        avg_daily_accuracy = np.mean([r['准确率'] for r in daily_results])
        print(f"平均每日准确率: {avg_daily_accuracy:.6f}")

        print("\n每日准确率详情:")
        for i, result in enumerate(daily_results, 1):
            print(f"第{i}天 ({result['日期']}): {result['准确率']:.6f} (数据点: {result['数据点数']})")

    # 9. 保存结果
    results_data = {
        'RMSE': [rmse],
        'R²': [r2],
        '总准确率': [total_accuracy],
        '有效数据点': [n],
        '评估天数': [len(daily_results)]
    }

    if daily_results:
        results_data['平均每日准确率'] = [avg_daily_accuracy]
        for i, result in enumerate(daily_results, 1):
            results_data[f'第{i}天({result["日期"]})准确率'] = [result['准确率']]

    pd.DataFrame(results_data).to_csv('指标评测结果.csv', index=False, encoding='utf-8-sig')
    print("\n✅ 结果已保存到: 指标评测结果.csv")

    # 保存详细数据
    df_merged['误差'] = df_merged['答案'] - df_merged['pred']
    df_merged['相对误差'] = df_merged['误差'] / INSTALLED_CAPACITY
    df_merged.to_csv('详细评测数据.csv', index=False, encoding='utf-8-sig')
    print("✅ 详细数据已保存到: 详细评测数据.csv")

if __name__ == "__main__":
    main()