"""
天气预报下载脚本包装器
整合原有的天气预报下载功能到主程序中
"""

import sys
import subprocess
from pathlib import Path
import pandas as pd

def download_weather_forecast():
    """
    执行天气预报下载
    直接运行原有的下载脚本
    """
    print("=" * 60)
    print("🌤️  天气预报数据下载")
    print("=" * 60)
    
    # 检查原始脚本是否存在
    original_script = Path("下载15min天气预报.py")
    if not original_script.exists():
        print("❌ 天气预报下载脚本不存在: 下载15min天气预报.py")
        return False
    
    # 确保data目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    print("正在下载15分钟间隔天气预报数据...")
    print("数据来源: Open-Meteo API")
    print("预报时长: 188个15分钟间隔 (约47小时)")
    
    try:
        # 直接执行原始脚本
        result = subprocess.run([
            sys.executable, 
            str(original_script)
        ], capture_output=True, text=True, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ 天气预报数据下载成功!")
            
            # 查找生成的文件
            forecast_files = list(data_dir.glob("15min_预报_*.csv"))
            if forecast_files:
                latest_file = max(forecast_files, key=lambda x: x.stat().st_mtime)
                print(f"📁 数据文件: {latest_file}")
                
                # 显示数据预览
                try:
                    df = pd.read_csv(latest_file)
                    print(f"📊 数据形状: {df.shape}")
                    print(f"📅 时间范围: {df['时间'].iloc[0]} 到 {df['时间'].iloc[-1]}")
                    print("\n数据预览:")
                    print(df.head(3).to_string(index=False))
                    
                    if len(df) > 3:
                        print("...")
                        print(df.tail(2).to_string(index=False))
                        
                except Exception as e:
                    print(f"⚠️  数据预览失败: {e}")
            
            print(f"\n🎯 下载完成!")
            print("可以将此文件用作测试集进行预测")
            return True
            
        else:
            print("❌ 天气预报下载失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 执行下载脚本时出错: {e}")
        return False

def check_dependencies():
    """
    检查天气预报下载所需的依赖
    """
    print("检查天气预报下载依赖...")
    
    required_packages = [
        'openmeteo_requests',
        'requests_cache', 
        'retry_requests',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def main():
    """
    主函数
    """
    print("🌤️  天气预报数据下载工具")
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺少的依赖包，然后重新运行")
        return
    
    # 下载天气预报
    success = download_weather_forecast()
    
    if success:
        print(f"\n" + "=" * 60)
        print("天气预报下载完成!")
        print("=" * 60)
        print("下一步建议:")
        print("1. 将下载的文件配置为测试集")
        print("2. 运行预测: python predict_today.py")
        print("3. 或使用启动脚本: python start.py (选择今日预测)")
    else:
        print(f"\n" + "=" * 60)
        print("天气预报下载失败")
        print("=" * 60)
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. 依赖包是否完整安装")
        print("3. 原始下载脚本是否存在")

if __name__ == "__main__":
    main()
