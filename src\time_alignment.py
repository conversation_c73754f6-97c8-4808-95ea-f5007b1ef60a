"""
时间精确对齐系统 - 解决时间断层和空值删除后的特征对齐问题
基于CREATIVE阶段设计的创新解决方案
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import warnings
from datetime import datetime, timedelta

class TimeAwareFeatureAligner:
    """
    时间感知的特征对齐器
    解决训练集删除空值后无法使用shift/diff的问题
    使用时间戳减法进行精确对齐
    """
    
    def __init__(self, base_interval: str = '15min', time_col: str = '时间'):
        """
        初始化时间对齐器
        
        Args:
            base_interval: 基础时间间隔，默认15分钟
            time_col: 时间列名称
        """
        self.base_interval = base_interval
        self.time_col = time_col
        self.interval_minutes = self._parse_interval(base_interval)
        
    def _parse_interval(self, interval: str) -> int:
        """解析时间间隔为分钟数"""
        if interval.endswith('min'):
            return int(interval[:-3])
        elif interval.endswith('h'):
            return int(interval[:-1]) * 60
        else:
            raise ValueError(f"不支持的时间间隔格式: {interval}")
    
    def create_lag_features(self, 
                          df: pd.DataFrame, 
                          feature_cols: List[str], 
                          lag_periods: List[int],
                          alignment_strategy: str = 'exact') -> pd.DataFrame:
        """
        创建滞后特征，使用时间戳精确对齐
        
        Args:
            df: 输入数据框，必须包含时间列
            feature_cols: 需要创建滞后特征的列名
            lag_periods: 滞后期数列表 (以base_interval为单位)
            alignment_strategy: 对齐策略 ('exact', 'nearest', 'interpolated')
            
        Returns:
            包含滞后特征的数据框
        """
        # 确保时间列为datetime类型
        df = df.copy()
        if not pd.api.types.is_datetime64_any_dtype(df[self.time_col]):
            df[self.time_col] = pd.to_datetime(df[self.time_col])
        
        # 按时间排序
        df = df.sort_values(self.time_col).reset_index(drop=True)
        
        # 创建时间索引字典用于快速查找
        time_index = {time: idx for idx, time in enumerate(df[self.time_col])}
        
        # 为每个特征和滞后期创建特征
        for feature in feature_cols:
            for lag in lag_periods:
                lag_col_name = f"{feature}_lag_{lag}"
                lag_values = []
                
                for idx, current_time in enumerate(df[self.time_col]):
                    # 计算目标时间
                    target_time = current_time - pd.Timedelta(minutes=self.interval_minutes * lag)
                    
                    # 根据对齐策略获取值
                    lag_value = self._get_aligned_value(
                        df, feature, target_time, time_index, alignment_strategy
                    )
                    lag_values.append(lag_value)
                
                df[lag_col_name] = lag_values
        
        return df
    
    def _get_aligned_value(self, 
                          df: pd.DataFrame, 
                          feature: str, 
                          target_time: pd.Timestamp,
                          time_index: Dict, 
                          strategy: str) -> Optional[float]:
        """
        根据对齐策略获取目标时间的特征值
        """
        if strategy == 'exact':
            # 精确匹配
            if target_time in time_index:
                idx = time_index[target_time]
                return df.iloc[idx][feature]
            else:
                return np.nan
                
        elif strategy == 'nearest':
            # 最近邻匹配
            time_diffs = np.abs(df[self.time_col] - target_time)
            min_diff_idx = time_diffs.idxmin()
            
            # 如果时间差超过阈值，返回NaN
            if time_diffs.iloc[min_diff_idx] > pd.Timedelta(minutes=self.interval_minutes):
                return np.nan
            else:
                return df.iloc[min_diff_idx][feature]
                
        elif strategy == 'interpolated':
            # 线性插值
            return self._interpolate_value(df, feature, target_time)
        
        else:
            raise ValueError(f"不支持的对齐策略: {strategy}")
    
    def _interpolate_value(self, 
                          df: pd.DataFrame, 
                          feature: str, 
                          target_time: pd.Timestamp) -> Optional[float]:
        """
        线性插值获取目标时间的特征值
        """
        # 找到目标时间前后的数据点
        before_mask = df[self.time_col] <= target_time
        after_mask = df[self.time_col] >= target_time
        
        if not before_mask.any() or not after_mask.any():
            return np.nan
        
        before_data = df[before_mask].iloc[-1]  # 最近的前一个点
        after_data = df[after_mask].iloc[0]     # 最近的后一个点
        
        # 如果是同一个点，直接返回
        if before_data[self.time_col] == after_data[self.time_col]:
            return before_data[feature]
        
        # 线性插值
        time_diff = (after_data[self.time_col] - before_data[self.time_col]).total_seconds()
        target_diff = (target_time - before_data[self.time_col]).total_seconds()
        
        if time_diff == 0:
            return before_data[feature]
        
        weight = target_diff / time_diff
        interpolated_value = before_data[feature] + weight * (after_data[feature] - before_data[feature])
        
        return interpolated_value

class MultiScaleTimeFeatures:
    """
    多尺度时间特征提取器
    实现CREATIVE阶段设计的分层时间特征
    """
    
    def __init__(self, time_col: str = '时间'):
        self.time_col = time_col
    
    def extract_all_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取所有时间特征
        """
        print("    开始传统时间特征工程...")
        df = df.copy()
        original_cols = len(df.columns)
        
        # 确保时间列为datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df[self.time_col]):
            df[self.time_col] = pd.to_datetime(df[self.time_col])
        
        # 微观时间特征 (15分钟级别)
        df = self._extract_micro_features(df)
        
        # 中观时间特征 (小时级别)
        df = self._extract_meso_features(df)
        
        # 宏观时间特征 (日级别)
        df = self._extract_macro_features(df)
        
        # 周期性特征
        df = self._extract_cyclical_features(df)

        new_cols = len(df.columns)
        print(f"    传统时间特征工程完成，新增 {new_cols - original_cols} 个特征")

        return df
    
    def _extract_micro_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取微观时间特征"""
        time_series = df[self.time_col]
        
        # 15分钟内的位置
        df['minute_15'] = (time_series.dt.minute // 15) * 15
        df['minute_in_15min'] = time_series.dt.minute % 15
        
        # 小时内的15分钟段
        df['quarter_hour'] = time_series.dt.minute // 15
        
        return df
    
    def _extract_meso_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取中观时间特征"""
        time_series = df[self.time_col]
        
        # 小时特征
        df['hour'] = time_series.dt.hour
        df['is_peak_hour'] = ((time_series.dt.hour >= 8) & (time_series.dt.hour <= 10) |
                             (time_series.dt.hour >= 18) & (time_series.dt.hour <= 20)).astype(int)
        
        # 一天中的时段
        df['time_period'] = pd.cut(time_series.dt.hour, 
                                  bins=[0, 6, 12, 18, 24], 
                                  labels=['night', 'morning', 'afternoon', 'evening'],
                                  include_lowest=True)
        
        return df
    
    def _extract_macro_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取宏观时间特征"""
        time_series = df[self.time_col]
        
        # 日期特征
        df['day'] = time_series.dt.day
        df['month'] = time_series.dt.month
        df['year'] = time_series.dt.year
        df['dayofweek'] = time_series.dt.dayofweek
        df['dayofyear'] = time_series.dt.dayofyear
        df['weekofyear'] = time_series.dt.isocalendar().week
        
        # 季节特征
        df['season'] = ((time_series.dt.month % 12 + 3) // 3).map({
            1: 'spring', 2: 'summer', 3: 'autumn', 4: 'winter'
        })
        
        # 是否周末
        df['is_weekend'] = (time_series.dt.dayofweek >= 5).astype(int)
        
        return df
    
    def _extract_cyclical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取周期性特征 (正弦/余弦编码)"""
        time_series = df[self.time_col]
        
        # 小时的周期性编码
        df['hour_sin'] = np.sin(2 * np.pi * time_series.dt.hour / 24)
        df['hour_cos'] = np.cos(2 * np.pi * time_series.dt.hour / 24)
        
        # 一年中天数的周期性编码
        df['dayofyear_sin'] = np.sin(2 * np.pi * time_series.dt.dayofyear / 365.25)
        df['dayofyear_cos'] = np.cos(2 * np.pi * time_series.dt.dayofyear / 365.25)
        
        # 一周中天数的周期性编码
        df['dayofweek_sin'] = np.sin(2 * np.pi * time_series.dt.dayofweek / 7)
        df['dayofweek_cos'] = np.cos(2 * np.pi * time_series.dt.dayofweek / 7)
        
        # 月份的周期性编码
        df['month_sin'] = np.sin(2 * np.pi * time_series.dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * time_series.dt.month / 12)
        
        return df

def validate_time_alignment(df: pd.DataFrame, time_col: str = '时间') -> Dict[str, any]:
    """
    验证时间对齐的质量
    """
    validation_results = {}
    
    # 检查时间序列的连续性
    time_series = pd.to_datetime(df[time_col])
    time_diffs = time_series.diff().dropna()
    
    # 期望的时间间隔
    expected_interval = pd.Timedelta(minutes=15)
    
    # 计算时间间隔统计
    validation_results['expected_interval'] = expected_interval
    validation_results['actual_intervals'] = time_diffs.value_counts().head(10)
    validation_results['missing_intervals'] = (time_diffs > expected_interval).sum()
    validation_results['duplicate_times'] = df[time_col].duplicated().sum()
    validation_results['time_range'] = (time_series.min(), time_series.max())
    validation_results['total_records'] = len(df)
    
    return validation_results
