---
description: Unified visual maps for all modes
globs: all-mode-maps.mdc
alwaysApply: false
---
# 🗺️ ALL MODE MAPS

## 🔍 VAN MODE MAP

```mermaid
graph TD
    Start[VAN Mode] --> Check{Memory Bank?}
    Check -->|No| Create[Create Memory Bank]
    Check -->|Yes| Analyze[Analyze Task]
    Create --> Analyze
    
    Analyze --> Complexity{Assess Complexity}
    Complexity -->|L1| Quick[Quick Fix Path]
    Complexity -->|L2| Standard[Standard Path]
    Complexity -->|L3| Feature[Feature Path]
    Complexity -->|L4| Enterprise[Enterprise Path]
    
    Quick --> UpdateTasks[Update tasks.md]
    Standard --> UpdateTasks
    Feature --> UpdateTasks
    Enterprise --> UpdateTasks
    
    UpdateTasks --> NextMode[Proceed to Next Mode]
```

## 📋 PLAN MODE MAP

```mermaid
graph TD
    Start[PLAN Mode] --> ReadTasks[Read tasks.md]
    ReadTasks --> Strategy[Define Strategy]
    Strategy --> Breakdown[Break Down Tasks]
    Breakdown --> Dependencies[Identify Dependencies]
    Dependencies --> Timeline[Create Timeline]
    Timeline --> UpdatePlan[Update tasks.md]
    UpdatePlan --> NextMode[Proceed to CREATIVE/IMPLEMENT]
```

## 🎨 CREATIVE MODE MAP

```mermaid
graph TD
    Start[CREATIVE Mode] --> Problem[1️⃣ Define Problem]
    Problem --> Options[2️⃣ Generate Options]
    Options --> Analysis[3️⃣ Analyze Options]
    Analysis --> Decision[4️⃣ Make Decision]
    Decision --> Guidelines[5️⃣ Create Guidelines]
    Guidelines --> Document[Document in creative-*.md]
    Document --> NextMode[Proceed to IMPLEMENT]
```

## ⚙️ IMPLEMENT MODE MAP

```mermaid
graph TD
    Start[IMPLEMENT Mode] --> ReadPlan[Read Plan/Creative Docs]
    ReadPlan --> Setup[Setup Environment]
    Setup --> Code[Write/Modify Code]
    Code --> Test[Test Changes]
    Test --> Pass{Tests Pass?}
    Pass -->|No| Debug[Debug Issues]
    Pass -->|Yes| UpdateProgress[Update progress.md]
    Debug --> Code
    UpdateProgress --> NextMode[Proceed to REFLECT]
```

## 🔍 REFLECT MODE MAP

```mermaid
graph TD
    Start[REFLECT Mode] --> Review[Review Implementation]
    Review --> Verify[Verify Against Requirements]
    Verify --> Quality[Assess Quality]
    Quality --> Issues{Issues Found?}
    Issues -->|Yes| Document[Document Issues]
    Issues -->|No| Success[Mark Success]
    Document --> Backlog[Add to Backlog]
    Success --> UpdateTasks[Update tasks.md]
    Backlog --> UpdateTasks
    UpdateTasks --> NextMode[Proceed to ARCHIVE or Complete]
```

## 📚 ARCHIVE MODE MAP

```mermaid
graph TD
    Start[ARCHIVE Mode] --> Collect[Collect All Documentation]
    Collect --> Organize[Organize by Category]
    Organize --> Summary[Create Summary]
    Summary --> Knowledge[Extract Knowledge]
    Knowledge --> Store[Store in Archive]
    Store --> Cleanup[Cleanup Temporary Files]
    Cleanup --> Complete[Mark Complete]
```

## 🔄 UNIFIED WORKFLOW MAP

```mermaid
graph TD
    Task[New Task] --> VAN[VAN Mode]
    VAN --> Level{Complexity Level}
    
    Level -->|L1| L1Flow[VAN→IMPLEMENT→REFLECT]
    Level -->|L2| L2Flow[VAN→PLAN→IMPLEMENT→REFLECT]
    Level -->|L3| L3Flow[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT]
    Level -->|L4| L4Flow[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT→ARCHIVE]
    
    L1Flow --> Done[Task Complete]
    L2Flow --> Done
    L3Flow --> Done
    L4Flow --> Done
    
    Done --> NextTask{More Tasks?}
    NextTask -->|Yes| Task
    NextTask -->|No| End[All Complete]
```

## 📊 MODE TRANSITION MAP

```mermaid
graph TD
    Current[Current Mode] --> Check{Mode Complete?}
    Check -->|No| Continue[Continue Current Mode]
    Check -->|Yes| Next{Next Mode?}
    
    Next -->|VAN→PLAN| Plan[Switch to PLAN]
    Next -->|PLAN→CREATIVE| Creative[Switch to CREATIVE]
    Next -->|CREATIVE→IMPLEMENT| Implement[Switch to IMPLEMENT]
    Next -->|IMPLEMENT→REFLECT| Reflect[Switch to REFLECT]
    Next -->|REFLECT→ARCHIVE| Archive[Switch to ARCHIVE]
    Next -->|Complete| Done[Task Complete]
    
    Plan --> UpdateContext[Update activeContext.md]
    Creative --> UpdateContext
    Implement --> UpdateContext
    Reflect --> UpdateContext
    Archive --> UpdateContext
    
    UpdateContext --> NewMode[Start New Mode]
```

## 🚨 ERROR HANDLING MAP

```mermaid
graph TD
    Error[Error Detected] --> Type{Error Type}
    Type -->|Memory Bank| CreateMB[Create Memory Bank]
    Type -->|File Missing| CreateFile[Create Missing File]
    Type -->|Command Failed| Retry[Retry Command]
    Type -->|Logic Error| Escalate[Escalate Complexity]
    
    CreateMB --> Verify[Verify Fix]
    CreateFile --> Verify
    Retry --> Verify
    Escalate --> Verify
    
    Verify --> Success{Fixed?}
    Success -->|Yes| Continue[Continue Operation]
    Success -->|No| Abort[Abort and Report]
```
