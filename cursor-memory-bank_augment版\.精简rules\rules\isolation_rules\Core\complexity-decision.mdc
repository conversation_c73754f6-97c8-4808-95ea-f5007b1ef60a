---
description: Compact complexity decision tree
globs: complexity-decision.mdc
alwaysApply: false
---
# 🌳 COMPLEXITY DECISION

## 🎯 DECISION TREE

```mermaid
graph TD
    Start[Task] --> Time{Time Estimate?}
    Time -->|<30min| L1[Level 1: Quick Fix]
    Time -->|30min-2h| L2[Level 2: Enhancement]
    Time -->|2h-1day| L3[Level 3: Feature]
    Time -->|>1day| L4[Level 4: Enterprise]
    
    L1 --> Flow1[VAN→IMPLEMENT→REFLECT]
    L2 --> Flow2[VAN→PLAN→IMPLEMENT→REFLECT]
    L3 --> Flow3[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT]
    L4 --> Flow4[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT→ARCHIVE]
```

## 📊 COMPLEXITY INDICATORS

### Level 1: Quick Fix
- **Time**: <30 minutes
- **Scope**: Single file/function
- **Impact**: Low risk
- **Documentation**: Minimal
- **Examples**: Bug fix, typo correction, simple update

### Level 2: Enhancement
- **Time**: 30 minutes - 2 hours
- **Scope**: Multiple files/functions
- **Impact**: Medium risk
- **Documentation**: Standard
- **Examples**: Feature improvement, refactoring, optimization

### Level 3: Feature
- **Time**: 2 hours - 1 day
- **Scope**: Module/component
- **Impact**: High risk
- **Documentation**: Detailed
- **Examples**: New feature, API changes, architecture updates

### Level 4: Enterprise
- **Time**: >1 day
- **Scope**: System-wide
- **Impact**: Critical risk
- **Documentation**: Comprehensive
- **Examples**: Major refactoring, new system, breaking changes

## 🔄 ESCALATION RULES

```mermaid
graph TD
    Current[Current Level] --> Issues{Issues Found?}
    Issues -->|No| Continue[Continue Current Level]
    Issues -->|Yes| Assess{Complexity Increased?}
    Assess -->|No| Resolve[Resolve at Current Level]
    Assess -->|Yes| Escalate[Escalate to Next Level]
    Escalate --> NewFlow[Follow Higher Level Flow]
```

## 📝 QUICK ASSESSMENT

**Ask these questions:**
1. How many files will be modified?
2. Will this break existing functionality?
3. Does this require design decisions?
4. How long will testing take?
5. What's the rollback complexity?

**Scoring:**
- 1-2 Yes answers: Level 1
- 3-4 Yes answers: Level 2
- 5+ Yes answers: Level 3+
