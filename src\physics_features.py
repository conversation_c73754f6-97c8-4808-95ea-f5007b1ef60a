"""
物理约束特征工程系统 - 基于风力发电物理原理的特征创建
实现CREATIVE阶段设计的物理约束驱动特征工程
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import warnings

class WindPowerPhysicsFeatures:
    """
    基于风力发电物理原理的特征工程器
    集成风力发电的物理约束和领域知识
    """
    
    def __init__(self):
        # 风力发电机的典型参数 (可根据实际情况调整)
        self.cut_in_speed = 3.0      # 切入风速 (m/s)
        self.rated_speed = 12.0      # 额定风速 (m/s)
        self.cut_out_speed = 25.0    # 切出风速 (m/s)
        self.air_density_std = 1.225  # 标准空气密度 (kg/m³)
        self.hub_height = 80         # 轮毂高度 (m)
        
    def create_all_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建所有基于物理原理的特征
        """
        print("    开始传统物理特征工程...")
        df = df.copy()
        original_cols = len(df.columns)
        
        # 风速相关物理特征
        df = self._create_wind_speed_features(df)
        
        # 风向相关物理特征
        df = self._create_wind_direction_features(df)
        
        # 空气密度和温度相关特征
        df = self._create_air_density_features(df)
        
        # 风力发电功率曲线特征
        df = self._create_power_curve_features(df)
        
        # 湍流和稳定性特征
        df = self._create_turbulence_features(df)
        
        # 组合物理特征
        df = self._create_combined_physics_features(df)

        new_cols = len(df.columns)
        print(f"    传统物理特征工程完成，新增 {new_cols - original_cols} 个特征")

        return df
    
    def _create_wind_speed_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建风速相关的物理特征
        基于风力发电的风速-功率关系
        """
        # 基础风速特征
        wind_10m = df['wind_speed_10m']
        wind_80m = df['wind_speed_80m']
        
        # 风速立方关系 (风力发电功率与风速立方成正比)
        df['wind_speed_10m_cubed'] = np.power(wind_10m, 3)
        df['wind_speed_80m_cubed'] = np.power(wind_80m, 3)
        
        # 风速梯度 (不同高度风速差异)
        df['wind_speed_gradient'] = wind_80m - wind_10m
        df['wind_speed_ratio'] = np.where(wind_10m > 0, wind_80m / wind_10m, 0)
        
        # 风速剪切指数 (大气稳定性指标)
        height_ratio = 80 / 10  # 高度比
        df['wind_shear_exponent'] = np.where(
            wind_10m > 0,
            np.log(wind_80m / wind_10m) / np.log(height_ratio),
            0
        )
        
        # 有效风速 (考虑切入切出风速)
        df['effective_wind_10m'] = np.where(
            (wind_10m >= self.cut_in_speed) & (wind_10m <= self.cut_out_speed),
            wind_10m, 0
        )
        df['effective_wind_80m'] = np.where(
            (wind_80m >= self.cut_in_speed) & (wind_80m <= self.cut_out_speed),
            wind_80m, 0
        )
        
        # 风速区间分类
        df['wind_speed_category_10m'] = pd.cut(
            wind_10m,
            bins=[0, self.cut_in_speed, self.rated_speed, self.cut_out_speed, np.inf],
            labels=['below_cut_in', 'variable', 'rated', 'above_cut_out'],
            include_lowest=True
        )
        
        df['wind_speed_category_80m'] = pd.cut(
            wind_80m,
            bins=[0, self.cut_in_speed, self.rated_speed, self.cut_out_speed, np.inf],
            labels=['below_cut_in', 'variable', 'rated', 'above_cut_out'],
            include_lowest=True
        )
        
        return df
    
    def _create_wind_direction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建风向相关的物理特征
        """
        wind_dir_10m = df['wind_direction_10m']
        wind_dir_80m = df['wind_direction_80m']
        
        # 风向的正弦余弦编码 (处理角度的周期性)
        df['wind_dir_10m_sin'] = np.sin(np.radians(wind_dir_10m))
        df['wind_dir_10m_cos'] = np.cos(np.radians(wind_dir_10m))
        df['wind_dir_80m_sin'] = np.sin(np.radians(wind_dir_80m))
        df['wind_dir_80m_cos'] = np.cos(np.radians(wind_dir_80m))
        
        # 风向差异 (不同高度风向的一致性)
        wind_dir_diff = wind_dir_80m - wind_dir_10m
        # 处理角度差异的周期性 (-180 到 180)
        wind_dir_diff = ((wind_dir_diff + 180) % 360) - 180
        df['wind_direction_difference'] = wind_dir_diff
        
        # 风向一致性指标
        df['wind_direction_consistency'] = np.cos(np.radians(wind_dir_diff))
        
        # 主导风向分类 (8个方位)
        direction_bins = np.arange(0, 361, 45)
        direction_labels = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW']
        
        df['wind_sector_10m'] = pd.cut(
            wind_dir_10m, bins=direction_bins, labels=direction_labels, 
            include_lowest=True, right=False
        )
        df['wind_sector_80m'] = pd.cut(
            wind_dir_80m, bins=direction_bins, labels=direction_labels,
            include_lowest=True, right=False
        )
        
        return df
    
    def _create_air_density_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建空气密度相关特征
        空气密度影响风力发电机的功率输出
        """
        temp_2m = df['temperature_2m']
        apparent_temp = df['apparent_temperature']
        
        # 温度差异
        df['temperature_difference'] = temp_2m - apparent_temp
        
        # 空气密度估算 (简化公式，假设标准大气压)
        # ρ = ρ₀ * (T₀ / T)，其中T为绝对温度
        temp_kelvin = temp_2m + 273.15
        standard_temp_kelvin = 288.15  # 15°C
        
        df['estimated_air_density'] = self.air_density_std * (standard_temp_kelvin / temp_kelvin)
        
        # 密度修正的风速功率
        df['density_corrected_power_10m'] = (
            df['estimated_air_density'] / self.air_density_std * df['wind_speed_10m_cubed']
        )
        df['density_corrected_power_80m'] = (
            df['estimated_air_density'] / self.air_density_std * df['wind_speed_80m_cubed']
        )
        
        # 温度对发电效率的影响
        df['temperature_efficiency_factor'] = np.where(
            temp_2m < -10, 0.9,  # 极低温效率下降
            np.where(temp_2m > 40, 0.95,  # 高温效率轻微下降
                    1.0)  # 正常温度
        )
        
        return df
    
    def _create_power_curve_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建基于功率曲线的特征
        模拟典型风力发电机的功率曲线
        """
        wind_80m = df['wind_speed_80m']  # 使用轮毂高度风速
        
        # 理论功率曲线 (简化模型)
        def theoretical_power_curve(wind_speed):
            if wind_speed < self.cut_in_speed:
                return 0
            elif wind_speed < self.rated_speed:
                # 在切入风速到额定风速之间，功率与风速立方成正比
                return (wind_speed / self.rated_speed) ** 3
            elif wind_speed <= self.cut_out_speed:
                # 额定功率
                return 1.0
            else:
                # 超过切出风速，停机
                return 0
        
        df['theoretical_power_ratio'] = wind_80m.apply(theoretical_power_curve)
        
        # 功率曲线区间
        df['power_curve_region'] = pd.cut(
            wind_80m,
            bins=[0, self.cut_in_speed, self.rated_speed, self.cut_out_speed, np.inf],
            labels=['no_power', 'variable_power', 'rated_power', 'cut_out'],
            include_lowest=True
        )
        
        # 距离额定风速的差异
        df['distance_to_rated_speed'] = np.abs(wind_80m - self.rated_speed)
        
        return df
    
    def _create_turbulence_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建湍流和稳定性相关特征
        """
        wind_10m = df['wind_speed_10m']
        wind_80m = df['wind_speed_80m']
        wind_gusts = df['wind_gusts_10m']
        
        # 阵风系数 (湍流强度指标)
        df['gust_factor'] = np.where(wind_10m > 0, wind_gusts / wind_10m, 0)
        
        # 湍流强度估算
        df['turbulence_intensity'] = np.where(
            wind_10m > 0,
            (wind_gusts - wind_10m) / wind_10m,
            0
        )
        
        # 风速稳定性 (基于风速梯度)
        df['wind_stability'] = np.where(
            np.abs(df['wind_speed_gradient']) < 1.0, 'stable',
            np.where(df['wind_speed_gradient'] > 0, 'unstable', 'very_unstable')
        )
        
        return df
    
    def _create_combined_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建组合的物理特征
        """
        # 综合风力资源指标
        df['wind_resource_index'] = (
            df['density_corrected_power_80m'] * 
            df['wind_direction_consistency'] * 
            df['temperature_efficiency_factor']
        )
        
        # 发电适宜性指标
        df['generation_suitability'] = (
            (df['wind_speed_80m'] >= self.cut_in_speed) &
            (df['wind_speed_80m'] <= self.cut_out_speed) &
            (df['gust_factor'] < 2.0) &  # 湍流不太强
            (df['rain'] < 5.0)  # 降雨量不大
        ).astype(int)
        
        # 功率预期指标 (基于多个物理因素)
        df['power_expectation'] = (
            df['theoretical_power_ratio'] * 
            df['estimated_air_density'] / self.air_density_std *
            df['temperature_efficiency_factor'] *
            (1 - df['turbulence_intensity'] * 0.1)  # 湍流降低效率
        )
        
        return df

def validate_physics_features(df: pd.DataFrame) -> Dict[str, any]:
    """
    验证物理特征的合理性
    """
    validation_results = {}
    
    # 检查风速立方特征的范围
    if 'wind_speed_80m_cubed' in df.columns:
        cubed_stats = df['wind_speed_80m_cubed'].describe()
        validation_results['wind_speed_cubed_stats'] = cubed_stats
    
    # 检查空气密度的合理性
    if 'estimated_air_density' in df.columns:
        density_stats = df['estimated_air_density'].describe()
        validation_results['air_density_stats'] = density_stats
        validation_results['air_density_range_check'] = (
            (df['estimated_air_density'] >= 0.8) & 
            (df['estimated_air_density'] <= 1.5)
        ).all()
    
    # 检查阵风系数的合理性
    if 'gust_factor' in df.columns:
        gust_factor_stats = df['gust_factor'].describe()
        validation_results['gust_factor_stats'] = gust_factor_stats
        validation_results['gust_factor_reasonable'] = (
            df['gust_factor'] <= 5.0  # 阵风系数通常不超过5
        ).mean()
    
    # 检查功率预期的分布
    if 'power_expectation' in df.columns:
        power_expectation_stats = df['power_expectation'].describe()
        validation_results['power_expectation_stats'] = power_expectation_stats
    
    return validation_results
