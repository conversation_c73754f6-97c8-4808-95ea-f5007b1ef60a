---
type: "agent_requested"
---

# 🖥️ PLATFORM & CREATIVE

## 🔍 PLATFORM DETECTION

```mermaid
graph TD
    Start[Detect Platform] --> Win{Windows?}
    Win -->|Yes| WinCmds[Use Windows Commands]
    Win -->|No| Unix{Mac/Linux?}
    Unix -->|Yes| UnixCmds[Use Unix Commands]
    Unix -->|No| Generic[Use Generic Commands]
```

## 💻 PLATFORM-SPECIFIC PATHS

### Windows
- **Separator**: `\`
- **Home**: `%USERPROFILE%`
- **Temp**: `%TEMP%`
- **Current**: `%CD%`

### Mac/Linux
- **Separator**: `/`
- **Home**: `$HOME` or `~`
- **Temp**: `/tmp`
- **Current**: `$PWD`

## 🎨 CREATIVE PHASE (L3+)

```mermaid
graph TD
    Start[Creative Phase] --> P1[1️⃣ PROBLEM]
    P1 --> P2[2️⃣ OPTIONS]
    P2 --> P3[3️⃣ ANALYSIS]
    P3 --> P4[4️⃣ DECISION]
    P4 --> P5[5️⃣ GUIDELINES]
```

### Phase 1: PROBLEM
```markdown
## PROBLEM DEFINITION
- **Core Issue**: [What needs to be solved]
- **Constraints**: [Technical/business limitations]
- **Success Criteria**: [How to measure success]
```

### Phase 2: OPTIONS
```markdown
## SOLUTION OPTIONS
1. **Option A**: [Approach 1]
   - Pros: [Benefits]
   - Cons: [Drawbacks]

2. **Option B**: [Approach 2]
   - Pros: [Benefits]
   - Cons: [Drawbacks]
```

### Phase 3: ANALYSIS
```markdown
## ANALYSIS
- **Technical Feasibility**: [Assessment]
- **Resource Requirements**: [Time/effort needed]
- **Risk Assessment**: [Potential issues]
- **Impact Analysis**: [Effects on system]
```

### Phase 4: DECISION
```markdown
## DECISION
- **Chosen Approach**: [Selected option]
- **Rationale**: [Why this choice]
- **Trade-offs**: [What we're accepting]
```

### Phase 5: GUIDELINES
```markdown
## IMPLEMENTATION GUIDELINES
- **Architecture**: [High-level design]
- **Key Principles**: [Design principles to follow]
- **Quality Gates**: [Checkpoints for quality]
```

## 🔄 ADAPTIVE CREATIVE PROCESS

### Level 3: Feature
- Use all 5 phases
- Detailed analysis required
- Document all decisions

### Level 4: Enterprise
- Use all 5 phases + extended analysis
- Multiple stakeholder input
- Comprehensive documentation
- Risk mitigation plans

## 📝 CREATIVE TEMPLATES

### Quick Decision (L3)
```markdown
# CREATIVE DECISION: [Feature Name]

## PROBLEM
[Brief problem statement]

## OPTIONS
1. [Option 1] - [Brief description]
2. [Option 2] - [Brief description]

## DECISION
**Chosen**: [Selected option]
**Why**: [Brief rationale]

## GUIDELINES
- [Key principle 1]
- [Key principle 2]
```

### Comprehensive Decision (L4)
Uses full 5-phase template with extended analysis sections.
