"""
时序增强特征模块
基于深度误差分析结果生成时序增强特征
专门解决误差自相关性和时间依赖性问题
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
from scipy import stats

warnings.filterwarnings('ignore')

class TemporalEnhancedFeatures:
    """
    时序增强特征生成器
    
    基于深度误差分析结果生成时序特征：
    1. 自相关特征处理（解决0.93的高自相关系数）
    2. 时间模式特征（解决时段差异问题）
    3. 误差去相关特征
    4. 时序稳定性特征
    """
    
    def __init__(self, time_col: str = '时间'):
        """
        初始化时序增强特征生成器
        
        Args:
            time_col: 时间列名
        """
        self.time_col = time_col
        
        # 基于深度误差分析的参数
        self.error_autocorr_coeff = 0.93  # 误差自相关系数
        self.high_error_hours = [1, 19, 0]  # 误差最大的时段
        self.low_error_hours = [6, 4, 13]   # 误差最小的时段
        self.max_consecutive_error_length = 88  # 最长连续同向误差
        self.avg_consecutive_error_length = 9.11  # 平均连续同向误差长度
        
        print("时序增强特征生成器初始化完成")
        print(f"误差自相关系数: {self.error_autocorr_coeff}")
        print(f"高误差时段: {self.high_error_hours}")
        print(f"低误差时段: {self.low_error_hours}")
    
    def generate_all_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成所有时序增强特征
        
        Args:
            df: 输入数据框，必须包含时间列
            
        Returns:
            添加时序增强特征的数据框
        """
        print("开始生成时序增强特征...")
        
        df_result = df.copy()
        original_cols = len(df_result.columns)
        
        # 确保时间列为datetime类型
        if self.time_col in df_result.columns:
            df_result[self.time_col] = pd.to_datetime(df_result[self.time_col])
            df_result = df_result.sort_values(self.time_col).reset_index(drop=True)
        else:
            print(f"警告：未找到时间列 {self.time_col}")
            return df_result
        
        # 1. 自相关特征处理
        df_result = self._generate_autocorr_features(df_result)
        
        # 2. 时间模式特征
        df_result = self._generate_time_pattern_features(df_result)
        
        # 3. 误差去相关特征
        df_result = self._generate_decorrelation_features(df_result)
        
        # 4. 时序稳定性特征
        df_result = self._generate_stability_features(df_result)
        
        # 5. 周期性增强特征
        df_result = self._generate_cyclical_features(df_result)
        
        new_cols = len(df_result.columns)
        print(f"时序增强特征生成完成，新增 {new_cols - original_cols} 个特征")
        
        return df_result
    
    def _generate_autocorr_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成自相关特征"""
        print("  生成自相关特征...")
        
        # 基于误差自相关系数生成滞后特征
        lag_periods = [1, 2, 3, 4, 6, 8, 12, 24]  # 不同滞后期
        
        # 对主要特征生成滞后特征
        main_features = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m']
        
        for feature in main_features:
            if feature in df.columns:
                for lag in lag_periods:
                    # 滞后特征
                    df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)
                    
                    # 滞后差分特征
                    df[f'{feature}_lag_diff_{lag}'] = df[feature] - df[feature].shift(lag)
                    
                    # 滞后比率特征
                    df[f'{feature}_lag_ratio_{lag}'] = np.where(
                        df[feature].shift(lag) != 0,
                        df[feature] / df[feature].shift(lag),
                        1.0
                    )
        
        # 自相关强度特征
        df['autocorr_strength'] = self.error_autocorr_coeff
        
        # 基于自相关的权重特征
        for i, lag in enumerate(lag_periods[:4]):  # 只对前4个滞后期
            weight = self.error_autocorr_coeff ** lag  # 指数衰减权重
            df[f'autocorr_weight_{lag}'] = weight
        
        return df
    
    def _generate_time_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成时间模式特征"""
        print("  生成时间模式特征...")
        
        # 提取时间组件
        df['hour'] = df[self.time_col].dt.hour
        df['minute'] = df[self.time_col].dt.minute
        df['day_of_week'] = df[self.time_col].dt.dayofweek
        df['day_of_year'] = df[self.time_col].dt.dayofyear
        
        # 高误差时段特征
        df['is_high_error_hour'] = df['hour'].isin(self.high_error_hours).astype(int)
        df['is_low_error_hour'] = df['hour'].isin(self.low_error_hours).astype(int)
        
        # 误差时段强度
        hour_error_intensity = {
            1: 1.0, 19: 0.95, 0: 0.90,  # 高误差时段
            6: 0.1, 4: 0.15, 13: 0.20   # 低误差时段
        }
        df['hour_error_intensity'] = df['hour'].map(hour_error_intensity).fillna(0.5)
        
        # 夜间和傍晚特征（基于误差分析）
        df['is_night'] = ((df['hour'] >= 22) | (df['hour'] <= 6)).astype(int)
        df['is_evening'] = ((df['hour'] >= 18) & (df['hour'] <= 21)).astype(int)
        df['is_dawn'] = ((df['hour'] >= 5) & (df['hour'] <= 8)).astype(int)
        
        # 时间周期性特征（正弦余弦编码）
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['minute_sin'] = np.sin(2 * np.pi * df['minute'] / 60)
        df['minute_cos'] = np.cos(2 * np.pi * df['minute'] / 60)
        
        # 基于误差模式的时间权重
        df['time_error_weight'] = 1.0 + (df['hour_error_intensity'] - 0.5) * 0.5
        
        return df
    
    def _generate_decorrelation_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成误差去相关特征"""
        print("  生成误差去相关特征...")
        
        # 移动平均特征（平滑时序依赖性）
        windows = [3, 6, 12, 24]
        main_features = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m']
        
        for feature in main_features:
            if feature in df.columns:
                for window in windows:
                    # 移动平均
                    df[f'{feature}_ma_{window}'] = df[feature].rolling(window=window, min_periods=1).mean()
                    
                    # 移动标准差
                    df[f'{feature}_std_{window}'] = df[feature].rolling(window=window, min_periods=1).std()
                    
                    # 与移动平均的偏差
                    df[f'{feature}_ma_dev_{window}'] = df[feature] - df[f'{feature}_ma_{window}']
                    
                    # 标准化偏差
                    df[f'{feature}_ma_dev_norm_{window}'] = np.where(
                        df[f'{feature}_std_{window}'] > 0,
                        df[f'{feature}_ma_dev_{window}'] / df[f'{feature}_std_{window}'],
                        0
                    )
        
        # 去趋势特征
        for feature in main_features:
            if feature in df.columns:
                # 线性去趋势
                x = np.arange(len(df))
                if len(df) > 1:
                    slope, intercept, _, _, _ = stats.linregress(x, df[feature].fillna(df[feature].mean()))
                    trend = slope * x + intercept
                    df[f'{feature}_detrended'] = df[feature] - trend
                else:
                    df[f'{feature}_detrended'] = df[feature]
        
        return df
    
    def _generate_stability_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成时序稳定性特征"""
        print("  生成时序稳定性特征...")
        
        # 连续性特征（基于最长连续误差88个点的发现）
        main_features = ['wind_speed_80m', 'wind_speed_10m']
        
        for feature in main_features:
            if feature in df.columns:
                # 变化率
                df[f'{feature}_change_rate'] = df[feature].pct_change().fillna(0)
                
                # 稳定性指标（变化率的标准差）
                df[f'{feature}_stability'] = df[f'{feature}_change_rate'].rolling(
                    window=12, min_periods=1
                ).std().fillna(0)
                
                # 持续性指标
                df[f'{feature}_persistence'] = df[feature].rolling(
                    window=6, min_periods=1
                ).apply(lambda x: len(x) - len(set(np.round(x, 1))))
        
        # 基于连续误差长度的特征
        df['consecutive_error_risk'] = np.where(
            df['hour_error_intensity'] > 0.7,
            self.avg_consecutive_error_length / self.max_consecutive_error_length,
            0.1
        )
        
        # 时序一致性特征
        if 'wind_speed_80m' in df.columns and 'wind_speed_10m' in df.columns:
            # 风速一致性
            df['wind_speed_consistency'] = 1 - abs(
                df['wind_speed_80m'].pct_change() - df['wind_speed_10m'].pct_change()
            ).fillna(0)
            
            # 风速梯度稳定性
            wind_gradient = df['wind_speed_80m'] - df['wind_speed_10m']
            df['wind_gradient_stability'] = 1 - abs(wind_gradient.pct_change()).fillna(0)
        
        return df
    
    def _generate_cyclical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成周期性增强特征"""
        print("  生成周期性增强特征...")
        
        # 多尺度周期性特征
        # 15分钟周期（4个点为1小时）
        df['quarter_hour_cycle'] = (df.index % 4) / 4
        
        # 小时周期（基于误差分析的24小时模式）
        df['hour_cycle_error_adjusted'] = np.sin(
            2 * np.pi * df['hour'] / 24 + np.pi * df['hour_error_intensity']
        )
        
        # 日周期（96个点为1天）
        df['daily_cycle'] = (df.index % 96) / 96
        
        # 周周期
        df['weekly_cycle'] = df['day_of_week'] / 7
        
        # 季节周期
        df['seasonal_cycle'] = df['day_of_year'] / 365.25
        
        # 复合周期特征
        df['hour_minute_interaction'] = df['hour_sin'] * df['minute_sin']
        df['daily_seasonal_interaction'] = df['daily_cycle'] * df['seasonal_cycle']
        
        # 基于误差模式的周期调整
        df['error_adjusted_daily_cycle'] = df['daily_cycle'] * (1 + df['hour_error_intensity'] * 0.2)
        
        return df
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """获取特征重要性分组"""
        return {
            'core_temporal_features': [
                'hour_error_intensity',
                'time_error_weight',
                'autocorr_strength',
                'consecutive_error_risk'
            ],
            'autocorr_features': [
                'wind_speed_80m_lag_1',
                'wind_speed_80m_lag_2',
                'wind_speed_80m_lag_4',
                'autocorr_weight_1'
            ],
            'time_pattern_features': [
                'is_high_error_hour',
                'is_low_error_hour',
                'is_night',
                'is_evening'
            ],
            'decorrelation_features': [
                'wind_speed_80m_ma_6',
                'wind_speed_80m_ma_dev_12',
                'wind_speed_80m_detrended'
            ],
            'stability_features': [
                'wind_speed_80m_stability',
                'wind_speed_consistency',
                'wind_gradient_stability'
            ],
            'cyclical_features': [
                'hour_sin',
                'hour_cos',
                'hour_cycle_error_adjusted',
                'error_adjusted_daily_cycle'
            ]
        }
