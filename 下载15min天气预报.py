import openmeteo_requests
import pandas as pd
import requests_cache
from retry_requests import retry
import sys
from pathlib import Path
import requests
import datetime

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

# 导入配置管理器
from config_manager import ConfigManager

def download_weather_forecast_for_location(location_id, latitude, longitude, openmeteo, current_time):
    """
    为指定点位下载天气预报

    Args:
        location_id: 点位ID (如 'c', '1', '2', '3', '4')
        latitude: 纬度
        longitude: 经度
        openmeteo: API客户端
        current_time: 当前时间字符串

    Returns:
        bool: 下载是否成功
    """
    try:
        print(f"\n正在下载点位{location_id}的天气预报...")
        print(f"  坐标: {latitude}°N {longitude}°E")

        # API参数
        url = "https://api.open-meteo.com/v1/forecast"
        params = {
            "latitude": latitude,
            "longitude": longitude,
            "minutely_15": ["wind_speed_10m", "wind_speed_80m", "wind_direction_10m",
                           "wind_direction_80m", "wind_gusts_10m", "temperature_2m",
                           "rain", "apparent_temperature"],
            "timezone": "Asia/Singapore",
            "wind_speed_unit": "ms",
            "forecast_days": 4,
            "past_days": 30
        }

        # 请求数据
        responses = openmeteo.weather_api(url, params=params)
        response = responses[0]

        print(f"  海拔: {response.Elevation()} m")
        print(f"  时区: {response.Timezone()}{response.TimezoneAbbreviation()}")

        # 处理15分钟数据
        minutely_15 = response.Minutely15()
        minutely_15_wind_speed_10m = minutely_15.Variables(0).ValuesAsNumpy()
        minutely_15_wind_speed_80m = minutely_15.Variables(1).ValuesAsNumpy()
        minutely_15_wind_direction_10m = minutely_15.Variables(2).ValuesAsNumpy()
        minutely_15_wind_direction_80m = minutely_15.Variables(3).ValuesAsNumpy()
        minutely_15_wind_gusts_10m = minutely_15.Variables(4).ValuesAsNumpy()
        minutely_15_temperature_2m = minutely_15.Variables(5).ValuesAsNumpy()
        minutely_15_rain = minutely_15.Variables(6).ValuesAsNumpy()
        minutely_15_apparent_temperature = minutely_15.Variables(7).ValuesAsNumpy()

        # 构建数据框
        minutely_15_data = {"date": pd.date_range(
            start = pd.to_datetime(minutely_15.Time(), unit = "s", utc = True),
            end = pd.to_datetime(minutely_15.TimeEnd(), unit = "s", utc = True),
            freq = pd.Timedelta(seconds = minutely_15.Interval()),
            inclusive = "left"
        )}

        minutely_15_data["wind_speed_10m"] = minutely_15_wind_speed_10m
        minutely_15_data["wind_speed_80m"] = minutely_15_wind_speed_80m
        minutely_15_data["wind_direction_10m"] = minutely_15_wind_direction_10m
        minutely_15_data["wind_direction_80m"] = minutely_15_wind_direction_80m
        minutely_15_data["wind_gusts_10m"] = minutely_15_wind_gusts_10m
        minutely_15_data["temperature_2m"] = minutely_15_temperature_2m
        minutely_15_data["rain"] = minutely_15_rain
        minutely_15_data["apparent_temperature"] = minutely_15_apparent_temperature

        minutely_15_dataframe = pd.DataFrame(data = minutely_15_data)

        # 数据处理：除了date列之外，全部下移32行
        minutely_15_dataframe.iloc[:, 1:] = minutely_15_dataframe.iloc[:, 1:].shift(32)
        # 删除前32行
        minutely_15_dataframe = minutely_15_dataframe.iloc[32:]
        # date重命名为时间
        minutely_15_dataframe.rename(columns = {"date": "时间"}, inplace = True)

        # 保存文件，文件名格式：点位{location_id}_15min_预报_{时间}.csv
        filename = f"点位{location_id}_15min_预报_{current_time}.csv"
        filepath = Path("data/天气预报") / filename

        # 确保目录存在
        filepath.parent.mkdir(parents=True, exist_ok=True)

        # 保存文件
        minutely_15_dataframe.to_csv(filepath, index=False)

        print(f"  ✅ 保存成功: {filename}")
        print(f"  数据条数: {len(minutely_15_dataframe)}")

        return True

    except Exception as e:
        print(f"  ❌ 下载失败: {e}")
        return False

def main():
    """主函数：下载所有点位的天气预报"""
    print("=" * 60)
    print("多点位15分钟天气预报下载器")
    print("=" * 60)

    try:
        # 加载配置
        config = ConfigManager()

        # 获取多点位配置
        multi_location_config = config.get('multi_location', {})
        if not multi_location_config.get('enable', False):
            print("❌ 多点位模式未启用，请检查配置文件")
            return False

        locations = multi_location_config.get('locations', {})
        if not locations:
            print("❌ 未找到点位配置")
            return False

        print(f"发现 {len(locations)} 个点位配置:")
        for loc_id, coords in locations.items():
            print(f"  点位{loc_id}: {coords[0]}°N {coords[1]}°E")

        # 设置API客户端
        cache_session = requests_cache.CachedSession('.cache', expire_after = 3600)
        retry_session = retry(cache_session, retries = 5, backoff_factor = 0.2)
        openmeteo = openmeteo_requests.Client(session = retry_session)

        # 生成时间戳
        current_time = pd.Timestamp.now().strftime("%Y%m%d%H%M")

        # 下载统计
        success_count = 0
        total_count = len(locations)

        # 遍历所有点位下载天气预报
        for loc_id, coords in locations.items():
            latitude, longitude = coords
            success = download_weather_forecast_for_location(
                loc_id, latitude, longitude, openmeteo, current_time
            )
            if success:
                success_count += 1

        # 输出结果
        print("\n" + "=" * 60)
        print("下载完成!")
        print(f"成功: {success_count}/{total_count} 个点位")

        if success_count == total_count:
            print("✅ 所有点位天气预报下载成功!")
        elif success_count > 0:
            print("⚠️ 部分点位下载成功")
        else:
            print("❌ 所有点位下载失败")

        print("=" * 60)
        return success_count == total_count

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return False

#########################################下载多网站########################################
url_天气预报 = 'https://api.open-meteo.com/v1/forecast?latitude=40.75&longitude=95.75&models=gfs_seamless,jma_seamless,kma_seamless,ecmwf_ifs025,gfs_global,jma_msm,kma_ldps,kma_gdps,jma_gsm,gfs_hrrr,ecmwf_aifs025_single,cma_grapes_global,ncep_nbm_conus,gfs_graphcast025,bom_access_global,icon_seamless,gem_seamless,italia_meteo_arpae_icon_2i,meteofrance_seamless,icon_global,gem_global,meteofrance_arpege_world,knmi_seamless,ukmo_seamless,ukmo_global_deterministic_10km,ukmo_uk_deterministic_2km,dmi_seamless,gem_hrdps_continental&minutely_15=wind_speed_10m,wind_speed_80m,wind_gusts_10m&timezone=Asia%2FSingapore&past_days=30&forecast_days=4&wind_speed_unit=ms&format=csv'
columns_to_keep = [
        '时间',
        'wind_speed_10m_gfs_seamless (m/s)',
        'wind_speed_80m_gfs_seamless (m/s)',
        'wind_gusts_10m_gfs_seamless (m/s)',
        'wind_speed_10m_jma_seamless (m/s)',
        'wind_speed_10m_gfs_global (m/s)',
        'wind_speed_80m_gfs_global (m/s)',
        'wind_gusts_10m_gfs_global (m/s)',
        'wind_speed_10m_jma_gsm (m/s)',
        'wind_speed_10m_cma_grapes_global (m/s)',
        'wind_speed_80m_cma_grapes_global (m/s)',
        'wind_gusts_10m_cma_grapes_global (m/s)',
        'wind_speed_10m_icon_seamless (m/s)',
        'wind_speed_80m_icon_seamless (m/s)',
        'wind_gusts_10m_icon_seamless (m/s)',
        'wind_speed_10m_gem_seamless (m/s)',
        'wind_speed_80m_gem_seamless (m/s)',
        'wind_gusts_10m_gem_seamless (m/s)',
        'wind_speed_10m_meteofrance_seamless (m/s)',
        'wind_speed_80m_meteofrance_seamless (m/s)',
        'wind_gusts_10m_meteofrance_seamless (m/s)',
        'wind_speed_10m_icon_global (m/s)',
        'wind_speed_80m_icon_global (m/s)',
        'wind_gusts_10m_icon_global (m/s)',
        'wind_speed_10m_gem_global (m/s)',
        'wind_speed_80m_gem_global (m/s)',
        'wind_gusts_10m_gem_global (m/s)',
        'wind_speed_10m_meteofrance_arpege_world (m/s)',
        'wind_speed_80m_meteofrance_arpege_world (m/s)',
        'wind_gusts_10m_meteofrance_arpege_world (m/s)',
        'wind_speed_10m_ukmo_seamless (m/s)',
        'wind_gusts_10m_ukmo_seamless (m/s)',
        'wind_speed_10m_ukmo_global_deterministic_10km (m/s)',
        'wind_gusts_10m_ukmo_global_deterministic_10km (m/s)'
    ]

def download_multi_forecast(url, save_path):
    response = requests.get(url)
    with open(save_path, 'wb') as f:
        f.write(response.content)
        print(f'✅ 天气预报数据已保存到 {save_path}')
        #跳过前3条
        df = pd.read_csv(save_path, skiprows=3)
        #重命名时间列
        df.rename(columns={'time': '时间'}, inplace=True)
        df = df[columns_to_keep]
        df.to_csv(save_path, index=False)
        print(f'数据形状: {df.shape}')

if __name__ == "__main__":
    main()
    print("开始下载多网站数据")
    now = datetime.datetime.now()
    current_time = now.strftime("%Y%m%d%H%M%S")
    save_path = f'data/天气预报/点位c_多网站_天气预报_{current_time}.csv'
    download_multi_forecast(url_天气预报, save_path)