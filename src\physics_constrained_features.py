"""
物理约束特征生成器
基于风机参数和深度误差分析结果生成物理约束特征
解决系统性偏差和时间依赖性问题
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
from .turbine_parameters import TurbineParameterProcessor

warnings.filterwarnings('ignore')

class PhysicsConstrainedFeatureGenerator:
    """
    物理约束特征生成器
    
    基于真实风机参数生成物理约束特征：
    1. 多风机类型理论功率特征
    2. 物理约束验证特征
    3. 误差模式特征
    4. 系统偏差校正特征
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化物理约束特征生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        
        # 初始化风机参数处理器
        self.turbine_processor = TurbineParameterProcessor(config_path)
        
        # 获取风机数量
        self.huarui_count, self.goldwind_count = self.turbine_processor.get_turbine_counts()
        self.total_turbines = self.huarui_count + self.goldwind_count
        
        # 物理常数
        self.air_density_std = 1.225  # 标准空气密度 kg/m³
        self.hub_height = 70  # 轮毂高度 m (根据用户提供信息)
        
        # 基于深度误差分析的系统偏差校正参数
        self.systematic_bias = -3.8536  # MW，模型平均高估
        self.error_autocorr_coeff = 0.93  # 误差自相关系数
        
        print("物理约束特征生成器初始化完成")
        print(f"风机配置: 华锐{self.huarui_count}台 + 金风{self.goldwind_count}台 = 总计{self.total_turbines}台")
    
    def generate_all_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成所有物理约束特征
        
        Args:
            df: 输入数据框，包含气象数据
            
        Returns:
            添加物理约束特征的数据框
        """
        print("开始生成物理约束特征...")
        
        df_result = df.copy()
        original_cols = len(df_result.columns)
        
        # 1. 多风机理论功率特征
        df_result = self._generate_turbine_power_features(df_result)
        
        # 2. 物理约束验证特征
        df_result = self._generate_physics_validation_features(df_result)
        
        # 3. 误差模式特征
        df_result = self._generate_error_pattern_features(df_result)
        
        # 4. 系统偏差校正特征
        df_result = self._generate_bias_correction_features(df_result)
        
        # 5. 风机类型差异特征
        df_result = self._generate_turbine_difference_features(df_result)
        
        new_cols = len(df_result.columns)
        print(f"物理约束特征生成完成，新增 {new_cols - original_cols} 个特征")
        
        return df_result
    
    def _generate_turbine_power_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成多风机理论功率特征"""
        print("  生成多风机理论功率特征...")
        
        # 使用80m风速作为主要输入
        wind_speeds = df['wind_speed_80m'].values
        
        # 华锐科技风机理论功率
        huarui_single_power = self.turbine_processor.get_huarui_power(wind_speeds)
        df['huarui_theoretical_power'] = huarui_single_power * self.huarui_count
        
        # 金风科技风机理论功率
        goldwind_single_power = self.turbine_processor.get_goldwind_power(wind_speeds)
        df['goldwind_theoretical_power'] = goldwind_single_power * self.goldwind_count
        
        # 总理论功率
        df['total_theoretical_power'] = df['huarui_theoretical_power'] + df['goldwind_theoretical_power']
        
        # 功率系数特征
        huarui_cp = self.turbine_processor.get_huarui_power_coefficient(wind_speeds)
        goldwind_cp = self.turbine_processor.get_goldwind_power_coefficient(wind_speeds)
        
        df['huarui_power_coefficient'] = huarui_cp
        df['goldwind_power_coefficient'] = goldwind_cp
        df['weighted_power_coefficient'] = (
            huarui_cp * self.huarui_count + goldwind_cp * self.goldwind_count
        ) / self.total_turbines
        
        # 推力系数特征
        huarui_ct = self.turbine_processor.get_huarui_thrust_coefficient(wind_speeds)
        goldwind_ct = self.turbine_processor.get_goldwind_thrust_coefficient(wind_speeds)
        
        df['huarui_thrust_coefficient'] = huarui_ct
        df['goldwind_thrust_coefficient'] = goldwind_ct
        df['weighted_thrust_coefficient'] = (
            huarui_ct * self.huarui_count + goldwind_ct * self.goldwind_count
        ) / self.total_turbines
        
        return df
    
    def _generate_physics_validation_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成物理约束验证特征"""
        print("  生成物理约束验证特征...")
        
        wind_speeds = df['wind_speed_80m'].values
        
        # 空气密度修正（基于温度）
        if 'temperature_2m' in df.columns:
            # 理想气体定律：ρ = ρ₀ * (T₀/T)
            temp_kelvin = df['temperature_2m'] + 273.15
            standard_temp = 288.15  # 15°C
            df['air_density_corrected'] = self.air_density_std * (standard_temp / temp_kelvin)
        else:
            df['air_density_corrected'] = self.air_density_std
        
        # 密度修正的理论功率
        density_ratio = df['air_density_corrected'] / self.air_density_std
        df['density_corrected_theoretical_power'] = df['total_theoretical_power'] * density_ratio
        
        # 风速有效性指标
        df['wind_speed_effectiveness'] = np.where(
            (wind_speeds >= 3) & (wind_speeds <= 25),  # 有效风速范围
            1.0,
            np.where(wind_speeds < 3, 0.0, 0.5)  # 切出风速时部分有效
        )
        
        # 湍流强度影响
        if 'wind_gusts_10m' in df.columns and 'wind_speed_10m' in df.columns:
            df['turbulence_intensity'] = np.where(
                df['wind_speed_10m'] > 0,
                (df['wind_gusts_10m'] - df['wind_speed_10m']) / df['wind_speed_10m'],
                0
            )
            # 湍流对功率的影响（湍流会降低功率输出）
            df['turbulence_power_loss'] = np.clip(df['turbulence_intensity'] * 0.1, 0, 0.3)
            df['turbulence_corrected_power'] = df['density_corrected_theoretical_power'] * (1 - df['turbulence_power_loss'])
        else:
            df['turbulence_corrected_power'] = df['density_corrected_theoretical_power']
        
        return df
    
    def _generate_error_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成误差模式特征"""
        print("  生成误差模式特征...")
        
        # 基于深度误差分析的时间模式特征
        if '时间' in df.columns:
            df['时间'] = pd.to_datetime(df['时间'])
            df['hour'] = df['时间'].dt.hour
            df['minute'] = df['时间'].dt.minute
            
            # 高误差时段标识（基于误差分析结果）
            high_error_hours = [1, 19, 0]  # 误差最大的时段
            low_error_hours = [6, 4, 13]   # 误差最小的时段
            
            df['high_error_period'] = df['hour'].isin(high_error_hours).astype(int)
            df['low_error_period'] = df['hour'].isin(low_error_hours).astype(int)
            
            # 时间依赖性特征（基于自相关系数0.93）
            df['time_dependency_factor'] = np.sin(2 * np.pi * df['hour'] / 24) * self.error_autocorr_coeff
            
            # 夜间和傍晚特殊处理（误差分析显示这些时段误差较大）
            df['night_evening_factor'] = np.where(
                (df['hour'] >= 18) | (df['hour'] <= 6),
                1.2,  # 夜间和傍晚增加不确定性
                1.0
            )
        
        return df
    
    def _generate_bias_correction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成系统偏差校正特征"""
        print("  生成系统偏差校正特征...")
        
        # 系统偏差校正
        df['systematic_bias_correction'] = self.systematic_bias
        df['bias_corrected_theoretical_power'] = df['turbulence_corrected_power'] + df['systematic_bias_correction']
        
        # 基于功率范围的偏差校正（误差分析显示不同功率范围有不同误差特征）
        power_ranges = [
            (0, 10, 0.95),    # 0-10MW范围，校正系数0.95
            (10, 30, 0.92),   # 10-30MW范围，校正系数0.92
            (30, 60, 0.88),   # 30-60MW范围，校正系数0.88
            (60, 100, 0.85),  # 60-100MW范围，校正系数0.85
            (100, 1000, 0.90) # >100MW范围，校正系数0.90
        ]
        
        df['power_range_correction'] = 1.0
        for min_power, max_power, correction_factor in power_ranges:
            mask = (df['turbulence_corrected_power'] >= min_power) & (df['turbulence_corrected_power'] < max_power)
            df.loc[mask, 'power_range_correction'] = correction_factor
        
        df['range_corrected_theoretical_power'] = df['bias_corrected_theoretical_power'] * df['power_range_correction']
        
        # 非正态分布校正（误差分析显示误差呈尖峰右偏分布）
        df['distribution_skew_correction'] = np.where(
            df['range_corrected_theoretical_power'] > 100,
            0.98,  # 高功率时向下校正
            1.02   # 低功率时向上校正
        )
        
        df['final_corrected_theoretical_power'] = df['range_corrected_theoretical_power'] * df['distribution_skew_correction']
        
        return df
    
    def _generate_turbine_difference_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成风机类型差异特征"""
        print("  生成风机类型差异特征...")
        
        # 两种风机的功率差异
        df['turbine_power_difference'] = abs(df['huarui_theoretical_power'] - df['goldwind_theoretical_power'])
        df['turbine_power_ratio'] = np.where(
            df['goldwind_theoretical_power'] > 0,
            df['huarui_theoretical_power'] / df['goldwind_theoretical_power'],
            1.0
        )
        
        # 功率系数差异
        df['power_coefficient_difference'] = abs(df['huarui_power_coefficient'] - df['goldwind_power_coefficient'])
        
        # 推力系数差异
        df['thrust_coefficient_difference'] = abs(df['huarui_thrust_coefficient'] - df['goldwind_thrust_coefficient'])
        
        # 风机类型优势指标
        wind_speeds = df['wind_speed_80m'].values
        df['huarui_advantage'] = np.where(
            (wind_speeds >= 3) & (wind_speeds <= 8),  # 华锐在低风速时可能有优势
            df['huarui_power_coefficient'] / (df['weighted_power_coefficient'] + 1e-6),
            1.0
        )
        
        df['goldwind_advantage'] = np.where(
            (wind_speeds >= 8) & (wind_speeds <= 15),  # 金风在中高风速时可能有优势
            df['goldwind_power_coefficient'] / (df['weighted_power_coefficient'] + 1e-6),
            1.0
        )
        
        # 综合风机效率指标
        df['combined_efficiency'] = (
            df['huarui_advantage'] * self.huarui_count + 
            df['goldwind_advantage'] * self.goldwind_count
        ) / self.total_turbines
        
        return df
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """获取特征重要性分组"""
        return {
            'core_turbine_features': [
                'total_theoretical_power',
                'final_corrected_theoretical_power',
                'weighted_power_coefficient',
                'weighted_thrust_coefficient'
            ],
            'bias_correction_features': [
                'systematic_bias_correction',
                'power_range_correction',
                'distribution_skew_correction'
            ],
            'error_pattern_features': [
                'high_error_period',
                'low_error_period',
                'time_dependency_factor',
                'night_evening_factor'
            ],
            'turbine_difference_features': [
                'turbine_power_difference',
                'power_coefficient_difference',
                'combined_efficiency'
            ],
            'physics_validation_features': [
                'air_density_corrected',
                'wind_speed_effectiveness',
                'turbulence_power_loss'
            ]
        }
