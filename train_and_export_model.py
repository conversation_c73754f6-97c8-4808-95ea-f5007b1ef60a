"""
训练并导出模型 - 生产环境版本
一次性训练，导出模型和特征工程管道，用于每日预测
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import joblib
from datetime import datetime
import shutil
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from src.data_loader import WindPowerDataLoader
from src.time_alignment import MultiScaleTimeFeatures
from src.physics_features import WindPowerPhysicsFeatures
from src.lightgbm_optimizer import PhysicsConstrainedLightGBM, LightGBMHyperOptimizer
from src.config_manager import ConfigManager
from src.ensemble_trainer import EnsembleTrainer
from src.comprehensive_feature_engine import ComprehensiveFeatureEngine
from src.advanced_loss_functions import AdvancedLossFunctions
from src.feature_selector import FeatureSelector
from src.multi_location_fusion import MultiLocationDataFusion
from src.spatial_feature_engine import SpatialFeatureEngine
from src.integrated_feature_engineer import IntegratedFeatureEngineer
from sklearn.preprocessing import LabelEncoder

class ProductionModelPipeline:
    """
    生产环境模型管道
    包含完整的特征工程和模型预测流程
    """
    
    def __init__(self):
        self.config = ConfigManager()
        self.time_feature_extractor = MultiScaleTimeFeatures()
        self.physics_feature_creator = WindPowerPhysicsFeatures()
        self.label_encoders = {}
        self.model = None
        self.feature_columns = None
        self.target_column = self.config.get('features.target_column', '理论功率 (MW)')

        # 集成学习相关
        self.ensemble_trainer = None
        self.use_ensemble = True  # 是否使用集成学习

        # 综合特征工程
        self.feature_engine = ComprehensiveFeatureEngine(self.config)
        self.loss_functions = AdvancedLossFunctions()

        # 多点位数据融合
        self.multi_location_fusion = MultiLocationDataFusion()
        self.spatial_feature_engine = SpatialFeatureEngine()

        # 特征选择器
        self.feature_selector = FeatureSelector(self.config)

        # 集成特征工程器（新增）
        time_col = self.config.get('features.time_column', '时间')
        self.integrated_engineer = IntegratedFeatureEngineer("config.json", time_col)

        # 特征工程配置
        self.feature_engineering_mode = self.config.get('feature_engineering.mode', 4)
        
    def fit_feature_pipeline(self, df):
        """
        训练特征工程管道
        新流程：先完成所有特征工程，显示特征数，然后进行特征筛选
        """
        print("训练特征工程管道...")

        # 根据配置选择特征工程模式
        print(f"特征工程模式: {self.feature_engineering_mode}")
        mode_descriptions = self.config.get('feature_engineering.mode_description', {})
        if str(self.feature_engineering_mode) in mode_descriptions:
            print(f"模式说明: {mode_descriptions[str(self.feature_engineering_mode)]}")

        if self.feature_engineering_mode == 1:
            # 模式1: 传统特征工程 + 集成特征工程 + 特征选择
            print("执行传统特征工程...")
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            # 集成特征工程（新增风机参数特征）
            print("执行集成特征工程（风机参数增强）...")
            df = self.integrated_engineer.engineer_features(df, is_training=True)

        elif self.feature_engineering_mode == 2:
            # 模式2: 综合特征工程 + 集成特征工程 + 特征选择
            print("执行综合特征工程 (方案1、2、4、5)...")
            df = self.feature_engine.process_comprehensive_features(
                df,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True
            )

            # 集成特征工程（新增风机参数特征）
            print("执行集成特征工程（风机参数增强）...")
            df = self.integrated_engineer.engineer_features(df, is_training=True)

        elif self.feature_engineering_mode == 3:
            # 模式3: 传统特征工程 + 综合特征工程 + 集成特征工程 + 特征选择
            print("执行传统特征工程 + 综合特征工程 + 集成特征工程...")
            print("  1. 传统特征工程...")
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            print("  2. 综合特征工程 (方案1、2、4、5)...")
            df = self.feature_engine.process_comprehensive_features(
                df,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True
            )

            # 集成特征工程（新增风机参数特征）
            print("  3. 集成特征工程（风机参数增强）...")
            df = self.integrated_engineer.engineer_features(df, is_training=True)

        elif self.feature_engineering_mode == 4:
            # 模式4: 多点位融合 + 传统特征工程 + 综合特征工程 + 集成特征工程 + 特征选择
            print("执行多点位融合 + 传统特征工程 + 综合特征工程 + 集成特征工程...")

            # 1. 多点位数据融合
            print("  1. 多点位天气数据融合...")
            multi_location_config = self.config.get('multi_location', {})
            if multi_location_config.get('enable', False):
                data_dir = self.config.get('data_files.train_data_directory', 'data')
                train_pattern = multi_location_config.get('train_file_pattern', '15min_历史气象20250228之前')

                # 执行多点位数据融合
                spatial_df = self.multi_location_fusion.fuse_multi_location_data(
                    data_dir=data_dir,
                    file_pattern=train_pattern,
                    is_train=True
                )

                # 应用空间特征工程
                spatial_df = self.spatial_feature_engine.process_spatial_features(spatial_df)

                # 合并空间特征到原始数据
                # 基于时间列合并
                time_col = self.config.get('features.time_column', '时间')
                df[time_col] = pd.to_datetime(df[time_col])
                spatial_df[time_col] = pd.to_datetime(spatial_df[time_col])

                # 合并数据
                df = pd.merge(df, spatial_df, on=time_col, how='inner', suffixes=('', '_spatial'))
                print(f"    多点位融合完成，数据形状: {df.shape}")
            else:
                print("    多点位融合已禁用，跳过...")

            # 2. 传统特征工程
            print("  2. 传统特征工程...")
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            # 3. 多源风速数据加载和融合
            multi_source_data = None
            multi_source_config = self.config.get('multi_source_wind', {})
            if multi_source_config.get('enable', False):
                print("  3. 多源风速数据融合...")
                try:
                    # 构建多源风速数据文件路径
                    train_data_dir = self.config.get('data_files.train_data_directory', 'data/training')
                    multi_source_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
                    multi_source_path = Path(train_data_dir) / multi_source_file

                    if multi_source_path.exists():
                        # 使用多源风速融合器加载数据
                        multi_source_data = self.feature_engine.multi_source_fusion.load_multi_source_data(str(multi_source_path))
                        print(f"    多源风速数据加载成功，形状: {multi_source_data.shape}")
                    else:
                        print(f"    警告：多源风速数据文件不存在: {multi_source_path}")
                except Exception as e:
                    print(f"    警告：多源风速数据加载失败: {e}")
            else:
                print("  3. 多源风速数据融合已禁用，跳过...")

            # 4. 综合特征工程
            print("  4. 综合特征工程 (方案1、2、4、5 + 多源风速)...")
            df = self.feature_engine.process_comprehensive_features(
                df,
                multi_source_data=multi_source_data,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True,
                enable_multi_source=True
            )

            # 5. 集成特征工程（新增风机参数特征）
            print("  5. 集成特征工程（风机参数增强）...")
            df = self.integrated_engineer.engineer_features(df, is_training=True)

        else:
            raise ValueError(f"不支持的特征工程模式: {self.feature_engineering_mode}，支持的模式: 1, 2, 3, 4")
        
        # 3. 分类变量编码
        categorical_columns = []
        for col in df.columns:
            if df[col].dtype == 'object' and col not in ['时间', self.target_column]:
                categorical_columns.append(col)
        
        if categorical_columns:
            print(f"训练 {len(categorical_columns)} 个分类变量的编码器...")
            for col in categorical_columns:
                le = LabelEncoder()
                values = df[col].astype(str).fillna('unknown')
                le.fit(values.unique())
                self.label_encoders[col] = le
                df[col] = le.transform(values)
                # 确保编码后的结果是数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
                print(f"   {col}: {len(le.classes_)} 个类别 -> {df[col].dtype}")

        # 最终检查：确保所有非时间列都是数值类型
        for col in df.columns:
            if col not in ['时间', self.target_column] and df[col].dtype == 'object':
                print(f"  警告: 列 {col} 仍为object类型，尝试转换...")
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                    print(f"   {col} 转换为 {df[col].dtype}")
                except:
                    print(f"   {col} 转换失败")

        # 4. 显示特征工程完成后的特征数量
        feature_cols = [col for col in df.columns
                       if col not in ['时间', self.target_column, '理论功率 (MW)']]
        print(f"\n 特征工程完成")
        print(f"特征工程后特征数量: {len(feature_cols)}")

        # 5. 特征选择
        print(f"\n开始特征选择...")
        X_features = df[feature_cols]
        y_target = df[self.target_column].values

        # 训练特征选择器
        self.selected_features = self.feature_selector.fit_select_features(X_features, y_target)

        # 打印特征重要性报告
        self.feature_selector.print_feature_importance_report(top_n=20)

        return df
    
    def transform_features(self, df):
        """
        应用特征工程管道到新数据
        新流程：先特征工程，再特征选择
        """
        # 根据配置应用相同的特征工程模式
        if self.feature_engineering_mode == 1:
            # 模式1: 传统特征工程 + 集成特征工程
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            # 集成特征工程（新增风机参数特征）
            df = self.integrated_engineer.engineer_features(df, is_training=False)

        elif self.feature_engineering_mode == 2:
            # 模式2: 综合特征工程
            df = self.feature_engine.process_comprehensive_features(
                df,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True
            )

            # 集成特征工程（新增风机参数特征）
            df = self.integrated_engineer.engineer_features(df, is_training=False)

        elif self.feature_engineering_mode == 3:
            # 模式3: 传统特征工程 + 综合特征工程
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            df = self.feature_engine.process_comprehensive_features(
                df,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True
            )

            # 4. 集成特征工程（新增风机参数特征）
            df = self.integrated_engineer.engineer_features(df, is_training=False)

        elif self.feature_engineering_mode == 4:
            # 模式4: 多点位融合 + 传统特征工程 + 综合特征工程

            # 1. 多点位数据融合
            multi_location_config = self.config.get('multi_location', {})
            if multi_location_config.get('enable', False):
                data_dir = self.config.get('data_files.test_data_directory', 'data')
                test_pattern = multi_location_config.get('test_file_pattern', '15min_历史气象202503')

                # 执行多点位数据融合
                spatial_df = self.multi_location_fusion.fuse_multi_location_data(
                    data_dir=data_dir,
                    file_pattern=test_pattern,
                    is_train=False
                )

                # 应用空间特征工程
                spatial_df = self.spatial_feature_engine.process_spatial_features(spatial_df)

                # 合并空间特征到原始数据
                time_col = self.config.get('features.time_column', '时间')
                df[time_col] = pd.to_datetime(df[time_col])
                spatial_df[time_col] = pd.to_datetime(spatial_df[time_col])

                # 合并数据
                df = pd.merge(df, spatial_df, on=time_col, how='inner', suffixes=('', '_spatial'))

            # 2. 传统特征工程
            df = self.time_feature_extractor.extract_all_time_features(df)
            df = self.physics_feature_creator.create_all_physics_features(df)

            # 3. 综合特征工程
            df = self.feature_engine.process_comprehensive_features(
                df,
                enable_data_quality=True,
                enable_physics=True,
                enable_time=True,
                enable_breakthrough=True
            )

            # 4. 集成特征工程（新增风机参数特征）
            print("\n4. 集成特征工程（风机参数增强）")
            print("-" * 50)
            df = self.integrated_engineer.engineer_features(df, is_training=True)

        # 3. 分类变量编码
        for col, le in self.label_encoders.items():
            if col in df.columns:
                values = df[col].astype(str).fillna('unknown')
                # 处理新的未见过的类别
                try:
                    df[col] = le.transform(values)
                except ValueError:
                    # 如果有新类别，用最常见的类别替代
                    print(f"警告: {col} 列包含未见过的类别，使用默认值")
                    known_values = [v if v in le.classes_ else le.classes_[0] for v in values]
                    df[col] = le.transform(known_values)

                # 确保结果是数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 确保所有列都是数值类型
        for col in df.columns:
            if col != '时间' and df[col].dtype == 'object':
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except:
                    pass

        # 应用特征选择
        if hasattr(self, 'selected_features') and self.selected_features:
            # 使用特征选择器选择特征
            feature_cols = [col for col in df.columns
                           if col not in ['时间', self.target_column, '理论功率 (MW)']]
            X_features = df[feature_cols]
            X_selected = self.feature_selector.transform(X_features)
            return X_selected
        else:
            # 如果没有特征选择，使用所有特征
            available_features = [col for col in df.columns
                                if col not in ['时间', self.target_column, '理论功率 (MW)']]
            return df[available_features]

    def predict(self, X):
        """
        使用管道进行预测
        """
        # 检查输入数据是否已经是处理过的特征
        if isinstance(X, pd.DataFrame) and set(X.columns) == set(self.feature_columns):
            # 如果输入已经是处理过的特征，直接使用
            X_processed = X
        else:
            # 否则进行特征工程
            X_processed = self.transform_features(X)

        # 模型预测
        if self.use_ensemble and self.ensemble_trainer is not None:
            return self.ensemble_trainer.predict(X_processed)
        else:
            return self.model.predict(X_processed)

    def save_pipeline(self, model_dir="production_model"):
        """
        保存完整的预测管道
        """
        model_path = Path(model_dir)
        model_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存完整的管道对象
        pipeline_file = model_path / "production_pipeline.pkl"
        joblib.dump(self, pipeline_file)
        print(f" 完整管道已保存到: {pipeline_file}")

        # 保存模型（向后兼容）
        if self.use_ensemble and self.ensemble_trainer is not None:
            model_file = model_path / f"ensemble_model_{timestamp}.pkl"
            joblib.dump(self.ensemble_trainer, model_file)
        else:
            model_file = model_path / f"lightgbm_model_{timestamp}.pkl"
            joblib.dump(self.model, model_file)

        # 保存标签编码器（向后兼容）
        encoders_file = model_path / f"label_encoders_{timestamp}.pkl"
        joblib.dump(self.label_encoders, encoders_file)

        # 保存特征列表（向后兼容）
        features_file = model_path / f"feature_columns_{timestamp}.json"
        with open(features_file, 'w', encoding='utf-8') as f:
            json.dump(self.feature_columns, f, ensure_ascii=False, indent=2)

        # 保存特征选择结果
        if hasattr(self, 'feature_selector') and self.feature_selector.selected_features_:
            selection_file = model_path / f"feature_selection_{timestamp}.pkl"
            self.feature_selector.save_selection_results(selection_file)
        
        # 保存管道配置
        config = {
            'timestamp': timestamp,
            'model_file': str(model_file.name),
            'encoders_file': str(encoders_file.name),
            'features_file': str(features_file.name),
            'feature_count': len(self.feature_columns),
            'target_column': self.target_column,
            'use_ensemble': self.use_ensemble,
            'model_type': 'ensemble' if self.use_ensemble else 'lightgbm',
            'feature_engineering_mode': self.feature_engineering_mode,  # 保存特征工程模式
            'feature_engineering_version': '3.0'  # 标记特征工程版本
        }
        
        config_file = model_path / f"pipeline_config_{timestamp}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 创建最新版本的符号链接
        latest_config = model_path / "latest_config.json"
        with open(latest_config, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return {
            'model_dir': str(model_path),
            'config_file': str(config_file),
            'timestamp': timestamp
        }

def clear_production_model_folder():
    """
    清空production_model文件夹，确保使用最新配置
    """
    production_dir = Path("production_model")

    if production_dir.exists():
        print("清空旧的生产模型文件夹...")
        try:
            shutil.rmtree(production_dir)
            print("旧模型文件已清除")
        except Exception as e:
            print(f"清除旧模型文件时出错: {e}")

    # 重新创建文件夹
    production_dir.mkdir(exist_ok=True)
    print("生产模型文件夹已准备就绪")

def main():
    """
    主训练流程
    """
    print("=" * 60)
    print("生产环境模型训练和导出")
    print("=" * 60)

    # 0. 清空旧的生产模型
    print("\n0. 清理旧模型")
    print("-" * 30)
    clear_production_model_folder()

    # 1. 数据加载
    print("\n1. 加载训练数据")
    print("-" * 30)
    
    loader = WindPowerDataLoader()
    try:
        train_df = loader.load_training_data()
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 2. 创建生产管道
    print("\n2. 创建生产管道")
    print("-" * 30)
    
    pipeline = ProductionModelPipeline()
    
    # 3. 特征工程
    print(f"\n3. 特征工程 (模式 {pipeline.feature_engineering_mode})")
    print("-" * 30)

    # 训练特征工程管道
    train_df_processed = pipeline.fit_feature_pipeline(train_df.copy())

    # 准备训练数据 - 应用特征选择结果
    if hasattr(pipeline, 'selected_features') and pipeline.selected_features:
        print(f" 应用特征选择结果: {len(pipeline.selected_features)} 个特征")
        X_train = train_df_processed[pipeline.selected_features]
        pipeline.feature_columns = pipeline.selected_features
    else:
        # 如果特征选择被禁用，使用所有特征
        feature_cols = [col for col in train_df_processed.columns
                       if col not in ['时间', pipeline.target_column, '理论功率 (MW)']]
        X_train = train_df_processed[feature_cols]
        pipeline.feature_columns = feature_cols
        print(f" 特征选择已禁用，使用所有 {len(feature_cols)} 个特征")

    y_train = train_df_processed[pipeline.target_column].values
    
    # 处理缺失值
    print("处理缺失值...")
    for col in X_train.columns:
        if X_train[col].isnull().any():
            print(f"  处理列 {col} (类型: {X_train[col].dtype})")

            # 检查数据类型并相应处理
            if X_train[col].dtype.name == 'category':
                # 分类数据用众数填充
                mode_val = X_train[col].mode().iloc[0] if not X_train[col].mode().empty else 0
                X_train[col] = X_train[col].fillna(mode_val)
            elif X_train[col].dtype in ['object']:
                # 字符串数据转换为数值或用众数
                try:
                    X_train[col] = pd.to_numeric(X_train[col], errors='coerce')
                    X_train[col] = X_train[col].fillna(X_train[col].median())
                except:
                    mode_val = X_train[col].mode().iloc[0] if not X_train[col].mode().empty else 'unknown'
                    X_train[col] = X_train[col].fillna(mode_val)
            else:
                # 数值数据用中位数填充
                X_train[col] = X_train[col].fillna(X_train[col].median())

    print(f" 数据准备完成")
    print(f"最终特征数量: {len(X_train.columns)}")
    print(f"训练样本数: {len(X_train)}")
    
    # 4. 模型训练
    print("\n4. 模型训练")
    print("-" * 30)
    
    # 超参数优化
    optimizer = LightGBMHyperOptimizer(cv_folds=3, n_trials=20, physics_constrained=True)
    optimization_results = optimizer.optimize(X_train, y_train)
    
    # 训练最终模型
    pipeline.model = PhysicsConstrainedLightGBM()
    
    # 使用80%数据训练，20%验证
    split_idx = int(len(X_train) * 0.8)
    X_train_final = X_train.iloc[:split_idx]
    y_train_final = y_train[:split_idx]
    X_val_final = X_train.iloc[split_idx:]
    y_val_final = y_train[split_idx:]
    
    pipeline.model.fit(
        X_train_final, y_train_final,
        X_val_final, y_val_final,
        **optimization_results['best_params']
    )
    
    print(f" 基线模型训练完成")
    print(f"最佳验证RMSE: {optimization_results['best_score']:.4f}")

    # 5. 集成学习训练 (方案3) + 高级损失函数 (方案6)
    print("\n5. 集成学习训练 (方案3) + 高级损失函数 (方案6)")
    print("-" * 30)

    try:
        # 创建集成训练器
        pipeline.ensemble_trainer = EnsembleTrainer(wind_speed_column='wind_speed_80m')

        # 应用高级损失函数
        print("  应用高级损失函数优化...")
        combined_loss = pipeline.loss_functions.create_combined_loss()
        print("   组合损失函数已创建")

        # 训练所有集成模型
        ensemble_results = pipeline.ensemble_trainer.train_all_models(
            X_train, y_train, X_val_final, y_val_final
        )

        # 检查集成模型是否优于基线
        if (pipeline.ensemble_trainer.best_model is not None and
            'baseline' in pipeline.ensemble_trainer.performance_comparison):

            baseline_rmse = pipeline.ensemble_trainer.performance_comparison['baseline']['rmse']
            best_rmse = pipeline.ensemble_trainer.performance_comparison[pipeline.ensemble_trainer.best_model_name]['rmse']

            if best_rmse < baseline_rmse:
                print(f" 集成模型优于基线: {best_rmse:.4f} < {baseline_rmse:.4f}")
                pipeline.use_ensemble = True
            else:
                print(f"  集成模型未超越基线，使用基线模型")
                pipeline.use_ensemble = False
        else:
            print(f"  集成训练失败，使用基线模型")
            pipeline.use_ensemble = False

    except Exception as e:
        print(f"✗ 集成学习训练失败: {e}")
        print("使用基线模型...")
        pipeline.use_ensemble = False

    # 6. 模型验证
    print("\n6. 模型验证")
    print("-" * 30)
    
    # 验证集预测 (使用最终选择的模型)
    y_val_pred = pipeline.predict(X_val_final)
    val_rmse = np.sqrt(np.mean((y_val_final - y_val_pred) ** 2))
    val_mae = np.mean(np.abs(y_val_final - y_val_pred))

    model_type = "集成模型" if pipeline.use_ensemble else "基线模型"
    print(f"最终模型 ({model_type}) 验证集性能:")
    print(f"  RMSE: {val_rmse:.4f}")
    print(f"  MAE: {val_mae:.4f}")

    if pipeline.use_ensemble and pipeline.ensemble_trainer is not None:
        print(f"  最佳集成模型: {pipeline.ensemble_trainer.best_model_name}")

    # 7. 导出生产模型
    print("\n7. 导出生产模型")
    print("-" * 30)
    
    export_info = pipeline.save_pipeline()
    
    print(f" 模型导出完成")
    print(f"模型目录: {export_info['model_dir']}")
    print(f"配置文件: {export_info['config_file']}")
    print(f"时间戳: {export_info['timestamp']}")
    
    # 7. 使用说明
    print("\n7. 使用说明")
    print("-" * 30)
    print("生产模型已准备就绪!")
    print("\n每日预测使用方法:")
    print("1. 将新的测试数据放入 data/ 目录")
    print("2. 运行: python daily_prediction.py --test_file 新测试文件.csv")
    print("3. 预测结果将保存在 daily_predictions/ 目录")
    
    print(f"\n" + "=" * 60)
    print("生产模型训练完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
