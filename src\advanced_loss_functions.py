"""
方案6：损失函数创新
实现分段加权损失、物理约束损失增强、预测一致性损失、极值预测专项优化
"""

import numpy as np
import pandas as pd
from typing import Callable, Dict, Any
import warnings
warnings.filterwarnings('ignore')

class AdvancedLossFunctions:
    """
    高级损失函数集合
    针对风力发电功率预测的专门优化
    """
    
    def __init__(self):
        # 风力发电机参数
        self.rated_power = 2.5  # MW
        self.cut_in_speed = 3.0  # m/s
        self.cut_out_speed = 25.0  # m/s
        self.rated_wind_speed = 12.0  # m/s
        
    def create_segmented_weighted_loss(self, power_segments: Dict[str, Dict] = None) -> Callable:
        """
        创建分段加权损失函数
        不同功率区间使用不同权重
        """
        if power_segments is None:
            power_segments = {
                'zero_power': {'range': (0, 0.1), 'weight': 2.0},      # 零功率区间
                'low_power': {'range': (0.1, 0.5), 'weight': 1.5},     # 低功率区间
                'mid_power': {'range': (0.5, 1.5), 'weight': 1.0},     # 中功率区间
                'high_power': {'range': (1.5, 2.0), 'weight': 1.2},    # 高功率区间
                'rated_power': {'range': (2.0, 2.5), 'weight': 1.8}    # 额定功率区间
            }
        
        def segmented_weighted_mse(y_true, y_pred):
            """
            分段加权均方误差
            """
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            total_loss = 0.0
            total_weight = 0.0
            
            for segment_name, segment_info in power_segments.items():
                min_power, max_power = segment_info['range']
                weight = segment_info['weight']
                
                # 找到属于当前区间的样本
                mask = (y_true >= min_power) & (y_true < max_power)
                
                if np.sum(mask) > 0:
                    segment_loss = np.mean((y_true[mask] - y_pred[mask]) ** 2)
                    total_loss += weight * segment_loss * np.sum(mask)
                    total_weight += weight * np.sum(mask)
            
            return total_loss / (total_weight + 1e-8)
        
        return segmented_weighted_mse
    
    def create_physics_constrained_loss(self, physics_weight: float = 0.2) -> Callable:
        """
        创建物理约束增强损失函数
        """
        def physics_constrained_mse(y_true, y_pred, wind_speed=None):
            """
            物理约束增强的均方误差
            """
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # 基础MSE损失
            base_loss = np.mean((y_true - y_pred) ** 2)
            
            # 物理约束损失
            physics_loss = 0.0
            
            # 1. 功率非负约束
            negative_power_penalty = np.mean(np.maximum(0, -y_pred) ** 2)
            physics_loss += negative_power_penalty
            
            # 2. 功率上限约束
            excess_power_penalty = np.mean(np.maximum(0, y_pred - self.rated_power) ** 2)
            physics_loss += excess_power_penalty
            
            # 3. 风速-功率关系约束 (如果提供风速数据)
            if wind_speed is not None:
                wind_speed = np.array(wind_speed)
                
                # 低风速时功率应该很小
                low_wind_mask = wind_speed < self.cut_in_speed
                if np.sum(low_wind_mask) > 0:
                    low_wind_penalty = np.mean((y_pred[low_wind_mask] - 0) ** 2)
                    physics_loss += low_wind_penalty
                
                # 高风速时功率应该接近额定功率或为零（切出）
                high_wind_mask = wind_speed > self.cut_out_speed
                if np.sum(high_wind_mask) > 0:
                    high_wind_penalty = np.mean(np.minimum(
                        (y_pred[high_wind_mask] - 0) ** 2,
                        (y_pred[high_wind_mask] - self.rated_power) ** 2
                    ))
                    physics_loss += high_wind_penalty
                
                # 额定风速附近功率应该接近额定功率
                rated_wind_mask = (wind_speed >= self.rated_wind_speed - 1) & (wind_speed <= self.rated_wind_speed + 1)
                if np.sum(rated_wind_mask) > 0:
                    rated_penalty = np.mean((y_pred[rated_wind_mask] - self.rated_power) ** 2)
                    physics_loss += rated_penalty * 0.5
            
            # 4. 功率变化平滑性约束
            if len(y_pred) > 1:
                power_changes = np.diff(y_pred)
                extreme_changes = np.abs(power_changes) > 0.5  # 15分钟内变化超过0.5MW
                smoothness_penalty = np.mean(power_changes[extreme_changes] ** 2) if np.sum(extreme_changes) > 0 else 0
                physics_loss += smoothness_penalty
            
            # 总损失
            total_loss = base_loss + physics_weight * physics_loss
            
            return total_loss
        
        return physics_constrained_mse
    
    def create_consistency_loss(self, consistency_weight: float = 0.1) -> Callable:
        """
        创建预测一致性损失
        相邻时间点预测的平滑性约束
        """
        def consistency_mse(y_true, y_pred):
            """
            一致性增强的均方误差
            """
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # 基础MSE损失
            base_loss = np.mean((y_true - y_pred) ** 2)
            
            # 一致性损失
            consistency_loss = 0.0
            
            if len(y_pred) > 1:
                # 1. 预测值的时间一致性
                pred_changes = np.diff(y_pred)
                true_changes = np.diff(y_true)
                
                # 预测变化应该与真实变化方向一致
                direction_consistency = np.mean((pred_changes - true_changes) ** 2)
                consistency_loss += direction_consistency
                
                # 2. 预测变化的幅度合理性
                # 真实功率变化通常比较平滑
                excessive_changes = np.abs(pred_changes) > np.abs(true_changes) * 2
                if np.sum(excessive_changes) > 0:
                    change_penalty = np.mean(pred_changes[excessive_changes] ** 2)
                    consistency_loss += change_penalty
                
                # 3. 二阶导数约束 (加速度)
                if len(y_pred) > 2:
                    pred_acceleration = np.diff(pred_changes)
                    true_acceleration = np.diff(true_changes)
                    acceleration_consistency = np.mean((pred_acceleration - true_acceleration) ** 2)
                    consistency_loss += acceleration_consistency * 0.5
            
            # 总损失
            total_loss = base_loss + consistency_weight * consistency_loss
            
            return total_loss
        
        return consistency_mse
    
    def create_extreme_value_loss(self, extreme_weight: float = 2.0) -> Callable:
        """
        创建极值预测专项优化损失
        对高功率和零功率区间的特殊处理
        """
        def extreme_value_mse(y_true, y_pred):
            """
            极值优化的均方误差
            """
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # 基础MSE损失
            base_loss = np.mean((y_true - y_pred) ** 2)
            
            # 极值损失
            extreme_loss = 0.0
            
            # 1. 零功率预测优化
            zero_power_mask = y_true < 0.1
            if np.sum(zero_power_mask) > 0:
                zero_power_loss = np.mean((y_true[zero_power_mask] - y_pred[zero_power_mask]) ** 2)
                extreme_loss += zero_power_loss * extreme_weight
            
            # 2. 高功率预测优化
            high_power_mask = y_true > self.rated_power * 0.8
            if np.sum(high_power_mask) > 0:
                high_power_loss = np.mean((y_true[high_power_mask] - y_pred[high_power_mask]) ** 2)
                extreme_loss += high_power_loss * extreme_weight
            
            # 3. 功率跳跃预测优化
            if len(y_true) > 1:
                true_jumps = np.abs(np.diff(y_true)) > 0.5
                if np.sum(true_jumps) > 0:
                    pred_jumps = np.diff(y_pred)[true_jumps]
                    true_jump_values = np.diff(y_true)[true_jumps]
                    jump_loss = np.mean((pred_jumps - true_jump_values) ** 2)
                    extreme_loss += jump_loss * extreme_weight
            
            # 4. 异常值预测优化
            # 使用IQR方法识别异常值
            Q1 = np.percentile(y_true, 25)
            Q3 = np.percentile(y_true, 75)
            IQR = Q3 - Q1
            outlier_mask = (y_true < Q1 - 1.5 * IQR) | (y_true > Q3 + 1.5 * IQR)
            
            if np.sum(outlier_mask) > 0:
                outlier_loss = np.mean((y_true[outlier_mask] - y_pred[outlier_mask]) ** 2)
                extreme_loss += outlier_loss * extreme_weight
            
            # 总损失
            total_loss = base_loss + extreme_loss
            
            return total_loss
        
        return extreme_value_mse
    
    def create_adaptive_loss(self, adaptation_factor: float = 0.1) -> Callable:
        """
        创建自适应损失函数
        根据预测误差动态调整权重
        """
        def adaptive_mse(y_true, y_pred):
            """
            自适应均方误差
            """
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # 计算基础误差
            errors = np.abs(y_true - y_pred)
            
            # 根据误差大小动态调整权重
            # 误差大的样本给予更高权重
            error_percentiles = np.percentile(errors, [25, 50, 75, 90])
            
            weights = np.ones_like(errors)
            weights[errors > error_percentiles[0]] *= (1 + adaptation_factor)      # 25%以上误差
            weights[errors > error_percentiles[1]] *= (1 + adaptation_factor * 2)  # 50%以上误差
            weights[errors > error_percentiles[2]] *= (1 + adaptation_factor * 3)  # 75%以上误差
            weights[errors > error_percentiles[3]] *= (1 + adaptation_factor * 4)  # 90%以上误差
            
            # 加权MSE
            weighted_mse = np.average((y_true - y_pred) ** 2, weights=weights)
            
            return weighted_mse
        
        return adaptive_mse
    
    def create_combined_loss(self, 
                           segmented_weight: float = 0.3,
                           physics_weight: float = 0.2,
                           consistency_weight: float = 0.1,
                           extreme_weight: float = 0.2,
                           adaptive_weight: float = 0.2) -> Callable:
        """
        创建组合损失函数
        结合所有创新损失函数
        """
        # 创建各个损失函数
        segmented_loss = self.create_segmented_weighted_loss()
        physics_loss = self.create_physics_constrained_loss(physics_weight=0.2)
        consistency_loss = self.create_consistency_loss(consistency_weight=0.1)
        extreme_loss = self.create_extreme_value_loss(extreme_weight=2.0)
        adaptive_loss = self.create_adaptive_loss(adaptation_factor=0.1)
        
        def combined_loss_function(y_true, y_pred, wind_speed=None):
            """
            组合损失函数
            """
            # 计算各个损失分量
            seg_loss = segmented_loss(y_true, y_pred)
            
            if wind_speed is not None:
                phys_loss = physics_loss(y_true, y_pred, wind_speed)
            else:
                phys_loss = physics_loss(y_true, y_pred)
            
            cons_loss = consistency_loss(y_true, y_pred)
            ext_loss = extreme_loss(y_true, y_pred)
            adapt_loss = adaptive_loss(y_true, y_pred)
            
            # 加权组合
            total_loss = (
                segmented_weight * seg_loss +
                physics_weight * phys_loss +
                consistency_weight * cons_loss +
                extreme_weight * ext_loss +
                adaptive_weight * adapt_loss
            )
            
            return total_loss
        
        return combined_loss_function
    
    def get_loss_function_info(self) -> Dict[str, Any]:
        """
        获取损失函数信息
        """
        return {
            'available_losses': [
                'segmented_weighted_loss',
                'physics_constrained_loss', 
                'consistency_loss',
                'extreme_value_loss',
                'adaptive_loss',
                'combined_loss'
            ],
            'parameters': {
                'rated_power': self.rated_power,
                'cut_in_speed': self.cut_in_speed,
                'cut_out_speed': self.cut_out_speed,
                'rated_wind_speed': self.rated_wind_speed
            },
            'description': '针对风力发电功率预测优化的高级损失函数集合'
        }
