---
type: "agent_requested"
---

# 📁 FILE VERIFICATION

## 🔍 MEMORY BANK VERIFICATION

```mermaid
graph TD
    Start[Mode Start] --> Check{Memory Bank Exists?}
    Check -->|No| Create[CREATE MEMORY BANK]
    Check -->|Yes| Verify[Verify Structure]
    Create --> Verify
    Verify --> Valid{Valid Structure?}
    Valid -->|No| Fix[Fix Structure]
    Valid -->|Yes| Continue[Continue Operations]
    Fix --> Continue
```

## 📂 REQUIRED STRUCTURE

```
cursor-memory-bank/
├── tasks.md              # Task tracking
├── activeContext.md      # Current focus
├── progress.md           # Implementation status
└── creative-*.md         # Design decisions (L3+)
```

## 🔧 VERIFICATION COMMANDS

### Windows
```powershell
# Check if memory bank exists
if (Test-Path "cursor-memory-bank") {
    Write-Host "Memory Bank exists"
    Get-ChildItem "cursor-memory-bank"
} else {
    Write-Host "Creating Memory Bank..."
    New-Item -ItemType Directory -Path "cursor-memory-bank"
    New-Item -ItemType File -Path "cursor-memory-bank\tasks.md"
    New-Item -ItemType File -Path "cursor-memory-bank\activeContext.md"
    New-Item -ItemType File -Path "cursor-memory-bank\progress.md"
}
```

### Mac/Linux
```bash
# Check and create memory bank
if [ -d "cursor-memory-bank" ]; then
    echo "Memory Bank exists"
    ls -la cursor-memory-bank/
else
    echo "Creating Memory Bank..."
    mkdir -p cursor-memory-bank
    touch cursor-memory-bank/{tasks.md,activeContext.md,progress.md}
fi
```

## 📝 FILE TEMPLATES

### tasks.md
```markdown
# TASKS

## Current Task
- [ ] [Task description]

## Completed
- [x] [Completed task]

## Backlog
- [ ] [Future task]
```

### activeContext.md
```markdown
# ACTIVE CONTEXT

## Current Focus
[What you're working on now]

## Key Decisions
[Important decisions made]

## Next Steps
[Immediate next actions]
```

### progress.md
```markdown
# PROGRESS

## Implementation Status
- [x] Phase 1: [Description]
- [ ] Phase 2: [Description]

## Issues
[Any blockers or issues]

## Notes
[Implementation notes]
```

## ⚡ QUICK VERIFICATION

**Before any operation, run:**
1. Check memory bank exists
2. Verify required files
3. Validate file structure
4. Create missing files if needed

**Abort if:**
- Memory bank creation fails
- Required files cannot be created
- File permissions are insufficient
