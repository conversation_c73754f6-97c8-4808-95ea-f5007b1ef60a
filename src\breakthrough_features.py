"""
方案4：特征工程突破
实现风速-功率相位分析、多高度风切变精确建模、湍流谱分析、温度分层效应等创新特征
"""

import pandas as pd
import numpy as np
from scipy import signal, stats
from scipy.signal import hilbert
import warnings
warnings.filterwarnings('ignore')

def safe_fillna_with_mean(series: pd.Series) -> pd.Series:
    """
    安全地用均值填充缺失值，处理分类变量
    """
    if pd.api.types.is_numeric_dtype(series):
        return series.fillna(series.mean())
    else:
        # 对于分类变量，用众数填充
        mode_value = series.mode()
        if len(mode_value) > 0:
            return series.fillna(mode_value[0])
        else:
            return series.fillna(0)  # 如果没有众数，用0填充

class BreakthroughFeatures:
    """
    突破性特征工程
    基于风力发电的深层物理机制
    """
    
    def __init__(self):
        pass
        
    def create_all_breakthrough_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建所有突破性特征
        """
        df = df.copy()
        
        print("创建突破性特征工程...")
        
        # 1. 风速-功率相位分析
        df = self._create_phase_analysis_features(df)
        
        # 2. 多高度风切变精确建模
        df = self._create_precise_wind_shear_features(df)
        
        # 3. 湍流谱分析
        df = self._create_turbulence_spectrum_features(df)
        
        # 4. 温度分层效应
        df = self._create_temperature_stratification_features(df)
        
        # 5. 非线性风速特征
        df = self._create_nonlinear_wind_features(df)
        
        # 6. 多变量交互特征
        df = self._create_multivariate_interaction_features(df)
        
        print(f" 突破性特征工程创建完成")
        
        return df
    
    def _create_phase_analysis_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        风速-功率相位分析
        分析风速变化与功率响应的时间延迟关系
        """
        if 'wind_speed_80m' not in df.columns:
            return df

        wind_speed = safe_fillna_with_mean(df['wind_speed_80m'])
        n_rows = len(df)

        # 使用Hilbert变换进行相位分析
        try:
            # 确保输入数据是连续的，并重置索引以避免长度不匹配
            wind_values = wind_speed.reset_index(drop=True).values

            if len(wind_values) != n_rows:
                print(f"警告: 风速数据长度 ({len(wind_values)}) 与DataFrame长度 ({n_rows}) 不匹配")
                # 调整到正确长度
                if len(wind_values) > n_rows:
                    wind_values = wind_values[:n_rows]
                else:
                    # 用最后一个值填充
                    wind_values = np.pad(wind_values, (0, n_rows - len(wind_values)), 'edge')

            # 计算解析信号
            analytic_signal = hilbert(wind_values)
            instantaneous_phase = np.angle(analytic_signal)
            instantaneous_amplitude = np.abs(analytic_signal)

            # 计算瞬时频率，确保长度一致
            unwrapped_phase = np.unwrap(instantaneous_phase)
            instantaneous_frequency = np.gradient(unwrapped_phase) / (2.0 * np.pi)

            # 确保所有数组长度正确
            assert len(instantaneous_phase) == n_rows, f"相位长度不匹配: {len(instantaneous_phase)} vs {n_rows}"
            assert len(instantaneous_amplitude) == n_rows, f"幅度长度不匹配: {len(instantaneous_amplitude)} vs {n_rows}"
            assert len(instantaneous_frequency) == n_rows, f"频率长度不匹配: {len(instantaneous_frequency)} vs {n_rows}"

            # 直接赋值，确保长度匹配
            df['wind_instantaneous_phase'] = instantaneous_phase
            df['wind_instantaneous_amplitude'] = instantaneous_amplitude
            df['wind_instantaneous_frequency'] = instantaneous_frequency

            # 相位导数 (频率)
            phase_derivative = np.gradient(instantaneous_phase)
            df['wind_phase_derivative'] = phase_derivative

            # 相位稳定性
            phase_stability = 1 / (1 + np.abs(np.gradient(instantaneous_frequency)))
            df['wind_phase_stability'] = phase_stability

        except Exception as e:
            print(f"相位分析失败: {e}")
            # 使用与DataFrame长度一致的默认值
            df['wind_instantaneous_phase'] = np.zeros(n_rows)
            df['wind_instantaneous_amplitude'] = wind_speed.iloc[:n_rows].values if len(wind_speed) >= n_rows else np.pad(wind_speed.values, (0, n_rows - len(wind_speed)), 'edge')
            df['wind_instantaneous_frequency'] = np.zeros(n_rows)
            df['wind_phase_derivative'] = np.zeros(n_rows)
            df['wind_phase_stability'] = np.ones(n_rows)
        
        # 风速变化的延迟效应
        for lag in [1, 2, 4, 8]:  # 15分钟, 30分钟, 1小时, 2小时延迟
            df[f'wind_speed_lag_{lag}'] = wind_speed.shift(lag).fillna(wind_speed.iloc[0])
            df[f'wind_speed_lead_{lag}'] = wind_speed.shift(-lag).fillna(wind_speed.iloc[-1])
        
        # 风速变化的累积效应
        df['wind_speed_cumulative_change_1h'] = wind_speed.rolling(4).apply(lambda x: np.sum(np.diff(x))).fillna(0)
        df['wind_speed_cumulative_change_3h'] = wind_speed.rolling(12).apply(lambda x: np.sum(np.diff(x))).fillna(0)
        
        # 风速变化的惯性指标
        wind_velocity = wind_speed.diff().fillna(0)  # 风速变化率
        wind_acceleration = wind_velocity.diff().fillna(0)  # 风速加速度
        
        df['wind_velocity'] = wind_velocity
        df['wind_acceleration'] = wind_acceleration
        df['wind_jerk'] = wind_acceleration.diff().fillna(0)  # 风速急动度
        
        # 动量指标
        df['wind_momentum'] = wind_speed * wind_velocity
        df['wind_kinetic_energy_change'] = wind_speed * wind_acceleration
        
        return df
    
    def _create_precise_wind_shear_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        多高度风切变精确建模
        不只是简单比值，而是完整的风廓线重构
        """
        wind_columns = [col for col in df.columns if 'wind_speed' in col and any(h in col for h in ['10m', '80m', '100m'])]
        
        if len(wind_columns) < 2:
            return df
        
        # 提取高度信息
        heights = []
        wind_data = []
        
        for col in wind_columns:
            # 检查列是否为数值型
            if not pd.api.types.is_numeric_dtype(df[col]):
                continue

            if '10m' in col:
                heights.append(10)
                wind_data.append(safe_fillna_with_mean(df[col]))
            elif '80m' in col:
                heights.append(80)
                wind_data.append(safe_fillna_with_mean(df[col]))
            elif '100m' in col:
                heights.append(100)
                wind_data.append(safe_fillna_with_mean(df[col]))
        
        if len(heights) >= 2:
            heights = np.array(heights)
            wind_data = np.array(wind_data)
            
            # 对数风廓线拟合
            log_heights = np.log(heights)
            
            wind_shear_exponents = []
            roughness_lengths = []
            profile_fit_qualities = []
            
            for i in range(len(df)):
                wind_profile = wind_data[:, i]
                
                if np.all(wind_profile > 0):
                    try:
                        # 拟合对数风廓线: u(z) = u_ref * ln(z/z0) / ln(z_ref/z0)
                        log_wind = np.log(wind_profile)
                        
                        # 线性回归拟合
                        slope, intercept, r_value, _, _ = stats.linregress(log_heights, log_wind)
                        
                        # 风切变指数
                        shear_exponent = slope
                        
                        # 粗糙度长度估算
                        z0 = np.exp(-intercept / slope) if slope != 0 else 0.1
                        z0 = np.clip(z0, 0.001, 10)  # 合理范围
                        
                        # 拟合质量
                        fit_quality = r_value ** 2
                        
                        wind_shear_exponents.append(shear_exponent)
                        roughness_lengths.append(z0)
                        profile_fit_qualities.append(fit_quality)
                        
                    except:
                        wind_shear_exponents.append(0.2)  # 默认值
                        roughness_lengths.append(0.1)
                        profile_fit_qualities.append(0.5)
                else:
                    wind_shear_exponents.append(0.2)
                    roughness_lengths.append(0.1)
                    profile_fit_qualities.append(0.5)
            
            df['precise_wind_shear_exponent'] = wind_shear_exponents
            df['surface_roughness_length'] = roughness_lengths
            df['wind_profile_fit_quality'] = profile_fit_qualities
            
            # 基于精确风廓线的80m风速预测
            if 10 in heights and 80 in heights:
                wind_10m = df[f'wind_speed_10m'].fillna(df[f'wind_speed_10m'].mean())
                predicted_80m = wind_10m * (np.log(80 / np.array(roughness_lengths)) / 
                                           np.log(10 / np.array(roughness_lengths)))
                df['predicted_wind_80m_from_profile'] = predicted_80m
                
                # 实际与预测的偏差
                if 'wind_speed_80m' in df.columns:
                    actual_80m = df['wind_speed_80m']
                    df['wind_profile_prediction_error'] = np.abs(actual_80m - predicted_80m) / (actual_80m + 1e-6)
            
            # 风切变稳定性
            df['wind_shear_stability'] = 1 / (1 + np.abs(pd.Series(wind_shear_exponents).diff().fillna(0)))
            
            # 风切变分类
            df['wind_shear_category'] = np.where(
                np.array(wind_shear_exponents) < 0.1, 1,  # 弱切变
                np.where(
                    np.array(wind_shear_exponents) < 0.3, 2,  # 中等切变
                    3  # 强切变
                )
            )
        
        return df
    
    def _create_turbulence_spectrum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        湍流谱分析
        从阵风数据中提取湍流能量谱特征
        """
        gust_columns = [col for col in df.columns if 'gust' in col.lower()]
        wind_columns = [col for col in df.columns if 'wind_speed' in col]
        
        if not gust_columns and not wind_columns:
            return df
        
        # 使用阵风数据或风速数据
        if gust_columns:
            primary_data = df[gust_columns[0]].fillna(df[gust_columns[0]].mean())
            reference_wind = df[wind_columns[0]].fillna(df[wind_columns[0]].mean()) if wind_columns else primary_data
        else:
            primary_data = df[wind_columns[0]].fillna(df[wind_columns[0]].mean())
            reference_wind = primary_data
        
        # 湍流强度
        df['turbulence_intensity'] = primary_data / (reference_wind + 1e-6)
        
        # 湍流动能
        df['turbulent_kinetic_energy'] = 0.5 * (primary_data - reference_wind) ** 2
        
        # 使用滑动窗口分析湍流谱
        window_size = min(48, len(df) // 4)  # 12小时窗口
        
        turbulence_scales = []
        energy_dissipation_rates = []
        integral_length_scales = []
        
        for i in range(len(df)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(df), i + window_size // 2)
            
            window_data = primary_data.iloc[start_idx:end_idx].values
            
            if len(window_data) >= 16:
                # 计算湍流统计量
                mean_val = np.mean(window_data)
                fluctuations = window_data - mean_val
                
                # 湍流尺度 (积分长度尺度的简化估算)
                autocorr = np.correlate(fluctuations, fluctuations, mode='full')
                autocorr = autocorr[autocorr.size // 2:]
                autocorr = autocorr / autocorr[0]
                
                # 找到自相关函数首次过零点
                zero_crossing = np.where(autocorr <= 0)[0]
                if len(zero_crossing) > 0:
                    integral_scale = zero_crossing[0] * 0.25  # 转换为小时
                else:
                    integral_scale = len(autocorr) * 0.25
                
                # 能量耗散率估算 (基于Kolmogorov理论)
                variance = np.var(fluctuations)
                dissipation_rate = variance ** 1.5 / integral_scale
                
                # 湍流尺度
                turbulence_scale = variance / (np.mean(np.abs(np.gradient(fluctuations))) + 1e-6)
                
                turbulence_scales.append(turbulence_scale)
                energy_dissipation_rates.append(dissipation_rate)
                integral_length_scales.append(integral_scale)
            else:
                turbulence_scales.append(1.0)
                energy_dissipation_rates.append(0.1)
                integral_length_scales.append(1.0)
        
        df['turbulence_scale'] = turbulence_scales
        df['energy_dissipation_rate'] = energy_dissipation_rates
        df['integral_length_scale'] = integral_length_scales
        
        # 湍流分类
        df['turbulence_regime'] = np.where(
            df['turbulence_intensity'] < 0.1, 1,  # 低湍流
            np.where(
                df['turbulence_intensity'] < 0.2, 2,  # 中等湍流
                3  # 高湍流
            )
        )
        
        # 湍流间歇性
        df['turbulence_intermittency'] = df['turbulence_intensity'].rolling(12).std() / (df['turbulence_intensity'].rolling(12).mean() + 1e-6)
        
        return df

    def _create_temperature_stratification_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        温度分层效应
        大气稳定度对风速垂直分布的影响
        """
        if 'temperature_2m' not in df.columns:
            return df

        temperature = df['temperature_2m'].fillna(df['temperature_2m'].mean())

        # 温度梯度 (简化，假设标准大气温度递减率)
        standard_lapse_rate = -0.0065  # K/m

        # 实际温度梯度估算 (基于地面温度)
        # 假设在不同高度的温度
        temp_10m = temperature
        temp_80m = temperature + standard_lapse_rate * (80 - 2)  # 从2m到80m

        df['estimated_temp_80m'] = temp_80m
        df['temperature_gradient'] = (temp_80m - temp_10m) / (80 - 10)

        # 大气稳定度参数
        # Richardson数的简化估算
        if 'wind_speed_80m' in df.columns and 'wind_speed_10m' in df.columns:
            wind_80m = df['wind_speed_80m'].fillna(df['wind_speed_80m'].mean())
            wind_10m = df['wind_speed_10m'].fillna(df['wind_speed_10m'].mean())

            # 风速切变
            wind_shear = (wind_80m - wind_10m) / (80 - 10)

            # 浮力频率
            g = 9.81  # 重力加速度
            buoyancy_frequency = np.sqrt(g * df['temperature_gradient'] / (temperature + 273.15))

            # Richardson数
            df['richardson_number'] = buoyancy_frequency / (wind_shear ** 2 + 1e-6)

            # 大气稳定度分类
            df['atmospheric_stability_class'] = np.where(
                df['richardson_number'] > 0.25, 1,  # 稳定
                np.where(
                    df['richardson_number'] > -0.03, 2,  # 中性
                    3  # 不稳定
                )
            )
        else:
            df['richardson_number'] = 0
            df['atmospheric_stability_class'] = 2  # 默认中性

        # 温度分层对风速的影响
        stability_factor = np.where(
            df['atmospheric_stability_class'] == 1, 0.8,  # 稳定层抑制湍流
            np.where(
                df['atmospheric_stability_class'] == 2, 1.0,  # 中性
                1.2  # 不稳定层增强湍流
            )
        )
        df['stability_wind_factor'] = stability_factor

        # 热力学效应
        df['thermal_effect_strength'] = np.abs(df['temperature_gradient']) / 0.01  # 归一化

        # 逆温现象检测
        df['temperature_inversion'] = (df['temperature_gradient'] > 0).astype(int)

        # 温度分层稳定性
        df['thermal_stratification_stability'] = 1 / (1 + np.abs(df['temperature_gradient'].diff().fillna(0)) / 0.001)

        return df

    def _create_nonlinear_wind_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        非线性风速特征
        捕获风速的非线性动力学特性
        """
        if 'wind_speed_80m' not in df.columns:
            return df

        wind_speed = df['wind_speed_80m'].fillna(df['wind_speed_80m'].mean())

        # 风速的非线性变换
        df['wind_speed_sqrt'] = np.sqrt(wind_speed)
        df['wind_speed_cbrt'] = np.cbrt(wind_speed)
        df['wind_speed_log'] = np.log1p(wind_speed)
        df['wind_speed_exp'] = np.exp(-wind_speed / 10)  # 指数衰减

        # 风速的分段函数
        df['wind_speed_low_regime'] = np.where(wind_speed < 5, wind_speed, 0)
        df['wind_speed_mid_regime'] = np.where((wind_speed >= 5) & (wind_speed < 15), wind_speed, 0)
        df['wind_speed_high_regime'] = np.where(wind_speed >= 15, wind_speed, 0)

        # 风速的周期性非线性
        df['wind_speed_sin'] = np.sin(wind_speed / 5)  # 5 m/s为周期
        df['wind_speed_cos'] = np.cos(wind_speed / 5)

        # 风速的混沌特征
        # Lyapunov指数的简化估算
        wind_diff = wind_speed.diff().fillna(0)
        df['wind_lyapunov_approx'] = wind_diff.rolling(12).apply(
            lambda x: np.mean(np.log(np.abs(x) + 1e-6)) if len(x) > 1 else 0
        ).fillna(0)

        # 风速的分形维数估算
        def box_counting_dimension(series, max_box_size=10):
            """简化的盒计数维数"""
            if len(series) < max_box_size:
                return 1.0

            box_sizes = range(2, min(max_box_size, len(series) // 2))
            counts = []

            for box_size in box_sizes:
                # 将序列分割成盒子
                boxes = [series[i:i+box_size] for i in range(0, len(series), box_size)]
                # 计算非空盒子数量
                non_empty_boxes = sum(1 for box in boxes if len(box) > 0 and np.std(box) > 1e-6)
                counts.append(non_empty_boxes)

            if len(counts) > 1:
                # 拟合log-log关系
                log_sizes = np.log(box_sizes)
                log_counts = np.log(counts)
                slope, _, _, _, _ = stats.linregress(log_sizes, log_counts)
                return -slope
            else:
                return 1.0

        # 使用滑动窗口计算分形维数
        fractal_dimensions = []
        window_size = 24  # 6小时窗口

        for i in range(len(df)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(df), i + window_size // 2)

            window_data = wind_speed.iloc[start_idx:end_idx].values
            fractal_dim = box_counting_dimension(window_data)
            fractal_dimensions.append(fractal_dim)

        df['wind_fractal_dimension'] = fractal_dimensions

        # 风速的复杂度指标
        df['wind_complexity'] = df['wind_fractal_dimension'] * np.abs(df['wind_lyapunov_approx'])

        return df

    def _create_multivariate_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        多变量交互特征
        捕获不同气象变量之间的复杂交互
        """
        # 主要变量
        variables = ['wind_speed_80m', 'temperature_2m', 'rain']
        available_vars = [var for var in variables if var in df.columns]

        if len(available_vars) < 2:
            return df

        # 两两交互特征
        for i, var1 in enumerate(available_vars):
            for j, var2 in enumerate(available_vars[i+1:], i+1):
                # 只处理数值型变量
                if not pd.api.types.is_numeric_dtype(df[var1]) or not pd.api.types.is_numeric_dtype(df[var2]):
                    continue

                data1 = safe_fillna_with_mean(df[var1])
                data2 = safe_fillna_with_mean(df[var2])

                # 乘积交互
                df[f'{var1}_{var2}_product'] = data1 * data2

                # 比值交互
                df[f'{var1}_{var2}_ratio'] = data1 / (data2 + 1e-6)

                # 差值交互
                df[f'{var1}_{var2}_diff'] = data1 - data2

                # 相关性交互 (滑动窗口)
                correlation = data1.rolling(12).corr(data2).fillna(0)
                df[f'{var1}_{var2}_correlation'] = correlation

        # 三变量交互 (如果有三个数值型变量)
        numeric_vars = [var for var in available_vars if pd.api.types.is_numeric_dtype(df[var])]
        if len(numeric_vars) >= 3:
            var1, var2, var3 = numeric_vars[:3]
            data1 = safe_fillna_with_mean(df[var1])
            data2 = safe_fillna_with_mean(df[var2])
            data3 = safe_fillna_with_mean(df[var3])

            # 三元乘积
            df[f'{var1}_{var2}_{var3}_product'] = data1 * data2 * data3

            # 加权组合
            df[f'{var1}_{var2}_{var3}_weighted'] = 0.5 * data1 + 0.3 * data2 + 0.2 * data3

        # 主成分近似 (简化PCA) - 只使用数值型变量
        if len(numeric_vars) >= 2:
            # 标准化数据
            standardized_data = []
            for var in numeric_vars:
                data = safe_fillna_with_mean(df[var])
                standardized = (data - data.mean()) / (data.std() + 1e-6)
                standardized_data.append(standardized)

            # 第一主成分 (简化为等权重组合)
            df['multivariate_pc1'] = np.mean(standardized_data, axis=0)

            # 第二主成分 (简化为差异组合)
            if len(standardized_data) >= 2:
                df['multivariate_pc2'] = standardized_data[0] - standardized_data[1]

        return df
