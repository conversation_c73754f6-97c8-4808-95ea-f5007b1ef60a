# -*- coding: utf-8 -*-
import json
import subprocess
import sys
import os
from time import sleep
import io

def setup_encoding():
    """设置全局编码为UTF-8"""
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

def modify_feature_count(count):
    """修改config.json中的selected_features_count值"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['feature_selection']['selected_features_count'] = count
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
            
    except Exception as e:
        print(f"❌ 修改config.json时出错: {e}")
        raise

def run_scripts(i):
    """运行训练、预测、评分脚本"""
    env = {
        **os.environ,
        "PYTHONIOENCODING": "utf-8",
        "PYTHONUTF8": "1"
    }
    
    try:
        # 训练模型
        print(f"\n🔧 开始训练模型 (特征数={i})...")
        subprocess.run(
            [sys.executable, "train_and_export_model.py"],
            check=True,
            env=env
        )
        
        # 运行预测
        print("\n🔮 正在运行预测...")
        subprocess.run(
            [sys.executable, "predict_today.py"],
            check=True,
            env=env
        )
        
        # 运行评分并捕获输出
        print("\n📊 正在计算准确率...")
        result = subprocess.run(
            [sys.executable, "score.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            env=env
        )
        
        # 打印完整输出用于调试
        print("\n" + "="*50)
        print("SCORE.PY OUTPUT:")
        print(result.stdout)
        if result.stderr:
            print("SCORE.PY ERRORS:")
            print(result.stderr)
        print("="*50 + "\n")
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 脚本运行失败: {e}")
        if hasattr(e, 'output'):
            print("错误输出:\n", e.output)
        return None

def parse_accuracy(output):
    """从score.py输出中解析总准确率"""
    if not output:
        return None
        
    for line in output.split('\n'):
        if "总准确率:" in line:
            try:
                return float(line.split(':')[1].strip())
            except (IndexError, ValueError):
                continue
    return None

def main():
    setup_encoding()
    
    # 结果文件设置
    results_file = '特征数对应准确率.csv'
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write("特征数,总准确率\n")
    
    # 特征数范围：400 → 10，步长-10
    for i in range(185, 9, -5):
        try:
            print(f"\n{'#'*60}")
            print(f"🚀 开始处理特征数: {i}")
            print(f"{'#'*60}")
            
            # 1. 修改配置文件
            modify_feature_count(i)
            
            # 2. 运行脚本流程
            output = run_scripts(i)
            
            # 3. 解析并记录结果
            accuracy = parse_accuracy(output)
            
            if accuracy is not None:
                with open(results_file, 'a', encoding='utf-8') as f:
                    f.write(f"{i},{accuracy}\n")
                print(f"✅ 记录结果: 特征数={i}, 准确率={accuracy}")
            else:
                print(f"⚠️ 未能获取准确率，跳过记录 (特征数={i})")
                
            # 间隔防止资源冲突
            sleep(2)
                
        except Exception as e:
            print(f"❌ 处理特征数 {i} 时发生严重错误: {e}")
            continue

    print("\n🎉 所有特征数测试完成!")
    print(f"📁 结果已保存到: {results_file}")

if __name__ == "__main__":
    main()