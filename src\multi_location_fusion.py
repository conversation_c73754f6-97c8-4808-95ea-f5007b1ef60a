"""
多点位天气数据融合系统
实现基于空间权重的多点位天气数据融合和特征工程
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
from pathlib import Path
import math

warnings.filterwarnings('ignore')

class MultiLocationDataFusion:
    """
    多点位天气数据融合器
    
    实现5个点位（点位C + 4个角点）的天气数据融合：
    - 点位C（中心点）: [40.75, 95.75]
    - 点位1（左下角）: [40.625, 95.625]  
    - 点位2（左上角）: [40.625, 95.875]
    - 点位3（右下角）: [40.875, 95.625]
    - 点位4（右上角）: [40.875, 95.875]
    """
    
    def __init__(self, time_col: str = '时间'):
        self.time_col = time_col
        
        # 点位坐标定义
        self.locations = {
            'c': [40.75, 95.75],    # 中心点（目标点位）
            '1': [40.625, 95.625],  # 左下角
            '2': [40.625, 95.875],  # 左上角  
            '3': [40.875, 95.625],  # 右下角
            '4': [40.875, 95.875]   # 右上角
        }
        
        # 气象特征列表
        self.weather_features = [
            'wind_speed_10m', 'wind_speed_80m', 'wind_direction_10m', 
            'wind_direction_80m', 'wind_gusts_10m', 'temperature_2m', 
            'rain', 'apparent_temperature'
        ]
        
        # 距离权重参数
        self.distance_alpha = 2.0  # 距离衰减指数
        self.min_weight = 0.01     # 最小权重阈值
        
        # 缓存计算结果
        self.distance_weights = None
        self.location_data = {}
        
    def calculate_distance(self, coord1: List[float], coord2: List[float]) -> float:
        """
        计算两点间的地理距离（简化为欧几里得距离）
        
        Args:
            coord1: 坐标1 [lat, lon]
            coord2: 坐标2 [lat, lon]
            
        Returns:
            距离值
        """
        lat_diff = coord1[0] - coord2[0]
        lon_diff = coord1[1] - coord2[1]
        return math.sqrt(lat_diff**2 + lon_diff**2)
    
    def calculate_spatial_weights(self) -> Dict[str, float]:
        """
        计算各点位相对于中心点C的空间权重
        
        Returns:
            各点位的权重字典
        """
        if self.distance_weights is not None:
            return self.distance_weights
            
        center_coord = self.locations['c']
        weights = {}
        
        # 计算距离和基础权重
        distances = {}
        for loc_id, coord in self.locations.items():
            if loc_id == 'c':
                distances[loc_id] = 0.0  # 中心点距离为0
                weights[loc_id] = 1.0    # 中心点权重最高
            else:
                dist = self.calculate_distance(center_coord, coord)
                distances[loc_id] = dist
                # 反距离权重：w = 1/d^α
                weights[loc_id] = 1.0 / (dist ** self.distance_alpha)
        
        # 归一化权重
        total_weight = sum(weights.values())
        for loc_id in weights:
            weights[loc_id] = weights[loc_id] / total_weight
            # 应用最小权重阈值
            if weights[loc_id] < self.min_weight:
                weights[loc_id] = self.min_weight
        
        # 重新归一化
        total_weight = sum(weights.values())
        for loc_id in weights:
            weights[loc_id] = weights[loc_id] / total_weight
            
        self.distance_weights = weights
        return weights
    
    def load_location_data(self, data_dir: str, file_pattern: str, 
                          is_train: bool = True) -> Dict[str, pd.DataFrame]:
        """
        加载所有点位的天气数据
        
        Args:
            data_dir: 数据目录
            file_pattern: 文件名模式，如 "15min_历史气象20250228之前" 或 "15min_历史气象202503"
            is_train: 是否为训练数据
            
        Returns:
            各点位数据字典
        """
        data_dir = Path(data_dir)
        location_data = {}
        
        print(f"加载多点位天气数据...")
        
        for loc_id in self.locations.keys():
            # 构建文件名
            filename = f"点位{loc_id}_{file_pattern}.csv"
            file_path = data_dir / filename
            
            if file_path.exists():
                try:
                    # 尝试不同编码加载数据
                    df = None
                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if df is None:
                        print(f"  ⚠️ 无法读取文件: {filename}")
                        continue
                        
                    # 标准化时间列名
                    if '时间' in df.columns:
                        df = df.rename(columns={'时间': self.time_col})
                    elif 'ʱ��' in df.columns:  # 处理编码问题
                        df = df.rename(columns={'ʱ��': self.time_col})
                    
                    # 转换时间格式
                    df[self.time_col] = pd.to_datetime(df[self.time_col])
                    
                    # 检查必需的特征列
                    missing_features = [f for f in self.weather_features if f not in df.columns]
                    if missing_features:
                        print(f"  ⚠️ 点位{loc_id}缺少特征: {missing_features}")
                        continue
                    
                    # 只保留需要的列
                    keep_columns = [self.time_col] + self.weather_features
                    df = df[keep_columns].copy()
                    
                    # 数据质量检查
                    df = df.dropna()  # 删除缺失值
                    
                    location_data[loc_id] = df
                    print(f"  ✅ 点位{loc_id}: {len(df)} 条记录")
                    
                except Exception as e:
                    print(f"  ❌ 加载点位{loc_id}数据失败: {e}")
                    continue
            else:
                print(f"  ⚠️ 文件不存在: {filename}")
        
        if not location_data:
            raise ValueError("未能加载任何点位数据")
            
        print(f"成功加载 {len(location_data)} 个点位的数据")
        self.location_data = location_data
        return location_data
    
    def align_time_series(self, location_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        对齐所有点位的时间序列
        
        Args:
            location_data: 各点位数据字典
            
        Returns:
            时间对齐后的数据字典
        """
        print("对齐多点位时间序列...")
        
        # 找到所有点位的共同时间范围
        all_times = []
        for loc_id, df in location_data.items():
            all_times.extend(df[self.time_col].tolist())
        
        # 获取共同时间点
        common_times = set(all_times)
        for loc_id, df in location_data.items():
            loc_times = set(df[self.time_col].tolist())
            common_times = common_times.intersection(loc_times)
        
        common_times = sorted(list(common_times))
        print(f"  共同时间点数量: {len(common_times)}")
        
        # 过滤到共同时间点
        aligned_data = {}
        for loc_id, df in location_data.items():
            aligned_df = df[df[self.time_col].isin(common_times)].copy()
            aligned_df = aligned_df.sort_values(self.time_col).reset_index(drop=True)
            aligned_data[loc_id] = aligned_df
            print(f"  点位{loc_id}: {len(aligned_df)} 条记录")
        
        return aligned_data
    
    def create_weighted_fusion_features(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        创建基于权重的融合特征
        
        Args:
            aligned_data: 时间对齐的数据字典
            
        Returns:
            融合特征数据框
        """
        print("创建加权融合特征...")
        
        # 获取权重
        weights = self.calculate_spatial_weights()
        print(f"  空间权重: {weights}")
        
        # 以中心点C的时间为基准
        base_df = aligned_data['c'][[self.time_col]].copy()
        
        # 为每个气象特征创建加权平均
        for feature in self.weather_features:
            weighted_values = np.zeros(len(base_df))
            
            for loc_id, weight in weights.items():
                if loc_id in aligned_data:
                    loc_values = aligned_data[loc_id][feature].values
                    weighted_values += weight * loc_values
            
            base_df[f'{feature}_weighted'] = weighted_values
        
        print(f"  创建了 {len(self.weather_features)} 个加权融合特征")
        return base_df

    def create_spatial_gradient_features(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        创建空间梯度特征

        Args:
            aligned_data: 时间对齐的数据字典

        Returns:
            空间梯度特征数据框
        """
        print("创建空间梯度特征...")

        base_df = aligned_data['c'][[self.time_col]].copy()

        # 计算各方向的梯度
        for feature in self.weather_features:
            # 东西方向梯度 (点位3,4 vs 点位1,2)
            if all(loc in aligned_data for loc in ['1', '2', '3', '4']):
                east_avg = (aligned_data['3'][feature] + aligned_data['4'][feature]) / 2
                west_avg = (aligned_data['1'][feature] + aligned_data['2'][feature]) / 2
                base_df[f'{feature}_gradient_ew'] = east_avg - west_avg

                # 南北方向梯度 (点位3,1 vs 点位4,2)
                south_avg = (aligned_data['1'][feature] + aligned_data['3'][feature]) / 2
                north_avg = (aligned_data['2'][feature] + aligned_data['4'][feature]) / 2
                base_df[f'{feature}_gradient_ns'] = north_avg - south_avg

                # 梯度幅度
                gradient_magnitude = np.sqrt(
                    base_df[f'{feature}_gradient_ew']**2 +
                    base_df[f'{feature}_gradient_ns']**2
                )
                base_df[f'{feature}_gradient_magnitude'] = gradient_magnitude

        print(f"  创建了 {len(self.weather_features) * 3} 个空间梯度特征")
        return base_df

    def create_spatial_statistics_features(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        创建空间统计特征

        Args:
            aligned_data: 时间对齐的数据字典

        Returns:
            空间统计特征数据框
        """
        print("创建空间统计特征...")

        base_df = aligned_data['c'][[self.time_col]].copy()

        for feature in self.weather_features:
            # 收集所有点位的数值
            all_values = []
            for loc_id in ['c', '1', '2', '3', '4']:
                if loc_id in aligned_data:
                    all_values.append(aligned_data[loc_id][feature].values)

            if len(all_values) >= 2:
                values_array = np.array(all_values)  # shape: (n_locations, n_times)

                # 空间统计量
                base_df[f'{feature}_spatial_mean'] = np.mean(values_array, axis=0)
                base_df[f'{feature}_spatial_std'] = np.std(values_array, axis=0)
                base_df[f'{feature}_spatial_min'] = np.min(values_array, axis=0)
                base_df[f'{feature}_spatial_max'] = np.max(values_array, axis=0)
                base_df[f'{feature}_spatial_range'] = base_df[f'{feature}_spatial_max'] - base_df[f'{feature}_spatial_min']

                # 空间变异系数
                base_df[f'{feature}_spatial_cv'] = base_df[f'{feature}_spatial_std'] / (base_df[f'{feature}_spatial_mean'] + 1e-8)

        print(f"  创建了 {len(self.weather_features) * 6} 个空间统计特征")
        return base_df

    def create_spatial_difference_features(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        创建空间差分特征

        Args:
            aligned_data: 时间对齐的数据字典

        Returns:
            空间差分特征数据框
        """
        print("创建空间差分特征...")

        base_df = aligned_data['c'][[self.time_col]].copy()

        # 重要的点位对比组合
        important_pairs = [
            ('c', '1'), ('c', '2'), ('c', '3'), ('c', '4'),  # 中心点与各角点
            ('1', '3'), ('2', '4'),  # 对角线
            ('1', '2'), ('3', '4')   # 同侧对比
        ]

        for feature in self.weather_features:
            for loc1, loc2 in important_pairs:
                if loc1 in aligned_data and loc2 in aligned_data:
                    diff_name = f'{feature}_diff_{loc1}_{loc2}'
                    base_df[diff_name] = aligned_data[loc1][feature] - aligned_data[loc2][feature]

        print(f"  创建了 {len(self.weather_features) * len(important_pairs)} 个空间差分特征")
        return base_df

    def fuse_multi_location_data(self, data_dir: str, file_pattern: str,
                                is_train: bool = True) -> pd.DataFrame:
        """
        执行完整的多点位数据融合

        Args:
            data_dir: 数据目录
            file_pattern: 文件名模式
            is_train: 是否为训练数据

        Returns:
            融合后的完整特征数据框
        """
        print("=" * 60)
        print("开始多点位天气数据融合")
        print("=" * 60)

        # 1. 加载数据
        location_data = self.load_location_data(data_dir, file_pattern, is_train)

        # 2. 时间对齐
        aligned_data = self.align_time_series(location_data)

        # 3. 创建各类空间特征
        fusion_features = self.create_weighted_fusion_features(aligned_data)
        gradient_features = self.create_spatial_gradient_features(aligned_data)
        statistics_features = self.create_spatial_statistics_features(aligned_data)
        difference_features = self.create_spatial_difference_features(aligned_data)

        # 4. 合并所有特征
        print("\n合并所有空间特征...")

        # 以时间列为基准合并
        result_df = fusion_features.copy()

        # 合并其他特征（去除重复的时间列）
        for feature_df in [gradient_features, statistics_features, difference_features]:
            feature_cols = [col for col in feature_df.columns if col != self.time_col]
            for col in feature_cols:
                result_df[col] = feature_df[col]

        # 5. 添加原始中心点数据
        center_data = aligned_data['c'].copy()
        for feature in self.weather_features:
            result_df[feature] = center_data[feature]

        # 6. 特征统计
        original_features = len(self.weather_features)
        fusion_features_count = len(self.weather_features)  # 加权融合
        gradient_features_count = len(self.weather_features) * 3  # 梯度特征
        statistics_features_count = len(self.weather_features) * 6  # 统计特征
        difference_features_count = len(self.weather_features) * 8  # 差分特征

        total_new_features = (fusion_features_count + gradient_features_count +
                            statistics_features_count + difference_features_count)

        print(f"\n特征工程统计:")
        print(f"  原始特征: {original_features}")
        print(f"  加权融合特征: {fusion_features_count}")
        print(f"  空间梯度特征: {gradient_features_count}")
        print(f"  空间统计特征: {statistics_features_count}")
        print(f"  空间差分特征: {difference_features_count}")
        print(f"  新增特征总数: {total_new_features}")
        print(f"  最终特征总数: {len(result_df.columns) - 1}")  # 减去时间列

        print("\n" + "=" * 60)
        print("多点位天气数据融合完成!")
        print("=" * 60)

        return result_df
