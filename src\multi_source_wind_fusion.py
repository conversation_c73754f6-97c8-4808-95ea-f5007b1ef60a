"""
多源气象数据融合系统
实现34个气象模型的风速数据融合和特征工程
作者：国网缙云县供电公司 韩剑
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
from pathlib import Path
import logging

warnings.filterwarnings('ignore')

class MultiSourceWindFusion:
    """
    多源气象数据融合器
    
    整合34个不同气象模型的风速预测数据：
    - GFS (Global Forecast System): 美国NOAA全球预报系统
    - JMA (Japan Meteorological Agency): 日本气象厅模型
    - CMA GRAPES: 中国气象局GRAPES模型
    - ICON: 德国DWD的ICON模型
    - GEM: 加拿大环境部GEM模型
    - MeteoFrance: 法国气象局模型
    - UKMO: 英国气象局模型
    """
    
    def __init__(self, config_manager, time_col: str = '时间'):
        self.config = config_manager
        self.time_col = time_col
        self.logger = logging.getLogger(__name__)
        
        # 编码尝试列表
        self.encoding_list = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252']
        
        # 气象模型分类
        self.model_categories = {
            'gfs': ['gfs_seamless', 'gfs_global'],
            'jma': ['jma_seamless', 'jma_gsm'],
            'cma': ['cma_grapes_global'],
            'icon': ['icon_seamless', 'icon_global'],
            'gem': ['gem_seamless', 'gem_global'],
            'meteofrance': ['meteofrance_seamless', 'meteofrance_arpege_world'],
            'ukmo': ['ukmo_seamless', 'ukmo_global_deterministic_10km']
        }
        
        # 模型权重（基于历史表现和可靠性）
        self.model_weights = {
            'gfs_seamless': 0.15,
            'gfs_global': 0.12,
            'jma_seamless': 0.12,
            'jma_gsm': 0.08,
            'cma_grapes_global': 0.09,
            'icon_seamless': 0.13,
            'icon_global': 0.10,
            'gem_seamless': 0.11,
            'gem_global': 0.08,
            'meteofrance_seamless': 0.10,
            'meteofrance_arpege_world': 0.07,
            'ukmo_seamless': 0.12,
            'ukmo_global_deterministic_10km': 0.08
        }
        
        # 风力发电物理参数
        self.cut_in_speed = 3.0
        self.rated_speed = 12.0
        self.cut_out_speed = 25.0
        
    def load_multi_source_data(self, file_path: str) -> pd.DataFrame:
        """
        加载多源风速数据，处理编码问题
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            处理后的DataFrame
        """
        self.logger.info(f"正在加载多源风速数据: {file_path}")
        
        for encoding in self.encoding_list:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                
                # 修复列名编码问题
                if 'ʱ��' in df.columns:
                    df = df.rename(columns={'ʱ��': '时间'})
                
                # 标准化时间格式
                df[self.time_col] = pd.to_datetime(df[self.time_col])
                
                self.logger.info(f"✅ 数据加载成功，形状: {df.shape}")
                self.logger.info(f"   时间范围: {df[self.time_col].min()} 到 {df[self.time_col].max()}")
                
                return df
                
            except Exception as e:
                self.logger.debug(f"编码 {encoding} 失败: {e}")
                continue
                
        raise ValueError(f"无法用常见编码读取文件: {file_path}")
    
    def identify_wind_columns(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        识别不同类型的风速列
        
        Args:
            df: 输入DataFrame
            
        Returns:
            按类型分组的列名字典
        """
        wind_columns = {
            'wind_10m': [],
            'wind_80m': [],
            'gusts_10m': []
        }
        
        for col in df.columns:
            if 'wind_speed_10m' in col and col != 'wind_speed_10m':
                wind_columns['wind_10m'].append(col)
            elif 'wind_speed_80m' in col and col != 'wind_speed_80m':
                wind_columns['wind_80m'].append(col)
            elif 'wind_gusts_10m' in col and col != 'wind_gusts_10m':
                wind_columns['gusts_10m'].append(col)
        
        self.logger.info(f"识别到风速列: 10m风速({len(wind_columns['wind_10m'])}), "
                        f"80m风速({len(wind_columns['wind_80m'])}), "
                        f"10m阵风({len(wind_columns['gusts_10m'])})")
        
        return wind_columns
    
    def create_ensemble_features(self, df: pd.DataFrame, wind_columns: Dict[str, List[str]]) -> pd.DataFrame:
        """
        创建集成统计特征
        
        Args:
            df: 输入DataFrame
            wind_columns: 风速列分组
            
        Returns:
            包含集成特征的DataFrame
        """
        features = pd.DataFrame(index=df.index)
        
        for wind_type, columns in wind_columns.items():
            if not columns:
                continue
                
            prefix = f'multi_{wind_type}'
            wind_data = df[columns]
            
            # 基础统计特征
            features[f'{prefix}_mean'] = wind_data.mean(axis=1)
            features[f'{prefix}_std'] = wind_data.std(axis=1)
            features[f'{prefix}_median'] = wind_data.median(axis=1)
            features[f'{prefix}_max'] = wind_data.max(axis=1)
            features[f'{prefix}_min'] = wind_data.min(axis=1)
            features[f'{prefix}_range'] = features[f'{prefix}_max'] - features[f'{prefix}_min']
            
            # 分位数特征
            features[f'{prefix}_q25'] = wind_data.quantile(0.25, axis=1)
            features[f'{prefix}_q75'] = wind_data.quantile(0.75, axis=1)
            features[f'{prefix}_iqr'] = features[f'{prefix}_q75'] - features[f'{prefix}_q25']
            
            # 模型一致性特征
            features[f'{prefix}_agreement'] = 1 - (features[f'{prefix}_std'] / (features[f'{prefix}_mean'] + 1e-6))
            features[f'{prefix}_cv'] = features[f'{prefix}_std'] / (features[f'{prefix}_mean'] + 1e-6)
            
            # 预测置信度（基于模型一致性）
            features[f'{prefix}_confidence'] = np.exp(-features[f'{prefix}_cv'])
            
        self.logger.info(f"✅ 创建了 {len(features.columns)} 个集成统计特征")
        return features
    
    def create_weighted_features(self, df: pd.DataFrame, wind_columns: Dict[str, List[str]]) -> pd.DataFrame:
        """
        创建基于模型权重的融合特征
        
        Args:
            df: 输入DataFrame
            wind_columns: 风速列分组
            
        Returns:
            包含加权特征的DataFrame
        """
        features = pd.DataFrame(index=df.index)
        
        for wind_type, columns in wind_columns.items():
            if not columns:
                continue
                
            weighted_sum = pd.Series(0.0, index=df.index)
            total_weight = 0.0
            
            for col in columns:
                # 提取模型名称
                model_name = self._extract_model_name(col)
                weight = self.model_weights.get(model_name, 0.05)  # 默认权重
                
                weighted_sum += df[col] * weight
                total_weight += weight
            
            # 归一化加权平均
            if total_weight > 0:
                features[f'weighted_{wind_type}'] = weighted_sum / total_weight
            
        self.logger.info(f"✅ 创建了 {len(features.columns)} 个加权融合特征")
        return features
    
    def _extract_model_name(self, column_name: str) -> str:
        """
        从列名中提取模型名称
        
        Args:
            column_name: 列名
            
        Returns:
            模型名称
        """
        # 移除前缀和后缀，提取模型名称
        parts = column_name.replace('wind_speed_', '').replace('wind_gusts_', '').replace('10m_', '').replace('80m_', '').replace(' (m/s)', '')
        return parts
    
    def create_physics_features(self, df: pd.DataFrame, wind_columns: Dict[str, List[str]]) -> pd.DataFrame:
        """
        创建基于物理原理的特征
        
        Args:
            df: 输入DataFrame
            wind_columns: 风速列分组
            
        Returns:
            包含物理特征的DataFrame
        """
        features = pd.DataFrame(index=df.index)
        
        # 使用10m风速计算物理特征
        if wind_columns['wind_10m']:
            wind_10m_data = df[wind_columns['wind_10m']]
            mean_wind_10m = wind_10m_data.mean(axis=1)
            
            # 功率密度特征（风速立方关系）
            features['multi_power_density'] = mean_wind_10m ** 3
            
            # 风速区间特征
            features['multi_wind_in_range'] = ((mean_wind_10m >= self.cut_in_speed) & 
                                             (mean_wind_10m <= self.cut_out_speed)).astype(int)
            
            # 额定风速接近度
            features['multi_rated_proximity'] = np.exp(-np.abs(mean_wind_10m - self.rated_speed) / 3.0)
            
            # 发电适宜性指标
            features['multi_generation_suitability'] = np.where(
                mean_wind_10m < self.cut_in_speed, 0,
                np.where(mean_wind_10m > self.cut_out_speed, 0,
                        np.minimum(mean_wind_10m / self.rated_speed, 1.0))
            )
        
        # 风切变特征（如果有80m风速）
        if wind_columns['wind_10m'] and wind_columns['wind_80m']:
            mean_wind_10m = df[wind_columns['wind_10m']].mean(axis=1)
            mean_wind_80m = df[wind_columns['wind_80m']].mean(axis=1)
            
            # 风切变指数
            features['multi_wind_shear'] = np.log(mean_wind_80m / (mean_wind_10m + 1e-6)) / np.log(80/10)
            
            # 垂直风速梯度
            features['multi_wind_gradient'] = (mean_wind_80m - mean_wind_10m) / (80 - 10)
        
        self.logger.info(f"✅ 创建了 {len(features.columns)} 个物理特征")
        return features

    def create_temporal_consistency_features(self, df: pd.DataFrame, wind_columns: Dict[str, List[str]]) -> pd.DataFrame:
        """
        创建时序一致性特征

        Args:
            df: 输入DataFrame
            wind_columns: 风速列分组

        Returns:
            包含时序一致性特征的DataFrame
        """
        features = pd.DataFrame(index=df.index)

        for wind_type, columns in wind_columns.items():
            if not columns or len(columns) < 2:
                continue

            wind_data = df[columns]
            prefix = f'temporal_{wind_type}'

            # 时间一致性（滑动窗口）
            window_size = min(4, len(df))  # 1小时窗口（4个15分钟）
            if window_size > 1:
                rolling_std = wind_data.rolling(window=window_size, min_periods=1).std()
                rolling_mean = wind_data.rolling(window=window_size, min_periods=1).mean()

                # 时序稳定性
                features[f'{prefix}_stability'] = 1 - (rolling_std.mean(axis=1) / (rolling_mean.mean(axis=1) + 1e-6))

                # 趋势一致性
                wind_diff = wind_data.diff()
                trend_agreement = (wind_diff > 0).sum(axis=1) / len(columns)
                features[f'{prefix}_trend_agreement'] = trend_agreement

            # 模型间相关性
            if len(columns) >= 2:
                correlations = []
                for i in range(len(columns)):
                    for j in range(i+1, len(columns)):
                        corr = wind_data.iloc[:, i].rolling(window=24, min_periods=12).corr(wind_data.iloc[:, j])
                        correlations.append(corr)

                if correlations:
                    features[f'{prefix}_avg_correlation'] = pd.concat(correlations, axis=1).mean(axis=1)

        self.logger.info(f"✅ 创建了 {len(features.columns)} 个时序一致性特征")
        return features

    def create_model_category_features(self, df: pd.DataFrame, wind_columns: Dict[str, List[str]]) -> pd.DataFrame:
        """
        创建按模型类别分组的特征

        Args:
            df: 输入DataFrame
            wind_columns: 风速列分组

        Returns:
            包含模型类别特征的DataFrame
        """
        features = pd.DataFrame(index=df.index)

        for wind_type, columns in wind_columns.items():
            if not columns:
                continue

            # 按模型类别分组
            for category, models in self.model_categories.items():
                category_cols = [col for col in columns if any(model in col for model in models)]

                if category_cols:
                    category_data = df[category_cols]
                    prefix = f'{category}_{wind_type}'

                    # 类别统计特征
                    features[f'{prefix}_mean'] = category_data.mean(axis=1)
                    features[f'{prefix}_std'] = category_data.std(axis=1)

                    # 类别一致性
                    features[f'{prefix}_consistency'] = 1 - (category_data.std(axis=1) / (category_data.mean(axis=1) + 1e-6))

        self.logger.info(f"✅ 创建了 {len(features.columns)} 个模型类别特征")
        return features

    def fuse_multi_source_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        主要的多源数据融合方法

        Args:
            df: 包含多源风速数据的DataFrame

        Returns:
            包含所有融合特征的DataFrame，以时间列为索引
        """
        self.logger.info("🚀 开始多源风速数据融合...")

        # 确保时间列为索引
        if self.time_col in df.columns:
            df_work = df.set_index(self.time_col).copy()
        else:
            df_work = df.copy()

        # 识别风速列
        wind_columns = self.identify_wind_columns(df_work)

        # 创建各类特征
        ensemble_features = self.create_ensemble_features(df_work, wind_columns)
        weighted_features = self.create_weighted_features(df_work, wind_columns)
        physics_features = self.create_physics_features(df_work, wind_columns)
        temporal_features = self.create_temporal_consistency_features(df_work, wind_columns)
        category_features = self.create_model_category_features(df_work, wind_columns)

        # 合并所有特征
        all_features = pd.concat([
            ensemble_features,
            weighted_features,
            physics_features,
            temporal_features,
            category_features
        ], axis=1)

        # 处理无穷大和NaN值
        all_features = all_features.replace([np.inf, -np.inf], np.nan)
        all_features = all_features.fillna(0)

        # 确保索引是时间类型
        if self.time_col in df.columns:
            all_features.index = pd.to_datetime(df[self.time_col])
            all_features.index.name = self.time_col

        self.logger.info(f"✅ 多源数据融合完成，生成 {len(all_features.columns)} 个特征")
        self.logger.info(f"   特征列表: {list(all_features.columns)}")

        return all_features

    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """
        获取特征重要性分组，用于特征选择

        Returns:
            特征分组字典
        """
        return {
            'ensemble_stats': ['multi_wind_10m_mean', 'multi_wind_80m_mean', 'multi_gusts_10m_mean'],
            'model_agreement': ['multi_wind_10m_agreement', 'multi_wind_80m_agreement'],
            'physics_weighted': ['weighted_wind_10m', 'weighted_wind_80m'],
            'temporal_consistency': ['temporal_wind_10m_stability', 'temporal_wind_80m_stability'],
            'physics_derived': ['multi_power_density', 'multi_generation_suitability', 'multi_wind_shear']
        }
