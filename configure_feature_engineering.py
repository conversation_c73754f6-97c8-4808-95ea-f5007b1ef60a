"""
特征工程模式配置脚本
方便用户切换不同的特征工程模式
"""

import json
from pathlib import Path

def load_config():
    """加载配置文件"""
    config_file = Path("config.json")
    if not config_file.exists():
        print("❌ config.json 文件不存在")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_config(config):
    """保存配置文件"""
    config_file = Path("config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    print("✓ 配置已保存")

def show_current_config(config):
    """显示当前配置"""
    print("\n当前特征工程配置:")
    print("=" * 50)
    
    mode = config.get('feature_engineering', {}).get('mode', 3)
    mode_descriptions = config.get('feature_engineering', {}).get('mode_description', {})
    
    print(f"当前模式: {mode}")
    if str(mode) in mode_descriptions:
        print(f"模式说明: {mode_descriptions[str(mode)]}")
    
    # 特征选择配置
    feature_selection = config.get('feature_selection', {})
    print(f"\n特征选择配置:")
    print(f"  启用特征选择: {feature_selection.get('enable_feature_selection', True)}")
    print(f"  保留特征数: {feature_selection.get('selected_features_count', 100)}")
    print(f"  选择方法: {feature_selection.get('selection_method', 'importance')}")

def show_mode_descriptions():
    """显示所有模式说明"""
    print("\n特征工程模式说明:")
    print("=" * 50)
    print("模式1: 传统特征工程 + 特征选择")
    print("  - 时间特征工程 (基础时间特征、周期性特征、滞后特征等)")
    print("  - 物理特征工程 (风速特征、风向特征、温度密度特征等)")
    print("  - 特征选择 (基于重要性筛选)")
    print("  - 特点: 特征数量较少，计算速度快")
    
    print("\n模式2: 综合特征工程 + 特征选择")
    print("  - 方案1: 数据质量优化")
    print("  - 方案2: 时间序列深度挖掘")
    print("  - 方案4: 突破性特征工程")
    print("  - 方案5: 深度物理建模")
    print("  - 特征选择 (基于重要性筛选)")
    print("  - 特点: 高级特征，可能提供更好精度")
    
    print("\n模式3: 传统特征工程 + 综合特征工程 + 特征选择")
    print("  - 包含模式1的所有特征")
    print("  - 包含模式2的所有特征")
    print("  - 特征选择 (基于重要性筛选)")
    print("  - 特点: 特征数量最多，计算成本最高，可能精度最好")

def configure_mode():
    """配置特征工程模式"""
    config = load_config()
    if config is None:
        return
    
    show_current_config(config)
    show_mode_descriptions()
    
    print("\n请选择特征工程模式:")
    print("1 - 传统特征工程 + 特征选择")
    print("2 - 综合特征工程 + 特征选择")
    print("3 - 传统特征工程 + 综合特征工程 + 特征选择")
    print("0 - 退出")
    
    try:
        choice = int(input("\n请输入选择 (0-3): "))
        
        if choice == 0:
            print("退出配置")
            return
        elif choice in [1, 2, 3]:
            # 更新配置
            if 'feature_engineering' not in config:
                config['feature_engineering'] = {}
            
            config['feature_engineering']['mode'] = choice
            
            # 询问是否修改特征选择配置
            print(f"\n已选择模式 {choice}")
            
            modify_selection = input("是否修改特征选择配置? (y/n): ").lower().strip()
            if modify_selection == 'y':
                configure_feature_selection(config)
            
            save_config(config)
            
            print(f"\n✓ 特征工程模式已设置为: {choice}")
            
        else:
            print("❌ 无效选择")
            
    except ValueError:
        print("❌ 请输入有效数字")

def configure_feature_selection(config):
    """配置特征选择参数"""
    print("\n配置特征选择参数:")
    
    # 是否启用特征选择
    current_enable = config.get('feature_selection', {}).get('enable_feature_selection', True)
    enable_input = input(f"启用特征选择? (当前: {current_enable}) [y/n]: ").lower().strip()
    if enable_input in ['y', 'yes']:
        enable_selection = True
    elif enable_input in ['n', 'no']:
        enable_selection = False
    else:
        enable_selection = current_enable
    
    # 保留特征数
    current_count = config.get('feature_selection', {}).get('selected_features_count', 100)
    count_input = input(f"保留特征数 (当前: {current_count}): ").strip()
    if count_input:
        try:
            selected_count = int(count_input)
        except ValueError:
            print("❌ 无效数字，使用当前值")
            selected_count = current_count
    else:
        selected_count = current_count
    
    # 更新配置
    if 'feature_selection' not in config:
        config['feature_selection'] = {}
    
    config['feature_selection']['enable_feature_selection'] = enable_selection
    config['feature_selection']['selected_features_count'] = selected_count
    
    print(f"✓ 特征选择配置已更新")

def main():
    """主函数"""
    print("特征工程模式配置工具")
    print("=" * 50)
    
    try:
        configure_mode()
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {e}")

if __name__ == "__main__":
    main()
