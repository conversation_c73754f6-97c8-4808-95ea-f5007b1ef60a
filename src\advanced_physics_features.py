"""
方案1：深度物理建模增强
实现威布尔分布风速建模、雷诺数效应、叶尖速比优化、尾流效应建模等深度物理特征
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.optimize import minimize_scalar
from scipy.special import gamma  # 使用scipy.special.gamma替代numpy.gamma
import warnings
warnings.filterwarnings('ignore')

class AdvancedPhysicsFeatures:
    """
    深度物理建模特征提取器
    基于风力发电的高级物理原理
    """
    
    def __init__(self):
        # 风力发电机参数
        self.rotor_diameter = 120.0  # 转子直径 (m)
        self.hub_height = 80.0       # 轮毂高度 (m)
        self.rated_power = 2.5       # 额定功率 (MW)
        self.rated_wind_speed = 12.0 # 额定风速 (m/s)
        self.cut_in_speed = 3.0      # 切入风速 (m/s)
        self.cut_out_speed = 25.0    # 切出风速 (m/s)
        
        # 物理常数
        self.air_density_std = 1.225  # 标准空气密度 (kg/m³)
        self.kinematic_viscosity = 1.5e-5  # 空气运动粘度 (m²/s)
        self.von_karman_constant = 0.41    # 冯卡门常数
        
    def create_all_advanced_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建所有深度物理特征
        """
        df = df.copy()
        
        print("创建深度物理建模特征...")
        
        # 1. 威布尔分布风速建模
        df = self._create_weibull_wind_features(df)
        
        # 2. 雷诺数效应
        df = self._create_reynolds_number_features(df)
        
        # 3. 叶尖速比优化
        df = self._create_tip_speed_ratio_features(df)
        
        # 4. 尾流效应建模
        df = self._create_wake_effect_features(df)
        
        # 5. 高级空气动力学特征
        df = self._create_advanced_aerodynamic_features(df)
        
        # 6. 大气边界层特征
        df = self._create_boundary_layer_features(df)
        
        print(f" 深度物理建模特征创建完成")
        
        return df
    
    def _create_weibull_wind_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        威布尔分布风速建模
        风速通常符合威布尔分布，可以提取分布参数作为特征
        """
        if 'wind_speed_80m' not in df.columns:
            return df
        
        wind_speed = df['wind_speed_80m'].values
        
        # 使用滑动窗口估计威布尔参数
        window_size = min(50, len(df) // 4)
        
        shape_params = []
        scale_params = []
        
        for i in range(len(df)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(df), i + window_size // 2)
            
            window_data = wind_speed[start_idx:end_idx]
            window_data = window_data[window_data > 0]  # 移除零值
            
            if len(window_data) > 10:
                try:
                    # 拟合威布尔分布
                    shape, loc, scale = stats.weibull_min.fit(window_data, floc=0)
                    # 确保参数在合理范围内
                    shape = np.clip(shape, 0.5, 5.0)
                    scale = np.clip(scale, 0.1, 50.0)
                    shape_params.append(shape)
                    scale_params.append(scale)
                except Exception as e:
                    # 使用经验参数
                    mean_wind = np.mean(window_data)
                    shape_params.append(2.0)  # 典型的威布尔形状参数
                    scale_params.append(mean_wind * 1.128)  # 经验关系
            else:
                mean_wind = np.mean(window_data) if len(window_data) > 0 else 8.0
                shape_params.append(2.0)
                scale_params.append(mean_wind * 1.128)
        
        df['weibull_shape'] = shape_params
        df['weibull_scale'] = scale_params
        
        # 威布尔分布相关特征 (使用向量化的gamma函数)
        try:
            # 安全的gamma函数计算
            shape_vals = np.array(df['weibull_shape'])
            scale_vals = np.array(df['weibull_scale'])

            # 避免除零和无效值
            shape_vals = np.clip(shape_vals, 0.1, 10.0)
            scale_vals = np.clip(scale_vals, 0.1, 50.0)

            # 威布尔均值
            gamma_1 = np.array([gamma(1 + 1/s) for s in shape_vals])
            df['weibull_mean_wind'] = scale_vals * gamma_1

            # 威布尔方差
            gamma_2 = np.array([gamma(1 + 2/s) for s in shape_vals])
            df['weibull_wind_variance'] = (scale_vals ** 2) * (gamma_2 - gamma_1**2)

            # 当前风速在威布尔分布中的位置
            df['wind_weibull_percentile'] = stats.weibull_min.cdf(
                wind_speed, shape_vals, scale=scale_vals
            )

            # 威布尔功率密度
            gamma_3 = np.array([gamma(1 + 3/s) for s in shape_vals])
            df['weibull_power_density'] = 0.5 * self.air_density_std * (scale_vals ** 3) * gamma_3

        except Exception as e:
            print(f"威布尔特征计算警告: {e}")
            # 使用默认值
            df['weibull_mean_wind'] = wind_speed
            df['weibull_wind_variance'] = wind_speed * 0.1
            df['wind_weibull_percentile'] = 0.5
            df['weibull_power_density'] = 0.5 * self.air_density_std * wind_speed ** 3
        
        return df
    
    def _create_reynolds_number_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        雷诺数效应建模
        雷诺数影响叶片表面的边界层特性和升阻比
        """
        if 'wind_speed_80m' not in df.columns:
            return df
        
        wind_speed = df['wind_speed_80m']
        
        # 叶片弦长 (估算)
        chord_length = self.rotor_diameter * 0.08  # 典型弦长约为直径的8%
        
        # 雷诺数计算
        df['reynolds_number'] = wind_speed * chord_length / self.kinematic_viscosity
        
        # 雷诺数对升阻比的影响 (经验公式)
        re_normalized = df['reynolds_number'] / 1e6  # 归一化到百万级
        df['reynolds_lift_drag_ratio'] = 50 * (1 - np.exp(-re_normalized / 2))
        
        # 雷诺数对功率系数的影响
        df['reynolds_power_coefficient'] = 0.45 * (1 - np.exp(-re_normalized / 1.5))
        
        # 临界雷诺数指标
        critical_re = 5e5  # 临界雷诺数
        df['reynolds_criticality'] = np.tanh((df['reynolds_number'] - critical_re) / critical_re)
        
        # 雷诺数稳定性指标
        df['reynolds_stability'] = 1 / (1 + np.abs(df['reynolds_number'].diff().fillna(0)) / 1e5)
        
        return df
    
    def _create_tip_speed_ratio_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        叶尖速比优化特征
        叶尖速比是影响风力发电机效率的关键参数
        """
        if 'wind_speed_80m' not in df.columns:
            return df
        
        wind_speed = df['wind_speed_80m']
        
        # 最优叶尖速比 (通常在7-9之间)
        optimal_tsr = 8.0
        
        # 根据风速估算实际叶尖速比
        # 假设转速控制策略：低风速时保持最优TSR，高风速时限制转速
        rotor_radius = self.rotor_diameter / 2
        max_rotor_speed = 20.0  # 最大转速 (rpm)
        max_tip_speed = max_rotor_speed * 2 * np.pi / 60 * rotor_radius
        
        # 计算实际叶尖速比
        optimal_tip_speed = optimal_tsr * wind_speed
        actual_tip_speed = np.minimum(optimal_tip_speed, max_tip_speed)
        df['tip_speed_ratio'] = actual_tip_speed / (wind_speed + 1e-6)
        
        # TSR偏离最优值的程度
        df['tsr_deviation'] = np.abs(df['tip_speed_ratio'] - optimal_tsr)
        df['tsr_efficiency'] = np.exp(-df['tsr_deviation'] / 2)  # TSR效率因子
        
        # TSR对功率系数的影响 (基于理论曲线)
        tsr = df['tip_speed_ratio']
        df['tsr_power_coefficient'] = 0.5 * (
            116 / (1/tsr - 0.08) - 0.4 * 5 - 5
        ) * np.exp(-21 / (1/tsr - 0.08))
        df['tsr_power_coefficient'] = np.clip(df['tsr_power_coefficient'], 0, 0.5)
        
        # TSR稳定性
        df['tsr_stability'] = 1 / (1 + np.abs(df['tip_speed_ratio'].diff().fillna(0)))
        
        # 最优TSR区间指标
        df['in_optimal_tsr_range'] = ((tsr >= 7) & (tsr <= 9)).astype(int)
        
        return df
    
    def _create_wake_effect_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        尾流效应建模
        考虑风向和风速对尾流的影响
        """
        if 'wind_speed_80m' not in df.columns or 'wind_direction_80m' not in df.columns:
            return df
        
        wind_speed = df['wind_speed_80m']
        wind_direction = df['wind_direction_80m']
        
        # 尾流衰减系数 (基于Jensen模型)
        thrust_coefficient = 0.8  # 推力系数
        wake_decay_constant = 0.075  # 尾流衰减常数
        
        # 假设上游有风机的情况 (简化模型)
        upstream_distance = 5 * self.rotor_diameter  # 上游距离
        
        # 尾流半径
        df['wake_radius'] = (self.rotor_diameter / 2) * (
            1 + wake_decay_constant * upstream_distance / self.rotor_diameter
        )
        
        # 尾流速度亏损
        df['wake_velocity_deficit'] = wind_speed * (
            1 - np.sqrt(1 - thrust_coefficient)
        ) / (1 + wake_decay_constant * upstream_distance / self.rotor_diameter) ** 2
        
        # 有效风速 (考虑尾流影响)
        df['effective_wind_speed'] = wind_speed - df['wake_velocity_deficit']
        
        # 尾流湍流强度增加
        df['wake_turbulence_intensity'] = 0.1 * thrust_coefficient / (
            1 + wake_decay_constant * upstream_distance / self.rotor_diameter
        )
        
        # 风向变化对尾流的影响
        wind_direction_change = np.abs(wind_direction.diff().fillna(0))
        df['wake_directional_effect'] = np.exp(-wind_direction_change / 30)  # 30度为特征尺度
        
        # 尾流恢复指标
        df['wake_recovery_factor'] = 1 - np.exp(-upstream_distance / (3 * self.rotor_diameter))
        
        return df

    def _create_advanced_aerodynamic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        高级空气动力学特征
        """
        if 'wind_speed_80m' not in df.columns:
            return df

        wind_speed = df['wind_speed_80m']

        # 动压
        df['dynamic_pressure'] = 0.5 * self.air_density_std * wind_speed ** 2

        # 功率密度
        df['power_density'] = 0.5 * self.air_density_std * wind_speed ** 3

        # 扫掠面积功率密度
        swept_area = np.pi * (self.rotor_diameter / 2) ** 2
        df['swept_area_power'] = df['power_density'] * swept_area / 1e6  # MW

        # 理论最大功率 (贝茨极限)
        betz_limit = 16/27  # 贝茨系数
        df['betz_limit_power'] = betz_limit * df['swept_area_power']

        # 实际功率系数估算
        if 'wind_speed_10m' in df.columns:
            # 基于风切变的功率系数修正
            wind_shear = (wind_speed / (df['wind_speed_10m'] + 1e-6)) ** 0.2
            df['wind_shear_power_coefficient'] = 0.4 * np.exp(-np.abs(wind_shear - 1.2) / 0.5)
        else:
            df['wind_shear_power_coefficient'] = 0.4

        # 攻角效应 (简化模型)
        if 'wind_direction_80m' in df.columns and 'wind_direction_10m' in df.columns:
            direction_diff = np.abs(df['wind_direction_80m'] - df['wind_direction_10m'])
            direction_diff = np.minimum(direction_diff, 360 - direction_diff)  # 处理角度环绕
            df['attack_angle_effect'] = np.cos(np.radians(direction_diff))
        else:
            df['attack_angle_effect'] = 1.0

        # 失速检测
        stall_wind_speed = 15.0  # 失速风速阈值
        df['stall_probability'] = 1 / (1 + np.exp(-(wind_speed - stall_wind_speed) / 2))

        return df

    def _create_boundary_layer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        大气边界层特征
        """
        if 'wind_speed_80m' not in df.columns or 'wind_speed_10m' not in df.columns:
            return df

        wind_80m = df['wind_speed_80m']
        wind_10m = df['wind_speed_10m']

        # 风切变指数
        df['wind_shear_exponent'] = np.log(wind_80m / (wind_10m + 1e-6)) / np.log(80 / 10)
        df['wind_shear_exponent'] = np.clip(df['wind_shear_exponent'], 0, 1)

        # 摩擦速度
        df['friction_velocity'] = self.von_karman_constant * wind_10m / np.log(10 / 0.1)  # 假设粗糙度0.1m

        # 边界层高度估算
        if 'temperature_2m' in df.columns:
            # 基于温度梯度的稳定度参数
            temp = df['temperature_2m']
            temp_gradient = temp.diff().fillna(0)  # 简化的温度梯度

            # Monin-Obukhov长度 (简化)
            df['monin_obukhov_length'] = 100 * np.exp(-np.abs(temp_gradient))

            # 大气稳定度分类
            df['atmospheric_stability'] = np.where(
                temp_gradient > 0.5, 1,  # 稳定
                np.where(temp_gradient < -0.5, -1, 0)  # 不稳定, 中性
            )
        else:
            df['monin_obukhov_length'] = 100
            df['atmospheric_stability'] = 0

        # 边界层风速廓线拟合质量
        theoretical_wind_80m = wind_10m * (80 / 10) ** df['wind_shear_exponent']
        df['wind_profile_fit_quality'] = 1 - np.abs(wind_80m - theoretical_wind_80m) / (wind_80m + 1e-6)
        df['wind_profile_fit_quality'] = np.clip(df['wind_profile_fit_quality'], 0, 1)

        # 湍流动能
        if 'wind_gusts_10m' in df.columns:
            gust_factor = df['wind_gusts_10m'] / (wind_10m + 1e-6)
            df['turbulent_kinetic_energy'] = 0.5 * (gust_factor - 1) ** 2 * wind_10m ** 2
        else:
            df['turbulent_kinetic_energy'] = 0.1 * wind_10m ** 2

        # 边界层厚度估算
        df['boundary_layer_height'] = 1000 * (1 + df['atmospheric_stability'] * 0.3)  # 简化模型

        # 风速在边界层中的相对位置
        df['height_in_boundary_layer'] = self.hub_height / df['boundary_layer_height']

        return df
