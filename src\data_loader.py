"""
数据加载和预处理模块
实现高精度风力发电功率预测的数据处理流程
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import warnings
from pathlib import Path

# 导入配置管理器
try:
    from .config_manager import ConfigManager
except ImportError:
    import sys
    sys.path.append(str(Path(__file__).parent))
    from config_manager import ConfigManager

class WindPowerDataLoader:
    """
    风力发电数据加载器
    处理训练集和测试集的加载、验证和基础预处理
    """
    
    def __init__(self, config_file: str = "config.json"):
        # 加载配置
        self.config = ConfigManager(config_file)

        # 从配置获取数据目录和文件名
        self.data_dir = Path(self.config.get('data_files.data_directory', 'data'))

        # 获取训练和测试数据目录，如果未配置则使用data_directory作为默认值
        default_data_dir = self.config.get('data_files.data_directory', 'data')
        self.train_data_dir = Path(self.config.get('data_files.train_data_directory', default_data_dir))
        self.test_data_dir = Path(self.config.get('data_files.test_data_directory', default_data_dir))

        # 检查是否启用多点位模式
        multi_location_config = self.config.get('multi_location', {})
        if multi_location_config.get('enable', False):
            # 多点位模式：使用点位数据文件
            self.train_file = None  # 不使用单一训练文件
            self.test_file = None   # 不使用单一测试文件
        else:
            # 单点位模式：使用传统配置
            self.train_file = self.config.get('data_files.train_file', '训练集20250228之前.csv')
            self.test_file = self.config.get('data_files.test_file', '测试集202503.csv')

        # 从配置获取列名
        self.target_column = self.config.get('features.target_column', '理论功率 (MW)')
        self.time_column = self.config.get('features.time_column', '时间')

        # 预期的特征列（包含时间列）
        base_features = self.config.get('features.feature_columns', [
            "wind_speed_10m", "wind_speed_80m",
            "wind_direction_10m", "wind_direction_80m", "wind_gusts_10m",
            "temperature_2m", "rain", "apparent_temperature"
        ])
        self.feature_columns = [self.time_column] + base_features

        # 编码设置
        self.preferred_encodings = self.config.get('encoding.preferred_encodings',
                                                 ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252'])
        
    def load_training_data(self) -> pd.DataFrame:
        """
        加载训练数据集
        对于多点位模式，需要加载点位C的发电功率数据和天气数据
        """
        # 检查是否启用多点位模式
        multi_location_config = self.config.get('multi_location', {})
        if multi_location_config.get('enable', False):
            return self._load_multi_location_training_data()
        else:
            return self._load_single_location_training_data()

    def _load_single_location_training_data(self) -> pd.DataFrame:
        """
        加载单点位训练数据（原有逻辑）
        """
        if self.train_file is None:
            raise ValueError("单点位模式下未配置训练文件，请检查配置或启用多点位模式")

        train_path = self.data_dir / self.train_file

        if not train_path.exists():
            raise FileNotFoundError(f"训练数据文件不存在: {train_path}")

        print(f"正在加载训练数据: {train_path}")

        # 使用配置的编码格式
        encodings = self.preferred_encodings
        df = None
        successful_encoding = None

        for encoding in encodings:
            try:
                df = pd.read_csv(train_path, encoding=encoding)
                successful_encoding = encoding
                print(f" 成功使用 {encoding} 编码加载数据")
                break
            except UnicodeDecodeError:
                print(f"  尝试 {encoding} 编码失败，继续尝试下一个...")
                continue
            except Exception as e:
                print(f"  使用 {encoding} 编码时出现其他错误: {e}")
                continue

        if df is None:
            raise ValueError(f"无法使用任何编码格式读取文件: {train_path}。请检查文件格式和编码。")

        # 验证数据结构
        self._validate_data_structure(df, "训练集")

        # 基础预处理
        df = self._basic_preprocessing(df)

        print(f"训练数据加载完成: {len(df)} 条记录")
        print(f"时间范围: {df['时间'].min()} 到 {df['时间'].max()}")

        return df

    def _load_multi_location_training_data(self) -> pd.DataFrame:
        """
        加载多点位训练数据
        """
        print("加载多点位训练数据...")

        # 1. 加载点位C的发电功率数据
        multi_location_config = self.config.get('multi_location', {})
        target_power_file = multi_location_config.get('target_power_file', '点位c_发电功率.csv')
        power_file = self.train_data_dir / target_power_file

        if not power_file.exists():
            raise FileNotFoundError(f"发电功率数据文件不存在: {power_file}")

        print(f"  加载发电功率数据: {power_file}")

        # 加载发电功率数据
        power_df = None
        for encoding in self.preferred_encodings:
            try:
                power_df = pd.read_csv(power_file, encoding=encoding)
                print(f"    成功使用 {encoding} 编码")
                break
            except UnicodeDecodeError:
                continue

        if power_df is None:
            raise ValueError(f"无法读取发电功率文件: {power_file}")

        # 标准化列名
        if '时间' in power_df.columns:
            power_df = power_df.rename(columns={'时间': self.time_column})
        elif 'ʱ��' in power_df.columns:  # 处理编码问题
            power_df = power_df.rename(columns={'ʱ��': self.time_column})

        if '理论功率 (MW)' in power_df.columns:
            power_df = power_df.rename(columns={'理论功率 (MW)': self.target_column})
        elif '���۹��� (MW)' in power_df.columns:  # 处理编码问题
            power_df = power_df.rename(columns={'���۹��� (MW)': self.target_column})

        # 2. 加载点位C的天气数据
        train_pattern = multi_location_config.get('train_file_pattern', '15min_历史气象20250228之前')
        weather_file = self.train_data_dir / f"点位c_{train_pattern}.csv"

        if not weather_file.exists():
            raise FileNotFoundError(f"天气数据文件不存在: {weather_file}")

        print(f"  加载天气数据: {weather_file}")

        # 加载天气数据
        weather_df = None
        for encoding in self.preferred_encodings:
            try:
                weather_df = pd.read_csv(weather_file, encoding=encoding)
                print(f"    成功使用 {encoding} 编码")
                break
            except UnicodeDecodeError:
                continue

        if weather_df is None:
            raise ValueError(f"无法读取天气文件: {weather_file}")

        # 标准化列名
        if '时间' in weather_df.columns:
            weather_df = weather_df.rename(columns={'时间': self.time_column})
        elif 'ʱ��' in weather_df.columns:  # 处理编码问题
            weather_df = weather_df.rename(columns={'ʱ��': self.time_column})

        # 3. 合并数据
        print("  合并发电功率和天气数据...")

        # 转换时间格式
        power_df[self.time_column] = pd.to_datetime(power_df[self.time_column])
        weather_df[self.time_column] = pd.to_datetime(weather_df[self.time_column])

        # 合并数据
        df = pd.merge(power_df, weather_df, on=self.time_column, how='inner')

        print(f"  多点位训练数据加载成功: {df.shape}")
        print(f"    发电功率数据: {len(power_df)} 条记录")
        print(f"    天气数据: {len(weather_df)} 条记录")
        print(f"    合并后数据: {len(df)} 条记录")

        # 验证数据结构
        self._validate_data_structure(df, "多点位训练集")

        # 基础预处理
        df = self._basic_preprocessing(df)

        print(f"时间范围: {df[self.time_column].min()} 到 {df[self.time_column].max()}")

        return df

    def load_test_data(self) -> pd.DataFrame:
        """
        加载测试数据集
        """
        # 检查是否启用多点位模式
        multi_location_config = self.config.get('multi_location', {})
        if multi_location_config.get('enable', False):
            raise ValueError("多点位模式下不支持通过load_test_data加载测试数据，请使用predict_today.py中的多点位处理流程")

        if self.test_file is None:
            raise ValueError("单点位模式下未配置测试文件，请检查配置或启用多点位模式")

        test_path = self.data_dir / self.test_file

        if not test_path.exists():
            raise FileNotFoundError(f"测试数据文件不存在: {test_path}")

        print(f"正在加载测试数据: {test_path}")

        # 使用配置的编码格式
        encodings = self.preferred_encodings
        df = None
        successful_encoding = None

        for encoding in encodings:
            try:
                df = pd.read_csv(test_path, encoding=encoding)
                successful_encoding = encoding
                print(f" 成功使用 {encoding} 编码加载数据")
                break
            except UnicodeDecodeError:
                print(f"  尝试 {encoding} 编码失败，继续尝试下一个...")
                continue
            except Exception as e:
                print(f"  使用 {encoding} 编码时出现其他错误: {e}")
                continue

        if df is None:
            raise ValueError(f"无法使用任何编码格式读取文件: {test_path}。请检查文件格式和编码。")
        
        # 验证数据结构 (测试集可能没有目标变量)
        expected_cols = self.feature_columns.copy()
        if self.target_column in df.columns:
            expected_cols.append(self.target_column)
        
        missing_cols = set(expected_cols) - set(df.columns)
        if missing_cols:
            print(f"警告: 测试集缺少列: {missing_cols}")
        
        # 基础预处理
        df = self._basic_preprocessing(df)
        
        print(f"测试数据加载完成: {len(df)} 条记录")
        print(f"时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
        
        return df
    
    def _validate_data_structure(self, df: pd.DataFrame, dataset_name: str):
        """
        验证数据结构的完整性
        """
        print(f"\n=== {dataset_name}数据结构验证 ===")
        
        # 检查必需的列
        expected_cols = self.feature_columns + [self.target_column]
        missing_cols = set(expected_cols) - set(df.columns)
        
        if missing_cols:
            raise ValueError(f"{dataset_name}缺少必需的列: {missing_cols}")
        
        print(f" 所有必需列都存在")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 检查数据类型
        print(f"\n数据类型:")
        for col in df.columns:
            print(f"  {col}: {df[col].dtype}")
        
        # 检查缺失值
        missing_info = df.isnull().sum()
        if missing_info.sum() > 0:
            print(f"\n缺失值统计:")
            for col, missing_count in missing_info.items():
                if missing_count > 0:
                    missing_pct = missing_count / len(df) * 100
                    print(f"  {col}: {missing_count} ({missing_pct:.2f}%)")
        else:
            print(f" 无缺失值")
    
    def _basic_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        基础预处理
        """
        df = df.copy()
        
        # 时间列处理
        if '时间' in df.columns:
            df['时间'] = pd.to_datetime(df['时间'])
            
        # 数值列的基础清理
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            # 处理无穷大值
            df[col] = df[col].replace([np.inf, -np.inf], np.nan)
            
            # 基础异常值检测 (使用IQR方法)
            if col != self.target_column:  # 不对目标变量进行异常值处理
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 3 * IQR  # 使用3倍IQR，比较宽松
                upper_bound = Q3 + 3 * IQR
                
                outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                if outliers > 0:
                    print(f"  {col}: 发现 {outliers} 个潜在异常值")
        
        return df
    
    def get_data_summary(self, df: pd.DataFrame, dataset_name: str) -> Dict:
        """
        获取数据集的详细摘要
        """
        summary = {
            'dataset_name': dataset_name,
            'shape': df.shape,
            'time_range': (df['时间'].min(), df['时间'].max()) if '时间' in df.columns else None,
            'missing_values': df.isnull().sum().to_dict(),
            'numeric_summary': df.describe().to_dict()
        }
        
        # 目标变量统计
        if self.target_column in df.columns:
            target_stats = df[self.target_column].describe()
            summary['target_statistics'] = {
                'mean': target_stats['mean'],
                'std': target_stats['std'],
                'min': target_stats['min'],
                'max': target_stats['max'],
                'zero_power_ratio': (df[self.target_column] == 0).mean()
            }
        
        # 时间间隔分析
        if '时间' in df.columns:
            time_diffs = df['时间'].diff().dropna()
            summary['time_intervals'] = {
                'expected_interval': '15min',
                'actual_intervals': time_diffs.value_counts().head(5).to_dict(),
                'irregular_intervals': (time_diffs != pd.Timedelta('15min')).sum()
            }
        
        return summary

class DataQualityChecker:
    """
    数据质量检查器
    专门检查风力发电数据的质量问题
    """
    
    def __init__(self):
        # 合理的数据范围
        self.reasonable_ranges = {
            'wind_speed_10m': (0, 50),      # 风速 m/s
            'wind_speed_80m': (0, 50),      # 风速 m/s
            'wind_direction_10m': (0, 360), # 风向 度
            'wind_direction_80m': (0, 360), # 风向 度
            'wind_gusts_10m': (0, 80),      # 阵风 m/s
            'temperature_2m': (-40, 50),    # 温度 °C
            'apparent_temperature': (-40, 50), # 体感温度 °C
            'rain': (0, 100),               # 降雨量 mm
            '理论功率 (MW)': (0, None)       # 功率 MW (无上限)
        }
    
    def check_data_quality(self, df: pd.DataFrame) -> Dict:
        """
        全面的数据质量检查
        """
        quality_report = {
            'total_records': len(df),
            'quality_issues': {},
            'recommendations': []
        }
        
        # 检查数据范围
        range_issues = self._check_data_ranges(df)
        if range_issues:
            quality_report['quality_issues']['range_violations'] = range_issues
        
        # 检查时间序列连续性
        if '时间' in df.columns:
            time_issues = self._check_time_continuity(df)
            if time_issues:
                quality_report['quality_issues']['time_continuity'] = time_issues
        
        # 检查物理一致性
        physics_issues = self._check_physics_consistency(df)
        if physics_issues:
            quality_report['quality_issues']['physics_consistency'] = physics_issues
        
        # 生成建议
        quality_report['recommendations'] = self._generate_recommendations(quality_report)
        
        return quality_report
    
    def _check_data_ranges(self, df: pd.DataFrame) -> Dict:
        """检查数据范围的合理性"""
        range_violations = {}
        
        for col, (min_val, max_val) in self.reasonable_ranges.items():
            if col in df.columns:
                violations = []
                
                if min_val is not None:
                    below_min = (df[col] < min_val).sum()
                    if below_min > 0:
                        violations.append(f"{below_min} 个值小于 {min_val}")
                
                if max_val is not None:
                    above_max = (df[col] > max_val).sum()
                    if above_max > 0:
                        violations.append(f"{above_max} 个值大于 {max_val}")
                
                if violations:
                    range_violations[col] = violations
        
        return range_violations
    
    def _check_time_continuity(self, df: pd.DataFrame) -> Dict:
        """检查时间序列的连续性"""
        time_issues = {}
        
        time_series = pd.to_datetime(df['时间'])
        time_diffs = time_series.diff().dropna()
        
        expected_interval = pd.Timedelta('15min')
        irregular_intervals = time_diffs != expected_interval
        
        if irregular_intervals.any():
            time_issues['irregular_intervals'] = irregular_intervals.sum()
            time_issues['gap_analysis'] = time_diffs[irregular_intervals].value_counts().head(10).to_dict()
        
        # 检查重复时间
        duplicates = time_series.duplicated().sum()
        if duplicates > 0:
            time_issues['duplicate_timestamps'] = duplicates
        
        return time_issues
    
    def _check_physics_consistency(self, df: pd.DataFrame) -> Dict:
        """检查物理一致性"""
        physics_issues = {}
        
        # 检查风速一致性 (80m风速通常大于10m风速)
        if 'wind_speed_10m' in df.columns and 'wind_speed_80m' in df.columns:
            inconsistent_wind = (df['wind_speed_80m'] < df['wind_speed_10m']).sum()
            if inconsistent_wind > 0:
                physics_issues['wind_speed_inconsistency'] = inconsistent_wind
        
        # 检查阵风与平均风速的关系 (阵风应该大于等于平均风速)
        if 'wind_gusts_10m' in df.columns and 'wind_speed_10m' in df.columns:
            inconsistent_gust = (df['wind_gusts_10m'] < df['wind_speed_10m']).sum()
            if inconsistent_gust > 0:
                physics_issues['gust_speed_inconsistency'] = inconsistent_gust
        
        # 检查温度一致性 (体感温度与实际温度的差异)
        if 'temperature_2m' in df.columns and 'apparent_temperature' in df.columns:
            temp_diff = np.abs(df['temperature_2m'] - df['apparent_temperature'])
            extreme_temp_diff = (temp_diff > 20).sum()  # 差异超过20度可能有问题
            if extreme_temp_diff > 0:
                physics_issues['extreme_temperature_difference'] = extreme_temp_diff
        
        return physics_issues
    
    def _generate_recommendations(self, quality_report: Dict) -> List[str]:
        """基于质量检查结果生成建议"""
        recommendations = []
        
        issues = quality_report.get('quality_issues', {})
        
        if 'range_violations' in issues:
            recommendations.append("建议检查和处理超出合理范围的数据值")
        
        if 'time_continuity' in issues:
            recommendations.append("建议处理时间序列的不连续性问题")
        
        if 'physics_consistency' in issues:
            recommendations.append("建议检查和修正物理不一致的数据")
        
        if not issues:
            recommendations.append("数据质量良好，可以进行后续处理")
        
        return recommendations
