# 系统模式 - 风力发电功率预测

## 设计模式

### 1. 数据管道模式 (Data Pipeline Pattern)
```python
class DataPipeline:
    def __init__(self):
        self.steps = []
    
    def add_step(self, step):
        self.steps.append(step)
    
    def execute(self, data):
        for step in self.steps:
            data = step.transform(data)
        return data
```

### 2. 特征工程器模式 (Feature Engineer Pattern)
```python
class FeatureEngineer:
    def __init__(self):
        self.transformers = []
    
    def add_transformer(self, transformer):
        self.transformers.append(transformer)
    
    def fit_transform(self, X, y=None):
        for transformer in self.transformers:
            X = transformer.fit_transform(X, y)
        return X
```

### 3. 模型包装器模式 (Model Wrapper Pattern)
```python
class ModelWrapper:
    def __init__(self, model, preprocessor=None):
        self.model = model
        self.preprocessor = preprocessor
    
    def fit(self, X, y):
        if self.preprocessor:
            X = self.preprocessor.fit_transform(X)
        self.model.fit(X, y)
    
    def predict(self, X):
        if self.preprocessor:
            X = self.preprocessor.transform(X)
        return self.model.predict(X)
```

## 代码组织原则

### 1. 单一职责原则
- 每个模块专注于一个特定功能
- 数据预处理、特征工程、模型训练分离

### 2. 开闭原则
- 对扩展开放，对修改关闭
- 新特征可以通过添加新的转换器实现

### 3. 依赖倒置原则
- 高层模块不依赖低层模块
- 通过接口定义依赖关系

## 错误处理模式

### 1. 数据验证
```python
def validate_data(df):
    assert not df.empty, "数据集为空"
    assert '时间' in df.columns, "缺少时间列"
    assert '理论功率 (MW)' in df.columns, "缺少目标变量"
    return True
```

### 2. 异常处理
```python
try:
    model.fit(X_train, y_train)
except Exception as e:
    logger.error(f"模型训练失败: {e}")
    raise
```

### 3. 数据质量检查
```python
def check_data_quality(df):
    missing_ratio = df.isnull().sum() / len(df)
    if missing_ratio.max() > 0.5:
        warnings.warn("某些列缺失值超过50%")
```

## 配置管理模式

### 1. 配置文件
```python
# config.py
class Config:
    DATA_PATH = "data/"
    TRAIN_FILE = "训练集20250228之前.csv"
    TEST_FILE = "测试集202503.csv"
    
    MODEL_PARAMS = {
        'objective': 'regression',
        'metric': 'rmse',
        'num_leaves': 31,
        'learning_rate': 0.05
    }
```

### 2. 环境配置
```python
import os
from pathlib import Path

BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
RESULTS_DIR = BASE_DIR / "results"
```

## 日志记录模式

### 1. 结构化日志
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
```

### 2. 进度跟踪
```python
from tqdm import tqdm

for i in tqdm(range(len(features)), desc="特征工程进度"):
    # 处理特征
    pass
```

## 测试模式

### 1. 单元测试
```python
import unittest

class TestFeatureEngineering(unittest.TestCase):
    def test_time_features(self):
        # 测试时间特征提取
        pass
    
    def test_wind_features(self):
        # 测试风力特征提取
        pass
```

### 2. 数据测试
```python
def test_data_integrity(df):
    # 检查数据完整性
    assert df['wind_speed_10m'].min() >= 0, "风速不能为负"
    assert df['temperature_2m'].between(-50, 50).all(), "温度超出合理范围"
```

## 性能优化模式

### 1. 内存优化
```python
def optimize_dtypes(df):
    for col in df.columns:
        if df[col].dtype == 'float64':
            df[col] = df[col].astype('float32')
        elif df[col].dtype == 'int64':
            df[col] = df[col].astype('int32')
    return df
```

### 2. 并行处理
```python
from joblib import Parallel, delayed

def parallel_feature_engineering(data_chunks):
    return Parallel(n_jobs=-1)(
        delayed(process_chunk)(chunk) for chunk in data_chunks
    )
```

## 版本控制模式

### 1. 模型版本管理
```python
import joblib
from datetime import datetime

def save_model(model, version=None):
    if version is None:
        version = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    filename = f"model_v{version}.pkl"
    joblib.dump(model, filename)
    return filename
```

### 2. 实验跟踪
```python
class ExperimentTracker:
    def __init__(self):
        self.experiments = []
    
    def log_experiment(self, params, metrics):
        experiment = {
            'timestamp': datetime.now(),
            'params': params,
            'metrics': metrics
        }
        self.experiments.append(experiment)
```
