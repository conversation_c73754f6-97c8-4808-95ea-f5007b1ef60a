"""
集成学习架构革新 - 方案3实现
包含分段专家模型、物理-数据双轨道、动态权重集成、残差递进建模
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.linear_model import LinearRegression
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

from .lightgbm_optimizer import PhysicsConstrainedLightGBM

def apply_power_constraints(predictions: np.ndarray, min_power: float = 0.0, max_power: float = 200.0) -> np.ndarray:
    """
    应用风机容量物理约束

    Args:
        predictions: 原始预测值
        min_power: 最小功率 (MW)
        max_power: 最大功率 (MW)

    Returns:
        约束后的预测值
    """
    return np.clip(predictions, min_power, max_power)

class WindSpeedExpertEnsemble:
    """
    分段专家模型系统
    根据风速范围训练专门的专家模型
    """
    
    def __init__(self, wind_speed_column: str = 'wind_speed_80m'):
        """
        初始化分段专家模型系统
        
        Args:
            wind_speed_column: 用于分段的风速列名
        """
        self.wind_speed_column = wind_speed_column
        
        # 定义风速分段阈值
        self.low_wind_threshold = 5.0    # 低风速: 0-5 m/s
        self.high_wind_threshold = 15.0  # 高风速: 15+ m/s
        
        # 专家模型
        self.low_wind_expert = None      # 低风速专家 (0-5 m/s)
        self.medium_wind_expert = None   # 中风速专家 (5-15 m/s)  
        self.high_wind_expert = None     # 高风速专家 (15+ m/s)
        
        # 边界平滑模型 (处理分段边界的预测)
        self.boundary_smoother = None
        
        # 专家模型性能记录
        self.expert_performance = {}
        
    def _segment_data_by_wind_speed(self, X: pd.DataFrame, y: np.ndarray) -> Dict:
        """
        根据风速分段数据
        """
        if self.wind_speed_column not in X.columns:
            raise ValueError(f"风速列 '{self.wind_speed_column}' 不存在于数据中")
        
        wind_speed = X[self.wind_speed_column]
        
        # 创建分段掩码
        low_wind_mask = wind_speed <= self.low_wind_threshold
        medium_wind_mask = (wind_speed > self.low_wind_threshold) & (wind_speed <= self.high_wind_threshold)
        high_wind_mask = wind_speed > self.high_wind_threshold
        
        segments = {
            'low_wind': {
                'X': X[low_wind_mask],
                'y': y[low_wind_mask],
                'mask': low_wind_mask,
                'range': f'0-{self.low_wind_threshold}m/s'
            },
            'medium_wind': {
                'X': X[medium_wind_mask],
                'y': y[medium_wind_mask], 
                'mask': medium_wind_mask,
                'range': f'{self.low_wind_threshold}-{self.high_wind_threshold}m/s'
            },
            'high_wind': {
                'X': X[high_wind_mask],
                'y': y[high_wind_mask],
                'mask': high_wind_mask,
                'range': f'{self.high_wind_threshold}+m/s'
            }
        }
        
        # 打印分段统计
        print("风速分段统计:")
        for segment_name, segment_data in segments.items():
            print(f"  {segment_name} ({segment_data['range']}): {len(segment_data['X'])} 样本")
            if len(segment_data['y']) > 0:
                print(f"    功率范围: {segment_data['y'].min():.2f} - {segment_data['y'].max():.2f} MW")
                print(f"    平均功率: {segment_data['y'].mean():.2f} MW")
        
        return segments
    
    def fit(self, X: pd.DataFrame, y: np.ndarray, 
            X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None) -> 'WindSpeedExpertEnsemble':
        """
        训练分段专家模型
        """
        print("开始训练分段专家模型系统...")
        
        # 分段训练数据
        train_segments = self._segment_data_by_wind_speed(X, y)
        
        # 如果有验证集，也进行分段
        val_segments = None
        if X_val is not None and y_val is not None:
            val_segments = self._segment_data_by_wind_speed(X_val, y_val)
        
        # 训练各个专家模型
        experts = {
            'low_wind': 'low_wind_expert',
            'medium_wind': 'medium_wind_expert', 
            'high_wind': 'high_wind_expert'
        }
        
        for segment_name, expert_attr in experts.items():
            segment_data = train_segments[segment_name]
            
            if len(segment_data['X']) < 10:  # 样本太少，跳过
                print(f"  {segment_name} 样本太少 ({len(segment_data['X'])}), 跳过训练")
                setattr(self, expert_attr, None)
                continue
            
            print(f"\n训练 {segment_name} 专家模型...")
            
            # 创建专家模型
            expert_model = PhysicsConstrainedLightGBM(physics_weight=0.15)
            
            # 准备验证数据
            segment_X_val = None
            segment_y_val = None
            if val_segments is not None:
                val_segment_data = val_segments[segment_name]
                if len(val_segment_data['X']) > 0:
                    segment_X_val = val_segment_data['X']
                    segment_y_val = val_segment_data['y']
            
            # 训练专家模型
            try:
                expert_model.fit(
                    segment_data['X'], segment_data['y'],
                    segment_X_val, segment_y_val,
                    num_leaves=min(31, max(10, len(segment_data['X']) // 20)),
                    learning_rate=0.1,
                    feature_fraction=0.8,
                    bagging_fraction=0.8,
                    bagging_freq=5,
                    min_child_samples=max(5, len(segment_data['X']) // 100)
                )
                
                setattr(self, expert_attr, expert_model)
                
                # 评估专家模型性能
                if segment_X_val is not None and len(segment_X_val) > 0:
                    y_pred = expert_model.predict(segment_X_val)
                    rmse = np.sqrt(mean_squared_error(segment_y_val, y_pred))
                    mae = mean_absolute_error(segment_y_val, y_pred)
                    
                    self.expert_performance[segment_name] = {
                        'rmse': rmse,
                        'mae': mae,
                        'samples': len(segment_data['X'])
                    }
                    
                    print(f"  {segment_name} 专家模型性能: RMSE={rmse:.4f}, MAE={mae:.4f}")
                else:
                    print(f"  {segment_name} 专家模型训练完成 (无验证集)")
                    
            except Exception as e:
                print(f"  {segment_name} 专家模型训练失败: {e}")
                setattr(self, expert_attr, None)
        
        # 训练边界平滑模型
        self._train_boundary_smoother(X, y)
        
        print("分段专家模型系统训练完成!")
        return self
    
    def _train_boundary_smoother(self, X: pd.DataFrame, y: np.ndarray):
        """
        训练边界平滑模型，处理分段边界的预测平滑
        """
        print("训练边界平滑模型...")
        
        wind_speed = X[self.wind_speed_column]
        
        # 选择边界附近的数据进行训练
        boundary_tolerance = 2.0  # 边界容忍度 ±2 m/s
        
        boundary_mask = (
            (np.abs(wind_speed - self.low_wind_threshold) <= boundary_tolerance) |
            (np.abs(wind_speed - self.high_wind_threshold) <= boundary_tolerance)
        )
        
        if boundary_mask.sum() > 10:
            boundary_X = X[boundary_mask]
            boundary_y = y[boundary_mask]
            
            # 使用简单的线性回归作为边界平滑器
            self.boundary_smoother = LinearRegression()
            
            # 选择关键特征进行边界平滑
            smooth_features = [self.wind_speed_column]
            for col in ['temperature_2m', 'rain', 'wind_direction_consistency']:
                if col in boundary_X.columns:
                    smooth_features.append(col)
            
            try:
                self.boundary_smoother.fit(boundary_X[smooth_features], boundary_y)
                print(f"  边界平滑模型训练完成，使用 {len(smooth_features)} 个特征")
            except Exception as e:
                print(f"  边界平滑模型训练失败: {e}")
                self.boundary_smoother = None
        else:
            print("  边界数据不足，跳过边界平滑模型训练")
            self.boundary_smoother = None
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        使用分段专家模型进行预测
        """
        if self.wind_speed_column not in X.columns:
            raise ValueError(f"风速列 '{self.wind_speed_column}' 不存在于预测数据中")
        
        wind_speed = X[self.wind_speed_column]
        predictions = np.zeros(len(X))
        
        # 创建分段掩码
        low_wind_mask = wind_speed <= self.low_wind_threshold
        medium_wind_mask = (wind_speed > self.low_wind_threshold) & (wind_speed <= self.high_wind_threshold)
        high_wind_mask = wind_speed > self.high_wind_threshold
        
        # 使用对应的专家模型进行预测
        if self.low_wind_expert is not None and low_wind_mask.any():
            predictions[low_wind_mask] = self.low_wind_expert.predict(X[low_wind_mask])
        
        if self.medium_wind_expert is not None and medium_wind_mask.any():
            predictions[medium_wind_mask] = self.medium_wind_expert.predict(X[medium_wind_mask])
        
        if self.high_wind_expert is not None and high_wind_mask.any():
            predictions[high_wind_mask] = self.high_wind_expert.predict(X[high_wind_mask])
        
        # 对于没有对应专家模型的情况，使用最相近的专家模型
        self._handle_missing_experts(X, predictions, low_wind_mask, medium_wind_mask, high_wind_mask)
        
        # 应用边界平滑
        predictions = self._apply_boundary_smoothing(X, predictions)

        # 应用风机容量物理约束
        predictions = apply_power_constraints(predictions)

        return predictions
    
    def _handle_missing_experts(self, X: pd.DataFrame, predictions: np.ndarray,
                               low_wind_mask: np.ndarray, medium_wind_mask: np.ndarray, 
                               high_wind_mask: np.ndarray):
        """
        处理缺失专家模型的情况
        """
        # 如果低风速专家缺失，使用中风速专家
        if self.low_wind_expert is None and low_wind_mask.any():
            if self.medium_wind_expert is not None:
                predictions[low_wind_mask] = self.medium_wind_expert.predict(X[low_wind_mask]) * 0.5
            elif self.high_wind_expert is not None:
                predictions[low_wind_mask] = self.high_wind_expert.predict(X[low_wind_mask]) * 0.3
        
        # 如果中风速专家缺失，使用其他专家的平均
        if self.medium_wind_expert is None and medium_wind_mask.any():
            if self.low_wind_expert is not None and self.high_wind_expert is not None:
                low_pred = self.low_wind_expert.predict(X[medium_wind_mask])
                high_pred = self.high_wind_expert.predict(X[medium_wind_mask])
                predictions[medium_wind_mask] = (low_pred + high_pred) / 2
            elif self.low_wind_expert is not None:
                predictions[medium_wind_mask] = self.low_wind_expert.predict(X[medium_wind_mask])
            elif self.high_wind_expert is not None:
                predictions[medium_wind_mask] = self.high_wind_expert.predict(X[medium_wind_mask])
        
        # 如果高风速专家缺失，使用中风速专家
        if self.high_wind_expert is None and high_wind_mask.any():
            if self.medium_wind_expert is not None:
                predictions[high_wind_mask] = self.medium_wind_expert.predict(X[high_wind_mask])
            elif self.low_wind_expert is not None:
                predictions[high_wind_mask] = self.low_wind_expert.predict(X[high_wind_mask]) * 1.5
    
    def _apply_boundary_smoothing(self, X: pd.DataFrame, predictions: np.ndarray) -> np.ndarray:
        """
        应用边界平滑
        """
        if self.boundary_smoother is None:
            return predictions
        
        wind_speed = X[self.wind_speed_column]
        boundary_tolerance = 1.0
        
        # 找到边界附近的点
        boundary_mask = (
            (np.abs(wind_speed - self.low_wind_threshold) <= boundary_tolerance) |
            (np.abs(wind_speed - self.high_wind_threshold) <= boundary_tolerance)
        )
        
        if boundary_mask.any():
            try:
                # 获取边界平滑器的特征
                smooth_features = [self.wind_speed_column]
                for col in ['temperature_2m', 'rain', 'wind_direction_consistency']:
                    if col in X.columns:
                        smooth_features.append(col)
                
                boundary_X = X[boundary_mask]
                smooth_predictions = self.boundary_smoother.predict(boundary_X[smooth_features])
                
                # 使用加权平均进行平滑
                alpha = 0.3  # 平滑权重
                predictions[boundary_mask] = (
                    alpha * smooth_predictions + 
                    (1 - alpha) * predictions[boundary_mask]
                )
            except Exception as e:
                print(f"边界平滑应用失败: {e}")
        
        return predictions
    
    def get_expert_info(self) -> Dict:
        """
        获取专家模型信息
        """
        info = {
            'wind_speed_thresholds': {
                'low_wind_threshold': self.low_wind_threshold,
                'high_wind_threshold': self.high_wind_threshold
            },
            'expert_models': {
                'low_wind_expert': self.low_wind_expert is not None,
                'medium_wind_expert': self.medium_wind_expert is not None,
                'high_wind_expert': self.high_wind_expert is not None
            },
            'expert_performance': self.expert_performance,
            'boundary_smoother': self.boundary_smoother is not None
        }
        return info


class PhysicsDataDualTrack:
    """
    物理-数据双轨道架构
    分别建立物理模型轨道和纯数据轨道，最后进行智能融合
    """

    def __init__(self, wind_speed_column: str = 'wind_speed_80m'):
        """
        初始化物理-数据双轨道系统

        Args:
            wind_speed_column: 主要风速列名
        """
        self.wind_speed_column = wind_speed_column

        # 物理轨道模型 (基于物理特征)
        self.physics_track = None

        # 数据轨道模型 (基于所有特征)
        self.data_track = None

        # 融合模型 (决定两个轨道的权重)
        self.fusion_model = None

        # 物理特征列表
        self.physics_features = []

        # 数据特征列表 (排除物理特征)
        self.data_features = []

        # 轨道性能记录
        self.track_performance = {}

    def _identify_physics_features(self, X: pd.DataFrame) -> List[str]:
        """
        识别物理相关特征
        """
        physics_keywords = [
            'wind_speed', 'wind_direction', 'wind_gusts', 'gust_factor',
            'temperature', 'apparent_temperature', 'rain', 'pressure',
            'density', 'power_curve', 'theoretical_power', 'air_density',
            'turbulence', 'wind_resource', 'generation_suitability',
            'power_expectation', 'efficiency_factor'
        ]

        physics_features = []
        for col in X.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in physics_keywords):
                physics_features.append(col)

        return physics_features

    def _identify_data_features(self, X: pd.DataFrame, physics_features: List[str]) -> List[str]:
        """
        识别纯数据特征 (时间特征、统计特征等)
        """
        data_keywords = [
            'hour', 'day', 'month', 'season', 'weekday', 'weekend',
            'lag_', 'rolling_', 'diff_', 'shift_', 'mean_', 'std_',
            'max_', 'min_', 'trend_', 'cycle_', 'fourier_'
        ]

        data_features = []
        for col in X.columns:
            if col not in physics_features:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in data_keywords):
                    data_features.append(col)
                elif col_lower not in ['时间', '理论功率 (mw)']:  # 排除时间和目标列
                    data_features.append(col)

        return data_features

    def fit(self, X: pd.DataFrame, y: np.ndarray,
            X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None) -> 'PhysicsDataDualTrack':
        """
        训练物理-数据双轨道模型
        """
        print("开始训练物理-数据双轨道系统...")

        # 识别特征类型
        self.physics_features = self._identify_physics_features(X)
        self.data_features = self._identify_data_features(X, self.physics_features)

        print(f"物理特征数量: {len(self.physics_features)}")
        print(f"数据特征数量: {len(self.data_features)}")

        # 确保有足够的特征
        if len(self.physics_features) < 3:
            print("警告: 物理特征太少，添加基础风速特征")
            for col in [self.wind_speed_column, 'temperature_2m', 'rain']:
                if col in X.columns and col not in self.physics_features:
                    self.physics_features.append(col)

        if len(self.data_features) < 3:
            print("警告: 数据特征太少，使用所有非物理特征")
            self.data_features = [col for col in X.columns
                                if col not in self.physics_features and
                                col.lower() not in ['时间', '理论功率 (mw)']]

        # 训练物理轨道
        self._train_physics_track(X, y, X_val, y_val)

        # 训练数据轨道
        self._train_data_track(X, y, X_val, y_val)

        # 训练融合模型
        self._train_fusion_model(X, y, X_val, y_val)

        print("物理-数据双轨道系统训练完成!")
        return self

    def _train_physics_track(self, X: pd.DataFrame, y: np.ndarray,
                           X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练物理轨道模型
        """
        print("训练物理轨道模型...")

        X_physics = X[self.physics_features]
        X_val_physics = X_val[self.physics_features] if X_val is not None else None

        # 使用更强的物理约束
        self.physics_track = PhysicsConstrainedLightGBM(physics_weight=0.2)

        try:
            self.physics_track.fit(
                X_physics, y, X_val_physics, y_val,
                num_leaves=25,  # 较少的叶子节点，避免过拟合
                learning_rate=0.08,
                feature_fraction=0.9,  # 使用更多特征
                bagging_fraction=0.8,
                min_child_samples=20,
                reg_alpha=0.1,
                reg_lambda=0.1
            )

            if X_val is not None and y_val is not None:
                y_pred_physics = self.physics_track.predict(X_val_physics)
                rmse_physics = np.sqrt(mean_squared_error(y_val, y_pred_physics))
                self.track_performance['physics_track'] = {
                    'rmse': rmse_physics,
                    'features': len(self.physics_features)
                }
                print(f"  物理轨道性能: RMSE={rmse_physics:.4f}")

        except Exception as e:
            print(f"  物理轨道训练失败: {e}")
            self.physics_track = None

    def _train_data_track(self, X: pd.DataFrame, y: np.ndarray,
                         X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练数据轨道模型
        """
        print("训练数据轨道模型...")

        X_data = X[self.data_features]
        X_val_data = X_val[self.data_features] if X_val is not None else None

        # 使用较弱的物理约束，更注重数据拟合
        self.data_track = PhysicsConstrainedLightGBM(physics_weight=0.05)

        try:
            self.data_track.fit(
                X_data, y, X_val_data, y_val,
                num_leaves=40,  # 更多叶子节点，更强的拟合能力
                learning_rate=0.1,
                feature_fraction=0.8,
                bagging_fraction=0.8,
                min_child_samples=10,
                reg_alpha=0.05,
                reg_lambda=0.05
            )

            if X_val is not None and y_val is not None:
                y_pred_data = self.data_track.predict(X_val_data)
                rmse_data = np.sqrt(mean_squared_error(y_val, y_pred_data))
                self.track_performance['data_track'] = {
                    'rmse': rmse_data,
                    'features': len(self.data_features)
                }
                print(f"  数据轨道性能: RMSE={rmse_data:.4f}")

        except Exception as e:
            print(f"  数据轨道训练失败: {e}")
            self.data_track = None

    def _train_fusion_model(self, X: pd.DataFrame, y: np.ndarray,
                           X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练融合模型，决定两个轨道的权重
        """
        print("训练融合模型...")

        if self.physics_track is None or self.data_track is None:
            print("  缺少轨道模型，跳过融合模型训练")
            return

        if X_val is None or y_val is None:
            print("  缺少验证集，跳过融合模型训练")
            return

        # 获取两个轨道的预测结果
        X_physics_val = X_val[self.physics_features]
        X_data_val = X_val[self.data_features]

        physics_pred = self.physics_track.predict(X_physics_val)
        data_pred = self.data_track.predict(X_data_val)

        # 构建融合特征
        fusion_features = np.column_stack([
            physics_pred,
            data_pred,
            X_val[self.wind_speed_column].values,  # 风速作为权重决策特征
            X_val['temperature_2m'].values if 'temperature_2m' in X_val.columns else np.zeros(len(X_val)),
            np.abs(physics_pred - data_pred),  # 两个轨道的差异
            (physics_pred + data_pred) / 2     # 两个轨道的平均
        ])

        # 使用简单的线性回归作为融合模型
        self.fusion_model = LinearRegression()

        try:
            self.fusion_model.fit(fusion_features, y_val)

            # 评估融合模型
            fusion_pred = self.fusion_model.predict(fusion_features)
            rmse_fusion = np.sqrt(mean_squared_error(y_val, fusion_pred))

            self.track_performance['fusion_model'] = {
                'rmse': rmse_fusion,
                'physics_weight': self.fusion_model.coef_[0],
                'data_weight': self.fusion_model.coef_[1]
            }

            print(f"  融合模型性能: RMSE={rmse_fusion:.4f}")
            print(f"  物理轨道权重: {self.fusion_model.coef_[0]:.3f}")
            print(f"  数据轨道权重: {self.fusion_model.coef_[1]:.3f}")

        except Exception as e:
            print(f"  融合模型训练失败: {e}")
            self.fusion_model = None

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        使用物理-数据双轨道进行预测
        """
        if self.physics_track is None and self.data_track is None:
            raise ValueError("没有可用的轨道模型")

        # 如果只有一个轨道可用
        if self.physics_track is None:
            data_pred = self.data_track.predict(X[self.data_features])
            return apply_power_constraints(data_pred)

        if self.data_track is None:
            physics_pred = self.physics_track.predict(X[self.physics_features])
            return apply_power_constraints(physics_pred)

        # 获取两个轨道的预测
        physics_pred = self.physics_track.predict(X[self.physics_features])
        data_pred = self.data_track.predict(X[self.data_features])

        # 如果有融合模型，使用融合预测
        if self.fusion_model is not None:
            try:
                fusion_features = np.column_stack([
                    physics_pred,
                    data_pred,
                    X[self.wind_speed_column].values,
                    X['temperature_2m'].values if 'temperature_2m' in X.columns else np.zeros(len(X)),
                    np.abs(physics_pred - data_pred),
                    (physics_pred + data_pred) / 2
                ])

                fusion_pred = self.fusion_model.predict(fusion_features)
                return apply_power_constraints(fusion_pred)

            except Exception as e:
                print(f"融合预测失败，使用加权平均: {e}")

        # 如果融合模型不可用，使用简单的加权平均
        # 根据轨道性能动态调整权重
        physics_weight = 0.6  # 默认物理轨道权重
        data_weight = 0.4     # 默认数据轨道权重

        if 'physics_track' in self.track_performance and 'data_track' in self.track_performance:
            physics_rmse = self.track_performance['physics_track']['rmse']
            data_rmse = self.track_performance['data_track']['rmse']

            # 性能越好，权重越高
            total_inv_rmse = (1/physics_rmse) + (1/data_rmse)
            physics_weight = (1/physics_rmse) / total_inv_rmse
            data_weight = (1/data_rmse) / total_inv_rmse

        weighted_pred = physics_weight * physics_pred + data_weight * data_pred
        return apply_power_constraints(weighted_pred)

    def get_track_info(self) -> Dict:
        """
        获取双轨道信息
        """
        info = {
            'physics_features': self.physics_features,
            'data_features': self.data_features,
            'track_models': {
                'physics_track': self.physics_track is not None,
                'data_track': self.data_track is not None,
                'fusion_model': self.fusion_model is not None
            },
            'track_performance': self.track_performance
        }
        return info


class DynamicWeightEnsemble:
    """
    动态权重集成系统
    根据当前气象条件动态调整不同模型的权重
    """

    def __init__(self, wind_speed_column: str = 'wind_speed_80m'):
        """
        初始化动态权重集成系统

        Args:
            wind_speed_column: 用于权重决策的风速列名
        """
        self.wind_speed_column = wind_speed_column

        # 子模型列表
        self.models = []
        self.model_names = []

        # 权重决策器
        self.weight_predictor = None

        # 模型性能历史
        self.model_performance_history = {}

        # 气象条件分类器
        self.weather_classifier = None

    def add_model(self, model, name: str):
        """
        添加子模型到集成系统

        Args:
            model: 训练好的模型
            name: 模型名称
        """
        self.models.append(model)
        self.model_names.append(name)
        print(f"添加模型: {name}")

    def fit_weight_predictor(self, X: pd.DataFrame, y: np.ndarray,
                           X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None) -> 'DynamicWeightEnsemble':
        """
        训练权重预测器
        """
        if len(self.models) < 2:
            raise ValueError("至少需要2个模型才能进行集成")

        print("训练动态权重预测器...")

        # 如果没有验证集，使用训练集的一部分
        if X_val is None or y_val is None:
            split_idx = int(len(X) * 0.8)
            X_val = X.iloc[split_idx:]
            y_val = y[split_idx:]
            X = X.iloc[:split_idx]
            y = y[:split_idx]

        # 获取所有模型的预测结果
        model_predictions = []
        for i, model in enumerate(self.models):
            try:
                if hasattr(model, 'predict'):
                    pred = model.predict(X_val)
                else:
                    # 如果是集成模型，可能需要特殊处理
                    pred = np.zeros(len(X_val))

                model_predictions.append(pred)

                # 计算模型性能
                rmse = np.sqrt(mean_squared_error(y_val, pred))
                self.model_performance_history[self.model_names[i]] = {
                    'rmse': rmse,
                    'predictions': pred
                }

            except Exception as e:
                print(f"模型 {self.model_names[i]} 预测失败: {e}")
                model_predictions.append(np.zeros(len(X_val)))

        # 构建权重预测特征
        weight_features = self._extract_weight_features(X_val)

        # 计算最优权重 (基于每个样本的最佳模型)
        optimal_weights = self._calculate_optimal_weights(model_predictions, y_val)

        # 训练权重预测器
        self._train_weight_predictor(weight_features, optimal_weights)

        # 训练气象条件分类器
        self._train_weather_classifier(X_val)

        print("动态权重预测器训练完成!")
        return self

    def _extract_weight_features(self, X: pd.DataFrame) -> np.ndarray:
        """
        提取用于权重决策的特征
        """
        features = []

        # 风速特征
        if self.wind_speed_column in X.columns:
            wind_speed = X[self.wind_speed_column].values
            features.append(wind_speed)
            features.append(wind_speed ** 2)  # 风速平方
            features.append(np.log1p(wind_speed))  # 风速对数

        # 温度特征
        if 'temperature_2m' in X.columns:
            temp = X['temperature_2m'].values
            features.append(temp)
            features.append(temp ** 2)

        # 降雨特征
        if 'rain' in X.columns:
            rain = X['rain'].values
            features.append(rain)
            features.append(np.log1p(rain))

        # 时间特征
        if 'hour' in X.columns:
            hour = X['hour'].values
            features.append(np.sin(2 * np.pi * hour / 24))  # 小时的周期性
            features.append(np.cos(2 * np.pi * hour / 24))

        # 季节特征
        if 'month' in X.columns:
            month = X['month'].values
            features.append(np.sin(2 * np.pi * month / 12))  # 月份的周期性
            features.append(np.cos(2 * np.pi * month / 12))

        # 如果没有足够的特征，添加常数特征
        if len(features) == 0:
            features.append(np.ones(len(X)))

        return np.column_stack(features)

    def _calculate_optimal_weights(self, model_predictions: List[np.ndarray], y_true: np.ndarray) -> np.ndarray:
        """
        计算每个样本的最优权重
        """
        n_samples = len(y_true)
        n_models = len(model_predictions)
        optimal_weights = np.zeros((n_samples, n_models))

        for i in range(n_samples):
            # 计算每个模型在当前样本上的误差
            errors = []
            for j in range(n_models):
                error = abs(model_predictions[j][i] - y_true[i])
                errors.append(error)

            # 使用反误差作为权重 (误差越小，权重越大)
            errors = np.array(errors)
            if errors.sum() > 0:
                weights = 1 / (errors + 1e-8)  # 避免除零
                weights = weights / weights.sum()  # 归一化
            else:
                weights = np.ones(n_models) / n_models  # 平均权重

            optimal_weights[i] = weights

        return optimal_weights

    def _train_weight_predictor(self, weight_features: np.ndarray, optimal_weights: np.ndarray):
        """
        训练权重预测器
        """
        try:
            # 使用多输出回归预测每个模型的权重
            from sklearn.multioutput import MultiOutputRegressor
            from sklearn.ensemble import RandomForestRegressor

            self.weight_predictor = MultiOutputRegressor(
                RandomForestRegressor(
                    n_estimators=50,
                    max_depth=10,
                    min_samples_split=10,
                    random_state=42
                )
            )

            self.weight_predictor.fit(weight_features, optimal_weights)
            print("  权重预测器训练成功")

        except Exception as e:
            print(f"  权重预测器训练失败: {e}")
            self.weight_predictor = None

    def _train_weather_classifier(self, X: pd.DataFrame):
        """
        训练气象条件分类器
        """
        try:
            from sklearn.cluster import KMeans

            # 提取气象特征进行聚类
            weather_features = []

            for col in ['wind_speed_80m', 'temperature_2m', 'rain']:
                if col in X.columns:
                    weather_features.append(X[col].values)

            if len(weather_features) > 0:
                weather_data = np.column_stack(weather_features)

                # 使用K-means聚类识别不同的气象模式
                self.weather_classifier = KMeans(n_clusters=5, random_state=42)
                self.weather_classifier.fit(weather_data)
                print("  气象条件分类器训练成功")
            else:
                self.weather_classifier = None

        except Exception as e:
            print(f"  气象条件分类器训练失败: {e}")
            self.weather_classifier = None

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        使用动态权重进行集成预测
        """
        if len(self.models) == 0:
            raise ValueError("没有可用的模型")

        # 获取所有模型的预测
        model_predictions = []
        for i, model in enumerate(self.models):
            try:
                if hasattr(model, 'predict'):
                    pred = model.predict(X)
                else:
                    pred = np.zeros(len(X))
                model_predictions.append(pred)
            except Exception as e:
                print(f"模型 {self.model_names[i]} 预测失败: {e}")
                model_predictions.append(np.zeros(len(X)))

        model_predictions = np.column_stack(model_predictions)

        # 如果有权重预测器，使用动态权重
        if self.weight_predictor is not None:
            try:
                weight_features = self._extract_weight_features(X)
                predicted_weights = self.weight_predictor.predict(weight_features)

                # 确保权重为正且归一化
                predicted_weights = np.maximum(predicted_weights, 0)
                predicted_weights = predicted_weights / (predicted_weights.sum(axis=1, keepdims=True) + 1e-8)

                # 加权平均预测
                ensemble_pred = np.sum(model_predictions * predicted_weights, axis=1)
                return apply_power_constraints(ensemble_pred)

            except Exception as e:
                print(f"动态权重预测失败，使用平均权重: {e}")

        # 如果权重预测器不可用，使用基于性能的静态权重
        if self.model_performance_history:
            weights = []
            for name in self.model_names:
                if name in self.model_performance_history:
                    rmse = self.model_performance_history[name]['rmse']
                    weight = 1 / (rmse + 1e-8)  # 性能越好权重越大
                else:
                    weight = 1.0
                weights.append(weight)

            weights = np.array(weights)
            weights = weights / weights.sum()  # 归一化

            ensemble_pred = np.sum(model_predictions * weights, axis=1)
            return apply_power_constraints(ensemble_pred)

        # 最后的备选方案：简单平均
        ensemble_pred = np.mean(model_predictions, axis=1)
        return apply_power_constraints(ensemble_pred)

    def get_ensemble_info(self) -> Dict:
        """
        获取集成系统信息
        """
        info = {
            'model_count': len(self.models),
            'model_names': self.model_names,
            'weight_predictor': self.weight_predictor is not None,
            'weather_classifier': self.weather_classifier is not None,
            'model_performance': self.model_performance_history
        }
        return info


class ResidualProgressiveModel:
    """
    残差递进建模系统
    第一层预测主趋势，第二层预测残差，提升整体精度
    """

    def __init__(self, wind_speed_column: str = 'wind_speed_80m'):
        """
        初始化残差递进建模系统

        Args:
            wind_speed_column: 主要风速列名
        """
        self.wind_speed_column = wind_speed_column

        # 第一层模型 (主趋势预测)
        self.primary_model = None

        # 第二层模型 (残差预测)
        self.residual_model = None

        # 第三层模型 (残差的残差预测，可选)
        self.secondary_residual_model = None

        # 模型性能记录
        self.layer_performance = {}

        # 是否使用第三层
        self.use_third_layer = True

        # 存储训练时的特征数量，用于预测时验证
        self.primary_feature_count = None
        self.residual_feature_count = None
        self.secondary_feature_count = None

    def fit(self, X: pd.DataFrame, y: np.ndarray,
            X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None) -> 'ResidualProgressiveModel':
        """
        训练残差递进模型
        """
        print("开始训练残差递进建模系统...")

        # 记录原始特征数量
        self.primary_feature_count = len(X.columns)

        # 训练第一层模型 (主趋势)
        self._train_primary_model(X, y, X_val, y_val)

        # 训练第二层模型 (残差)
        self._train_residual_model(X, y, X_val, y_val)

        # 训练第三层模型 (残差的残差)
        if self.use_third_layer:
            self._train_secondary_residual_model(X, y, X_val, y_val)

        print("残差递进建模系统训练完成!")
        return self

    def _train_primary_model(self, X: pd.DataFrame, y: np.ndarray,
                           X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练第一层模型 (主趋势预测)
        """
        print("训练第一层模型 (主趋势预测)...")

        # 使用较强的正则化，关注主要趋势
        self.primary_model = PhysicsConstrainedLightGBM(physics_weight=0.15)

        try:
            self.primary_model.fit(
                X, y, X_val, y_val,
                num_leaves=20,  # 较少叶子节点，避免过拟合
                learning_rate=0.08,
                feature_fraction=0.8,
                bagging_fraction=0.8,
                min_child_samples=30,  # 较大的最小样本数
                reg_alpha=0.2,  # 较强的L1正则化
                reg_lambda=0.2,  # 较强的L2正则化
                max_depth=8     # 限制深度
            )

            if X_val is not None and y_val is not None:
                y_pred_primary = self.primary_model.predict(X_val)
                rmse_primary = np.sqrt(mean_squared_error(y_val, y_pred_primary))

                self.layer_performance['primary_model'] = {
                    'rmse': rmse_primary,
                    'mae': mean_absolute_error(y_val, y_pred_primary),
                    'r2': r2_score(y_val, y_pred_primary)
                }

                print(f"  第一层模型性能: RMSE={rmse_primary:.4f}")

        except Exception as e:
            print(f"  第一层模型训练失败: {e}")
            self.primary_model = None

    def _train_residual_model(self, X: pd.DataFrame, y: np.ndarray,
                            X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练第二层模型 (残差预测)
        """
        if self.primary_model is None:
            print("第一层模型不可用，跳过残差模型训练")
            return

        print("训练第二层模型 (残差预测)...")

        # 计算训练集残差
        y_pred_primary_train = self.primary_model.predict(X)
        residuals_train = y - y_pred_primary_train

        # 添加残差相关特征
        X_residual = self._add_residual_features(X, y_pred_primary_train, residuals_train)

        # 记录残差模型的特征数量
        self.residual_feature_count = len(X_residual.columns)

        # 计算验证集残差
        X_val_residual = None
        residuals_val = None
        if X_val is not None and y_val is not None:
            y_pred_primary_val = self.primary_model.predict(X_val)
            residuals_val = y_val - y_pred_primary_val
            X_val_residual = self._add_residual_features(X_val, y_pred_primary_val, residuals_val)

        # 训练残差模型
        self.residual_model = PhysicsConstrainedLightGBM(physics_weight=0.05)  # 较弱的物理约束

        try:
            self.residual_model.fit(
                X_residual, residuals_train,
                X_val_residual, residuals_val,
                num_leaves=30,  # 更多叶子节点，捕获细节
                learning_rate=0.1,
                feature_fraction=0.9,
                bagging_fraction=0.8,
                min_child_samples=10,  # 较小的最小样本数
                reg_alpha=0.1,
                reg_lambda=0.1,
                max_depth=10
            )

            if X_val is not None and residuals_val is not None:
                residual_pred = self.residual_model.predict(X_val_residual)
                rmse_residual = np.sqrt(mean_squared_error(residuals_val, residual_pred))

                # 计算组合模型性能
                y_pred_combined = y_pred_primary_val + residual_pred
                rmse_combined = np.sqrt(mean_squared_error(y_val, y_pred_combined))

                self.layer_performance['residual_model'] = {
                    'residual_rmse': rmse_residual,
                    'combined_rmse': rmse_combined,
                    'improvement': self.layer_performance['primary_model']['rmse'] - rmse_combined
                }

                print(f"  第二层模型性能: 残差RMSE={rmse_residual:.4f}")
                print(f"  组合模型性能: RMSE={rmse_combined:.4f}")
                print(f"  性能提升: {self.layer_performance['residual_model']['improvement']:.4f}")

        except Exception as e:
            print(f"  第二层模型训练失败: {e}")
            self.residual_model = None

    def _add_residual_features(self, X: pd.DataFrame, primary_pred: np.ndarray,
                              residuals: Optional[np.ndarray] = None) -> pd.DataFrame:
        """
        添加残差相关特征
        """
        X_residual = X.copy()

        # 添加主模型预测作为特征
        X_residual['primary_prediction'] = primary_pred

        # 添加预测置信度特征
        X_residual['prediction_magnitude'] = np.abs(primary_pred)
        X_residual['prediction_squared'] = primary_pred ** 2

        # 添加残差统计特征 (无论是否有真实残差)
        if residuals is not None:
            # 训练时：使用真实残差
            residual_series = pd.Series(residuals)
            X_residual['residual_rolling_mean'] = residual_series.rolling(window=5, min_periods=1).mean()
            X_residual['residual_rolling_std'] = residual_series.rolling(window=5, min_periods=1).std().fillna(0)
            X_residual['residual_trend'] = residual_series.diff().fillna(0)
        else:
            # 预测时：使用默认值保持特征数量一致
            X_residual['residual_rolling_mean'] = 0.0
            X_residual['residual_rolling_std'] = 0.0
            X_residual['residual_trend'] = 0.0

        # 添加预测误差可能相关的特征
        if self.wind_speed_column in X.columns:
            wind_speed = X[self.wind_speed_column]
            # 风速与预测的交互特征
            X_residual['wind_pred_interaction'] = wind_speed * primary_pred
            X_residual['wind_pred_ratio'] = primary_pred / (wind_speed + 1e-8)

        return X_residual

    def _train_secondary_residual_model(self, X: pd.DataFrame, y: np.ndarray,
                                      X_val: Optional[pd.DataFrame], y_val: Optional[np.ndarray]):
        """
        训练第三层模型 (残差的残差预测)
        """
        if self.primary_model is None or self.residual_model is None:
            print("前两层模型不可用，跳过第三层模型训练")
            return

        print("训练第三层模型 (残差的残差预测)...")

        # 计算前两层的预测和残差
        y_pred_primary_train = self.primary_model.predict(X)
        X_residual_train = self._add_residual_features(X, y_pred_primary_train, y - y_pred_primary_train)
        residual_pred_train = self.residual_model.predict(X_residual_train)

        # 计算第二层残差
        y_pred_combined_train = y_pred_primary_train + residual_pred_train
        secondary_residuals_train = y - y_pred_combined_train

        # 如果第二层残差很小，跳过第三层
        if np.std(secondary_residuals_train) < 0.1:
            print("  第二层残差已经很小，跳过第三层模型")
            return

        # 准备验证集数据
        X_val_secondary = None
        secondary_residuals_val = None
        if X_val is not None and y_val is not None:
            y_pred_primary_val = self.primary_model.predict(X_val)
            X_residual_val = self._add_residual_features(X_val, y_pred_primary_val, y_val - y_pred_primary_val)
            residual_pred_val = self.residual_model.predict(X_residual_val)
            y_pred_combined_val = y_pred_primary_val + residual_pred_val
            secondary_residuals_val = y_val - y_pred_combined_val

            # 添加第二层残差特征
            X_val_secondary = self._add_residual_features(X_val, y_pred_combined_val, secondary_residuals_val)

        # 添加第二层残差特征
        X_secondary_train = self._add_residual_features(X, y_pred_combined_train, secondary_residuals_train)

        # 记录第三层模型的特征数量
        self.secondary_feature_count = len(X_secondary_train.columns)

        # 训练第三层模型
        self.secondary_residual_model = PhysicsConstrainedLightGBM(physics_weight=0.02)

        try:
            self.secondary_residual_model.fit(
                X_secondary_train, secondary_residuals_train,
                X_val_secondary, secondary_residuals_val,
                num_leaves=15,  # 更少的叶子节点
                learning_rate=0.05,  # 更小的学习率
                feature_fraction=0.7,
                bagging_fraction=0.8,
                min_child_samples=20,
                reg_alpha=0.3,  # 更强的正则化
                reg_lambda=0.3,
                max_depth=6
            )

            if X_val is not None and secondary_residuals_val is not None:
                secondary_residual_pred = self.secondary_residual_model.predict(X_val_secondary)
                rmse_secondary = np.sqrt(mean_squared_error(secondary_residuals_val, secondary_residual_pred))

                # 计算三层组合模型性能
                y_pred_final = y_pred_combined_val + secondary_residual_pred
                rmse_final = np.sqrt(mean_squared_error(y_val, y_pred_final))

                self.layer_performance['secondary_residual_model'] = {
                    'secondary_residual_rmse': rmse_secondary,
                    'final_rmse': rmse_final,
                    'improvement': self.layer_performance['residual_model']['combined_rmse'] - rmse_final
                }

                print(f"  第三层模型性能: 二次残差RMSE={rmse_secondary:.4f}")
                print(f"  最终模型性能: RMSE={rmse_final:.4f}")
                print(f"  额外提升: {self.layer_performance['secondary_residual_model']['improvement']:.4f}")

        except Exception as e:
            print(f"  第三层模型训练失败: {e}")
            self.secondary_residual_model = None

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        使用残差递进模型进行预测
        """
        if self.primary_model is None:
            raise ValueError("主模型不可用")

        # 第一层预测
        y_pred = self.primary_model.predict(X)

        # 第二层残差预测
        if self.residual_model is not None:
            # 预测时不提供真实残差，使用None
            X_residual = self._add_residual_features(X, y_pred, residuals=None)

            # 验证特征数量
            if self.residual_feature_count is not None and len(X_residual.columns) != self.residual_feature_count:
                print(f"警告: 残差模型特征数量不匹配 - 训练时: {self.residual_feature_count}, 预测时: {len(X_residual.columns)}")
                # 尝试调整特征数量
                if len(X_residual.columns) < self.residual_feature_count:
                    # 添加缺失特征
                    missing_count = self.residual_feature_count - len(X_residual.columns)
                    for i in range(missing_count):
                        X_residual[f'missing_feature_{i}'] = 0.0
                elif len(X_residual.columns) > self.residual_feature_count:
                    # 移除多余特征
                    X_residual = X_residual.iloc[:, :self.residual_feature_count]

            residual_pred = self.residual_model.predict(X_residual)
            y_pred = y_pred + residual_pred

        # 第三层残差预测
        if self.secondary_residual_model is not None:
            # 预测时不提供真实残差，使用None
            X_secondary = self._add_residual_features(X, y_pred, residuals=None)

            # 验证特征数量
            if self.secondary_feature_count is not None and len(X_secondary.columns) != self.secondary_feature_count:
                print(f"警告: 第三层模型特征数量不匹配 - 训练时: {self.secondary_feature_count}, 预测时: {len(X_secondary.columns)}")
                # 尝试调整特征数量
                if len(X_secondary.columns) < self.secondary_feature_count:
                    # 添加缺失特征
                    missing_count = self.secondary_feature_count - len(X_secondary.columns)
                    for i in range(missing_count):
                        X_secondary[f'missing_feature_{i}'] = 0.0
                elif len(X_secondary.columns) > self.secondary_feature_count:
                    # 移除多余特征
                    X_secondary = X_secondary.iloc[:, :self.secondary_feature_count]

            secondary_residual_pred = self.secondary_residual_model.predict(X_secondary)
            y_pred = y_pred + secondary_residual_pred

        # 应用风机容量物理约束
        return apply_power_constraints(y_pred)

    def get_progressive_info(self) -> Dict:
        """
        获取递进建模信息
        """
        info = {
            'layers': {
                'primary_model': self.primary_model is not None,
                'residual_model': self.residual_model is not None,
                'secondary_residual_model': self.secondary_residual_model is not None
            },
            'layer_performance': self.layer_performance,
            'use_third_layer': self.use_third_layer
        }
        return info
