"""
方案5：数据质量优化
实现异常值智能识别、缺失值物理插值、数据一致性校验、时间对齐精度提升
"""

import pandas as pd
import numpy as np
from scipy import stats, interpolate
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class DataQualityOptimizer:
    """
    数据质量优化器
    基于物理约束和统计方法的智能数据清洗
    """
    
    def __init__(self):
        # 物理约束参数
        self.wind_speed_limits = {'min': 0, 'max': 50}  # m/s
        self.temperature_limits = {'min': -50, 'max': 60}  # °C
        self.rain_limits = {'min': 0, 'max': 200}  # mm/h
        self.pressure_limits = {'min': 900, 'max': 1100}  # hPa
        
        # 异常检测器
        self.anomaly_detectors = {}
        
    def optimize_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        执行完整的数据质量优化
        """
        df = df.copy()
        
        print("开始数据质量优化...")
        
        # 1. 异常值智能识别和处理
        df = self._detect_and_handle_anomalies(df)
        
        # 2. 缺失值物理插值
        df = self._physics_based_imputation(df)
        
        # 3. 数据一致性校验
        df = self._consistency_validation(df)
        
        # 4. 时间对齐精度提升
        df = self._improve_time_alignment(df)
        
        # 5. 数据平滑和去噪
        df = self._smooth_and_denoise(df)
        
        # 6. 确保所有期望的特征都存在 (特征一致性保证)
        self._ensure_feature_consistency(df)

        print(f" 数据质量优化完成")

        return df

    def _ensure_feature_consistency(self, df: pd.DataFrame):
        """
        确保所有期望的数据质量特征都存在
        """
        # 主要变量
        variables = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m', 'rain']

        for var in variables:
            # 确保异常标记列存在
            anomaly_col = f'{var}_anomaly_flag'
            if anomaly_col not in df.columns:
                df[anomaly_col] = 0  # 默认无异常

            # 确保平滑数据列存在
            smoothed_col = f'{var}_smoothed'
            if smoothed_col not in df.columns and var in df.columns:
                df[smoothed_col] = df[var]  # 使用原始数据

            # 确保质量评分列存在
            quality_col = f'{var}_quality_score'
            if quality_col not in df.columns:
                df[quality_col] = 1.0  # 默认高质量

        # 确保整体质量评分存在
        if 'overall_data_quality' not in df.columns:
            df['overall_data_quality'] = 1.0
    
    def _detect_and_handle_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        异常值智能识别和处理
        结合物理约束和统计方法
        """
        print("  检测和处理异常值...")
        
        # 主要气象变量
        variables = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m', 'rain']
        available_vars = [var for var in variables if var in df.columns]
        
        for var in available_vars:
            data = df[var].copy()
            
            # 1. 物理约束检查
            if 'wind_speed' in var:
                limits = self.wind_speed_limits
            elif 'temperature' in var:
                limits = self.temperature_limits
            elif 'rain' in var:
                limits = self.rain_limits
            else:
                continue
            
            # 标记超出物理限制的值
            physical_outliers = (data < limits['min']) | (data > limits['max'])
            
            # 2. 统计异常检测
            # Z-score方法
            z_scores = np.abs(stats.zscore(data.dropna()))
            statistical_outliers_z = z_scores > 3
            
            # IQR方法
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            statistical_outliers_iqr = (data < (Q1 - 1.5 * IQR)) | (data > (Q3 + 1.5 * IQR))
            
            # 3. 机器学习异常检测
            if len(data.dropna()) > 50:
                try:
                    # 使用Isolation Forest
                    iso_forest = IsolationForest(contamination=0.1, random_state=42)
                    data_reshaped = data.dropna().values.reshape(-1, 1)
                    ml_outliers_mask = iso_forest.fit_predict(data_reshaped) == -1
                    
                    # 创建完整的异常掩码
                    ml_outliers = pd.Series(False, index=data.index)
                    ml_outliers.loc[data.dropna().index] = ml_outliers_mask
                except:
                    ml_outliers = pd.Series(False, index=data.index)
            else:
                ml_outliers = pd.Series(False, index=data.index)
            
            # 4. 时间序列异常检测 (突变点)
            data_diff = data.diff().abs()
            change_threshold = data_diff.quantile(0.95)
            sudden_changes = data_diff > change_threshold
            
            # 5. 综合异常判断
            # 物理异常必须处理，统计异常需要多种方法确认
            confirmed_outliers = (
                physical_outliers | 
                (statistical_outliers_z & statistical_outliers_iqr) |
                (ml_outliers & statistical_outliers_iqr) |
                sudden_changes
            )
            
            # 6. 异常值处理
            if confirmed_outliers.sum() > 0:
                print(f"    {var}: 发现 {confirmed_outliers.sum()} 个异常值")

                # 使用插值替换异常值
                data_cleaned = data.copy()
                data_cleaned[confirmed_outliers] = np.nan

                # 线性插值
                data_cleaned = data_cleaned.interpolate(method='linear', limit_direction='both')

                # 如果仍有缺失值，使用前后值填充
                data_cleaned = data_cleaned.fillna(method='ffill').fillna(method='bfill')

                df[var] = data_cleaned
            else:
                print(f"    {var}: 未发现异常值")

            # 总是添加异常标记列 (确保特征一致性)
            df[f'{var}_anomaly_flag'] = confirmed_outliers.astype(int)
        
        return df
    
    def _physics_based_imputation(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        基于物理原理的缺失值插值
        """
        print("  执行物理插值...")
        
        # 风速插值 - 基于风切变关系
        if 'wind_speed_80m' in df.columns and 'wind_speed_10m' in df.columns:
            wind_80m = df['wind_speed_80m']
            wind_10m = df['wind_speed_10m']
            
            # 计算平均风切变指数
            valid_mask = wind_80m.notna() & wind_10m.notna() & (wind_10m > 0)
            if valid_mask.sum() > 10:
                shear_exponents = np.log(wind_80m[valid_mask] / wind_10m[valid_mask]) / np.log(80 / 10)
                avg_shear = np.median(shear_exponents[np.isfinite(shear_exponents)])
                avg_shear = np.clip(avg_shear, 0.1, 0.5)  # 合理范围
            else:
                avg_shear = 0.2  # 默认值
            
            # 使用风切变关系填补缺失值
            # 80m缺失，10m存在
            missing_80m = wind_80m.isna() & wind_10m.notna()
            if missing_80m.sum() > 0:
                df.loc[missing_80m, 'wind_speed_80m'] = wind_10m[missing_80m] * (80 / 10) ** avg_shear
            
            # 10m缺失，80m存在
            missing_10m = wind_10m.isna() & wind_80m.notna()
            if missing_10m.sum() > 0:
                df.loc[missing_10m, 'wind_speed_10m'] = wind_80m[missing_10m] * (10 / 80) ** avg_shear
        
        # 温度插值 - 基于时间和季节模式
        if 'temperature_2m' in df.columns:
            temp = df['temperature_2m']
            missing_temp = temp.isna()
            
            if missing_temp.sum() > 0 and '时间' in df.columns:
                # 基于时间的温度模式插值
                time_series = pd.to_datetime(df['时间'])
                
                # 计算小时和月份的平均温度
                df_temp = pd.DataFrame({
                    'temperature': temp,
                    'hour': time_series.dt.hour,
                    'month': time_series.dt.month
                })
                
                # 计算每小时每月的平均温度
                hourly_monthly_avg = df_temp.groupby(['month', 'hour'])['temperature'].mean()
                
                # 填补缺失值
                for idx in missing_temp[missing_temp].index:
                    hour = time_series.loc[idx].hour
                    month = time_series.loc[idx].month
                    
                    if (month, hour) in hourly_monthly_avg.index:
                        df.loc[idx, 'temperature_2m'] = hourly_monthly_avg[(month, hour)]
        
        # 降雨插值 - 基于邻近值和持续性
        if 'rain' in df.columns:
            rain = df['rain']
            missing_rain = rain.isna()
            
            if missing_rain.sum() > 0:
                # 降雨具有间歇性，缺失值更可能是0
                # 但需要考虑降雨事件的持续性
                rain_filled = rain.copy()
                
                for idx in missing_rain[missing_rain].index:
                    # 查看前后值
                    prev_idx = max(0, idx - 1)
                    next_idx = min(len(df) - 1, idx + 1)
                    
                    prev_rain = rain.iloc[prev_idx] if prev_idx != idx else 0
                    next_rain = rain.iloc[next_idx] if next_idx != idx else 0
                    
                    # 如果前后都有降雨，插值；否则设为0
                    if pd.notna(prev_rain) and pd.notna(next_rain) and (prev_rain > 0 or next_rain > 0):
                        rain_filled.iloc[idx] = (prev_rain + next_rain) / 2
                    else:
                        rain_filled.iloc[idx] = 0
                
                df['rain'] = rain_filled
        
        # 其他变量的标准插值
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df[col].isna().sum() > 0:
                # 线性插值
                df[col] = df[col].interpolate(method='linear', limit_direction='both')
                # 如果仍有缺失，用前后值填充
                df[col] = df[col].fillna(method='ffill').fillna(method='bfill')
        
        return df
    
    def _consistency_validation(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据一致性校验
        检查多个气象变量之间的物理一致性
        """
        print("  执行一致性校验...")
        
        # 风速一致性检查
        if 'wind_speed_80m' in df.columns and 'wind_speed_10m' in df.columns:
            wind_80m = df['wind_speed_80m']
            wind_10m = df['wind_speed_10m']
            
            # 80m风速应该大于等于10m风速
            inconsistent_wind = wind_80m < wind_10m
            
            if inconsistent_wind.sum() > 0:
                print(f"    发现 {inconsistent_wind.sum()} 个风速不一致点")
                
                # 修正：使用风切变关系
                avg_ratio = (wind_80m / wind_10m).median()
                avg_ratio = np.clip(avg_ratio, 1.0, 2.0)  # 合理范围
                
                # 修正不一致的点
                df.loc[inconsistent_wind, 'wind_speed_80m'] = (
                    df.loc[inconsistent_wind, 'wind_speed_10m'] * avg_ratio
                )
        
        # 温度合理性检查
        if 'temperature_2m' in df.columns:
            temp = df['temperature_2m']
            
            # 检查温度变化率
            temp_change = temp.diff().abs()
            extreme_change = temp_change > 10  # 1小时内变化超过10度
            
            if extreme_change.sum() > 0:
                print(f"    发现 {extreme_change.sum()} 个极端温度变化点")
                
                # 平滑处理
                df.loc[extreme_change, 'temperature_2m'] = (
                    temp.rolling(3, center=True).mean().loc[extreme_change]
                )
        
        # 降雨逻辑检查
        if 'rain' in df.columns:
            rain = df['rain']
            
            # 负降雨量修正
            negative_rain = rain < 0
            if negative_rain.sum() > 0:
                print(f"    修正 {negative_rain.sum()} 个负降雨量")
                df.loc[negative_rain, 'rain'] = 0
        
        return df

    def _improve_time_alignment(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        时间对齐精度提升
        """
        print("  提升时间对齐精度...")

        if '时间' not in df.columns:
            return df

        # 转换时间列
        time_series = pd.to_datetime(df['时间'])

        # 检查时间间隔的一致性
        time_diffs = time_series.diff().dropna()

        if len(time_diffs) > 0:
            # 计算标准时间间隔
            mode_interval = time_diffs.mode()
            if len(mode_interval) > 0:
                expected_interval = mode_interval[0]

                # 检测时间跳跃
                large_gaps = time_diffs > expected_interval * 2

                if large_gaps.sum() > 0:
                    print(f"    发现 {large_gaps.sum()} 个时间跳跃")

                    # 标记时间跳跃
                    df['time_gap_flag'] = 0
                    gap_indices = large_gaps[large_gaps].index
                    df.loc[gap_indices, 'time_gap_flag'] = 1

        return df

    def _smooth_and_denoise(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据平滑和去噪
        """
        print("  执行数据平滑和去噪...")

        # 主要气象变量
        variables = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m', 'rain']
        available_vars = [var for var in variables if var in df.columns]

        for var in available_vars:
            data = df[var].copy()

            # 1. 移动平均平滑
            # 对于风速：轻度平滑，保持变化特性
            if 'wind_speed' in var:
                smoothed = data.rolling(window=3, center=True).mean()
                # 保留原始数据的80%，平滑数据的20%
                df[f'{var}_smoothed'] = 0.8 * data + 0.2 * smoothed.fillna(data)

            # 对于温度：中度平滑，温度变化相对缓慢
            elif 'temperature' in var:
                smoothed = data.rolling(window=5, center=True).mean()
                df[f'{var}_smoothed'] = 0.7 * data + 0.3 * smoothed.fillna(data)

            # 对于降雨：保持原始数据，降雨具有突发性
            elif 'rain' in var:
                # 只对明显的噪声进行处理
                # 检测单点异常（前后都是0但中间有值，或相反）
                prev_rain = data.shift(1).fillna(0)
                next_rain = data.shift(-1).fillna(0)
                current_rain = data.fillna(0)

                # 单点噪声：前后都是0但当前有值，且值较小
                single_point_noise = (
                    (prev_rain == 0) & (next_rain == 0) &
                    (current_rain > 0) & (current_rain < 1)
                )

                smoothed_rain = data.copy()
                smoothed_rain[single_point_noise] = 0
                df[f'{var}_smoothed'] = smoothed_rain

        # 计算数据质量评分
        self._calculate_quality_scores(df)

        return df

    def _calculate_quality_scores(self, df: pd.DataFrame):
        """
        计算数据质量评分
        """
        # 主要变量
        variables = ['wind_speed_80m', 'wind_speed_10m', 'temperature_2m', 'rain']
        available_vars = [var for var in variables if var in df.columns]

        quality_scores = {}

        for var in available_vars:
            data = df[var]

            # 1. 完整性评分 (缺失值比例)
            completeness = 1 - data.isna().sum() / len(data)

            # 2. 一致性评分 (异常值比例)
            anomaly_col = f'{var}_anomaly_flag'
            if anomaly_col in df.columns:
                consistency = 1 - df[anomaly_col].sum() / len(df)
            else:
                consistency = 1.0

            # 3. 平滑性评分 (变化率的稳定性)
            if len(data.dropna()) > 1:
                changes = data.diff().abs()
                change_cv = changes.std() / (changes.mean() + 1e-6)  # 变异系数
                smoothness = 1 / (1 + change_cv)
            else:
                smoothness = 1.0

            # 4. 物理合理性评分
            if 'wind_speed' in var:
                # 风速应该在合理范围内
                reasonable = ((data >= 0) & (data <= 40)).sum() / len(data.dropna()) if len(data.dropna()) > 0 else 1.0
            elif 'temperature' in var:
                # 温度应该在合理范围内
                reasonable = ((data >= -30) & (data <= 50)).sum() / len(data.dropna()) if len(data.dropna()) > 0 else 1.0
            elif 'rain' in var:
                # 降雨应该非负
                reasonable = (data >= 0).sum() / len(data.dropna()) if len(data.dropna()) > 0 else 1.0
            else:
                reasonable = 1.0

            # 综合质量评分
            overall_quality = (completeness * 0.3 + consistency * 0.3 +
                             smoothness * 0.2 + reasonable * 0.2)

            quality_scores[var] = {
                'completeness': completeness,
                'consistency': consistency,
                'smoothness': smoothness,
                'reasonableness': reasonable,
                'overall': overall_quality
            }

        # 添加质量评分到数据框
        for var, scores in quality_scores.items():
            df[f'{var}_quality_score'] = scores['overall']

        # 计算整体数据质量
        overall_scores = [scores['overall'] for scores in quality_scores.values()]
        df['overall_data_quality'] = np.mean(overall_scores) if overall_scores else 1.0

        print(f"    数据质量评分完成，整体质量: {df['overall_data_quality'].iloc[0]:.3f}")

        return quality_scores
