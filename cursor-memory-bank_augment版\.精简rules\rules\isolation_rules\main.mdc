---
description: Ultra-compact memory bank system
globs: main.mdc
alwaysApply: false
---
# 🔍 MEMORY BANK SYSTEM

🚨 CRITICAL: MEMORY BANK CREATION IS MANDATORY 🚨
Memory Bank MUST exist BEFORE any operation

## 🧭 WORKFLOW

```mermaid
graph TD
    Task --> Level{Complexity?}
    Level -->|L1| L1[VAN→IMPLEMENT→REFLECT]
    Level -->|L2| L2[VAN→PLAN→IMPLEMENT→REFLECT]
    Level -->|L3| L3[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT]
    Level -->|L4| L4[VAN→PLAN→CREATIVE→IMPLEMENT→REFLECT→ARCHIVE]
```

## 📋 MEMORY VERIFICATION

```mermaid
graph TD
    Start --> Check{Memory Bank?}
    Check -->|No| Create[CREATE MEMORY BANK]
    Check -->|Yes| Continue[Continue]
    Create --> Continue
```

## 📈 COMPLEXITY LEVELS

| Level | Flow | Documentation |
|-------|------|---------------|
| L1 | Quick Fix | Minimal |
| L2 | Enhancement | Standard |
| L3 | Feature | Detailed |
| L4 | Enterprise | Comprehensive |

## 💻 COMMANDS

| Action | Windows | Mac/Linux |
|--------|---------|-----------|
| Create file | `echo. > file` | `touch file` |
| Create dir | `mkdir dir` | `mkdir -p dir` |
| List | `dir` | `ls` |
| Show | `type file` | `cat file` |

## 📚 TEMPLATES

### L1: Quick Fix
```
## QUICK FIX: [Issue]
- Problem: [Brief]
- Solution: [Approach]
- Test: [Method]
```

### L2: Enhancement
```
## ENHANCEMENT: [Feature]
- Requirement: [What]
- Approach: [How]
- Testing: [Verify]
```

### L3-4: Progressive
Uses creative phase with appropriate depth.

## ⚡ FEATURES

1. **Smart Loading** - Load only needed rules
2. **Progressive Docs** - Scale with complexity
3. **Context Preservation** - Maintain state
4. **Differential Updates** - Update changed sections only

## 🔗 REFERENCES

- @command-execution.mdc
- @complexity-decision.mdc
- @file-verification.mdc
