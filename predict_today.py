"""
今日预测脚本 - 简化版本
自动查找最新的测试数据进行预测
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import joblib
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from src.time_alignment import MultiScaleTimeFeatures
from src.physics_features import WindPowerPhysicsFeatures
from src.comprehensive_feature_engine import ComprehensiveFeatureEngine
from src.multi_location_fusion import MultiLocationDataFusion
from src.spatial_feature_engine import SpatialFeatureEngine
from src.integrated_feature_engineer import IntegratedFeatureEngineer

# 导入配置管理器
import sys
sys.path.append(str(Path(__file__).parent / "src"))
from config_manager import ConfigManager

def get_test_file_from_config():
    """
    从配置文件获取测试文件
    """
    config = ConfigManager()

    # 从配置获取测试文件
    data_dir = Path(config.get('data_files.data_directory', 'data'))
    test_file = config.get('data_files.test_file')

    if not test_file:
        print(" 配置文件中未设置测试文件")
        print("请运行: python configure.py 来配置数据文件")
        return None

    test_path = data_dir / test_file

    if test_path.exists():
        print(f" 使用配置的测试文件: {test_file}")
        return test_path
    else:
        print(f" 配置的测试文件不存在: {test_path}")

        # 尝试查找其他可用文件
        print("查找其他可用文件...")
        test_patterns = ["测试集*.csv", "test*.csv", "*test*.csv", "15min_预报*.csv"]
        test_files = []

        for pattern in test_patterns:
            test_files.extend(data_dir.glob(pattern))

        if test_files:
            print("找到以下可用文件:")
            for i, file in enumerate(test_files):
                print(f"  {i+1}. {file.name}")

            try:
                choice = int(input("请选择文件编号 (或按回车使用最新文件): ").strip())
                if 1 <= choice <= len(test_files):
                    selected_file = test_files[choice - 1]
                    print(f" 选择文件: {selected_file.name}")

                    # 询问是否更新配置
                    update = input("是否将此文件设为默认测试文件? (y/n): ").strip().lower()
                    if update in ['y', 'yes']:
                        config.set('data_files.test_file', selected_file.name)
                        print(f" 配置已更新")

                    return selected_file
            except (ValueError, KeyboardInterrupt):
                pass

            # 使用最新文件
            latest_file = max(test_files, key=lambda x: x.stat().st_mtime)
            print(f" 使用最新文件: {latest_file.name}")
            return latest_file

        raise FileNotFoundError("未找到任何测试数据文件")

def load_model_and_predict(test_file):
    """
    加载模型并进行预测
    """
    print(f"=" * 60)
    print(f"今日风力发电功率预测")
    print(f"=" * 60)
    print(f"测试文件: {test_file}")
    
    # 1. 检查模型是否存在
    model_dir = Path("production_model")
    config_file = model_dir / "latest_config.json"
    
    if not config_file.exists():
        print(" 未找到训练好的模型")
        print("请先运行: python train_and_export_model.py")
        return
    
    # 2. 加载配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"模型时间戳: {config['timestamp']}")
    
    # 3. 加载模型组件
    print("加载模型组件...")
    
    # 加载模型
    model_file = model_dir / config['model_file']
    model = joblib.load(model_file)
    
    # 加载编码器
    encoders_file = model_dir / config['encoders_file']
    label_encoders = joblib.load(encoders_file)
    
    # 加载特征列表
    features_file = model_dir / config['features_file']
    with open(features_file, 'r', encoding='utf-8') as f:
        feature_columns = json.load(f)
    
    print(f" 模型加载完成 ({len(feature_columns)} 个特征)")
    
    # 4. 加载测试数据
    print("加载测试数据...")

    # 检查是否为多点位模式
    current_config = ConfigManager()
    multi_location_config = current_config.get('multi_location', {})

    if multi_location_config.get('enable', False):
        # 多点位模式：使用点位C的测试数据作为基础
        print("  多点位模式：加载点位C的测试数据...")
        test_data_dir = Path(current_config.get('data_files.test_data_directory', 'data'))
        test_pattern = multi_location_config.get('test_file_pattern', '15min_历史气象202503')
        test_file_path = test_data_dir / f"点位c_{test_pattern}.csv"

        if not test_file_path.exists():
            print(f"  ❌ 测试数据文件不存在: {test_file_path}")
            return

        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        test_df = None

        for encoding in encodings:
            try:
                test_df = pd.read_csv(test_file_path, encoding=encoding)
                print(f"    使用 {encoding} 编码加载成功")
                break
            except UnicodeDecodeError:
                continue

        if test_df is None:
            print(f"  ❌ 无法读取文件: {test_file_path}")
            return

        # 标准化列名
        if '时间' in test_df.columns:
            test_df = test_df.rename(columns={'时间': current_config.get('features.time_column', '时间')})
        elif 'ʱ��' in test_df.columns:  # 处理编码问题
            test_df = test_df.rename(columns={'ʱ��': current_config.get('features.time_column', '时间')})

        print(f"  点位C测试数据形状: {test_df.shape}")
    else:
        # 单点位模式：使用传统数据加载
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        test_df = None

        for encoding in encodings:
            try:
                test_df = pd.read_csv(test_file, encoding=encoding)
                print(f"  使用 {encoding} 编码加载成功")
                break
            except UnicodeDecodeError:
                continue

        if test_df is None:
            print(f"  ❌ 无法读取文件: {test_file}")
            return

        print(f"测试数据形状: {test_df.shape}")
    
    # 5. 特征工程 - 使用与训练时相同的模式
    print("应用特征工程...")

    # 从模型配置中获取特征工程模式（而不是当前config.json）
    model_config_file = model_dir / "latest_config.json"
    if model_config_file.exists():
        with open(model_config_file, 'r', encoding='utf-8') as f:
            model_config = json.load(f)
        feature_mode = model_config.get('feature_engineering_mode', 3)
        print(f"使用训练时的特征工程模式: {feature_mode}")
    else:
        # 如果没有模型配置，使用当前配置
        feature_mode = config.get('feature_engineering.mode', 3)
        print(f"使用当前配置的特征工程模式: {feature_mode}")

    if feature_mode == 1:
        # 模式1: 传统特征工程 + 集成特征工程
        print("使用传统特征工程 + 风机参数集成特征工程...")
        time_extractor = MultiScaleTimeFeatures()
        test_df = time_extractor.extract_all_time_features(test_df)

        physics_creator = WindPowerPhysicsFeatures()
        test_df = physics_creator.create_all_physics_features(test_df)

        # 集成特征工程（新增风机参数特征）
        print("  应用集成特征工程（风机参数增强）...")
        integrated_engineer = IntegratedFeatureEngineer()
        test_df = integrated_engineer.engineer_features(test_df, is_training=False)

    elif feature_mode == 2:
        # 模式2: 综合特征工程 + 集成特征工程
        print("使用综合特征工程 (方案1、2、4、5) + 风机参数集成特征工程...")
        feature_engine = ComprehensiveFeatureEngine()
        test_df = feature_engine.process_comprehensive_features(
            test_df,
            enable_data_quality=True,
            enable_physics=True,
            enable_time=True,
            enable_breakthrough=True
        )

        # 集成特征工程（新增风机参数特征）
        print("  应用集成特征工程（风机参数增强）...")
        integrated_engineer = IntegratedFeatureEngineer()
        test_df = integrated_engineer.engineer_features(test_df, is_training=False)

    elif feature_mode == 3:
        # 模式3: 传统特征工程 + 综合特征工程 + 集成特征工程
        print("使用传统特征工程 + 综合特征工程 + 风机参数集成特征工程...")
        print("  1. 传统特征工程...")
        time_extractor = MultiScaleTimeFeatures()
        test_df = time_extractor.extract_all_time_features(test_df)

        physics_creator = WindPowerPhysicsFeatures()
        test_df = physics_creator.create_all_physics_features(test_df)

        print("  2. 综合特征工程...")
        feature_engine = ComprehensiveFeatureEngine()
        test_df = feature_engine.process_comprehensive_features(
            test_df,
            enable_data_quality=True,
            enable_physics=True,
            enable_time=True,
            enable_breakthrough=True
        )

        # 集成特征工程（新增风机参数特征）
        print("  3. 集成特征工程（风机参数增强）...")
        integrated_engineer = IntegratedFeatureEngineer()
        test_df = integrated_engineer.engineer_features(test_df, is_training=False)

    elif feature_mode == 4:
        # 模式4: 多点位融合 + 传统特征工程 + 综合特征工程 + 集成特征工程
        print("使用多点位融合 + 传统特征工程 + 综合特征工程 + 集成特征工程...")

        # 1. 多点位数据融合
        print("  1. 多点位天气数据融合...")

        # 从当前配置获取多点位设置
        current_config = ConfigManager()
        multi_location_config = current_config.get('multi_location', {})

        if multi_location_config.get('enable', False):
            data_dir = current_config.get('data_files.test_data_directory', 'data')
            test_pattern = multi_location_config.get('test_file_pattern', '15min_历史气象202503')

            # 执行多点位数据融合
            multi_location_fusion = MultiLocationDataFusion()
            spatial_df = multi_location_fusion.fuse_multi_location_data(
                data_dir=data_dir,
                file_pattern=test_pattern,
                is_train=False
            )

            # 应用空间特征工程
            spatial_feature_engine = SpatialFeatureEngine()
            spatial_df = spatial_feature_engine.process_spatial_features(spatial_df)

            # 合并空间特征到原始数据
            time_col = current_config.get('features.time_column', '时间')
            test_df[time_col] = pd.to_datetime(test_df[time_col])
            spatial_df[time_col] = pd.to_datetime(spatial_df[time_col])

            # 合并数据
            test_df = pd.merge(test_df, spatial_df, on=time_col, how='inner', suffixes=('', '_spatial'))
            print(f"    多点位融合完成，数据形状: {test_df.shape}")
        else:
            print("    多点位融合已禁用，跳过...")

        # 2. 传统特征工程
        print("  2. 传统特征工程...")
        time_extractor = MultiScaleTimeFeatures()
        test_df = time_extractor.extract_all_time_features(test_df)

        physics_creator = WindPowerPhysicsFeatures()
        test_df = physics_creator.create_all_physics_features(test_df)

        # 3. 多源风速数据加载和融合
        multi_source_data = None
        multi_source_config = current_config.get('multi_source_wind', {})
        if multi_source_config.get('enable', False):
            print("  3. 多源风速数据融合...")
            try:
                # 构建多源风速数据文件路径
                test_data_dir = current_config.get('data_files.test_data_directory', 'data/testing')
                multi_source_file = multi_source_config.get('test_file', '站点c_多网站历史风速202503.csv')
                multi_source_path = Path(test_data_dir) / multi_source_file

                if multi_source_path.exists():
                    # 创建多源风速融合器并加载数据
                    from src.multi_source_wind_fusion import MultiSourceWindFusion
                    multi_source_fusion = MultiSourceWindFusion(current_config)
                    multi_source_data = multi_source_fusion.load_multi_source_data(str(multi_source_path))
                    print(f"    多源风速数据加载成功，形状: {multi_source_data.shape}")
                else:
                    print(f"    警告：多源风速数据文件不存在: {multi_source_path}")
            except Exception as e:
                print(f"    警告：多源风速数据加载失败: {e}")
        else:
            print("  3. 多源风速数据融合已禁用，跳过...")

        # 4. 综合特征工程
        print("  4. 综合特征工程...")
        feature_engine = ComprehensiveFeatureEngine(current_config)
        test_df = feature_engine.process_comprehensive_features(
            test_df,
            multi_source_data=multi_source_data,
            enable_data_quality=True,
            enable_physics=True,
            enable_time=True,
            enable_breakthrough=True,
            enable_multi_source=True
        )

        # 5. 集成特征工程（新增风机参数特征）
        print("  5. 集成特征工程（风机参数增强）...")
        integrated_engineer = IntegratedFeatureEngineer()
        test_df = integrated_engineer.engineer_features(test_df, is_training=False)

    else:
        print(f" 不支持的特征工程模式: {feature_mode}")
        return
    
    # 分类变量编码
    for col, le in label_encoders.items():
        if col in test_df.columns:
            values = test_df[col].astype(str).fillna('unknown')
            try:
                test_df[col] = le.transform(values)
            except ValueError:
                print(f"警告: {col} 包含新类别，使用默认值")
                known_values = [v if v in le.classes_ else le.classes_[0] for v in values]
                test_df[col] = le.transform(known_values)
    
    print(f" 综合特征工程完成，当前特征数: {len(test_df.columns)}")
    
    # 6. 准备预测数据
    print("准备预测数据...")
    
    available_features = [col for col in feature_columns if col in test_df.columns]
    missing_features = [col for col in feature_columns if col not in test_df.columns]

    if missing_features:
        print(f"警告: 缺少 {len(missing_features)} 个特征，自动补充...")

        # 为缺失特征添加默认值
        for feature in missing_features:
            if 'anomaly_flag' in feature:
                test_df[feature] = 0  # 异常标记默认为0（无异常）
            elif 'quality_score' in feature:
                test_df[feature] = 1.0  # 质量评分默认为1.0（高质量）
            elif 'smoothed' in feature:
                # 平滑特征：尝试使用对应的原始特征
                original_feature = feature.replace('_smoothed', '')
                if original_feature in test_df.columns:
                    test_df[feature] = test_df[original_feature]
                else:
                    test_df[feature] = 0.0
            else:
                test_df[feature] = 0.0  # 其他特征默认为0

        print(f" 已补充 {len(missing_features)} 个缺失特征")

    # 重新选择特征（现在应该包含所有期望的特征）
    X_pred = test_df[feature_columns].copy()
    
    # 处理缺失值
    for col in X_pred.columns:
        if X_pred[col].isnull().any():
            if X_pred[col].dtype.name == 'category':
                # 分类数据用众数填充
                mode_val = X_pred[col].mode().iloc[0] if not X_pred[col].mode().empty else 0
                X_pred[col] = X_pred[col].fillna(mode_val)
            elif X_pred[col].dtype in ['float64', 'int64', 'float32', 'int32']:
                X_pred[col] = X_pred[col].fillna(X_pred[col].median())
            else:
                X_pred[col] = X_pred[col].fillna(0)

    # 确保所有列都是数值类型
    for col in X_pred.columns:
        if X_pred[col].dtype == 'object' or X_pred[col].dtype.name == 'category':
            try:
                X_pred[col] = pd.to_numeric(X_pred[col], errors='coerce').fillna(0)
            except:
                X_pred[col] = 0
    
    print(f" 预测数据准备完成: {X_pred.shape}")
    
    # 7. 进行预测
    print("开始预测...")

    predictions = model.predict(X_pred)

    # 验证物理约束 (风机容量0-200MW)
    if predictions.min() < 0 or predictions.max() > 200:
        print(f"  检测到超出物理约束的预测值，已自动修正")
        print(f"  修正前范围: {predictions.min():.4f} - {predictions.max():.4f} MW")
        predictions = np.clip(predictions, 0.0, 200.0)
        print(f"  修正后范围: {predictions.min():.4f} - {predictions.max():.4f} MW")

    print(f" 预测完成")
    print(f"预测结果统计:")
    print(f"  样本数: {len(predictions)}")
    print(f"  平均功率: {np.mean(predictions):.4f} MW")
    print(f"  最大功率: {np.max(predictions):.4f} MW")
    print(f"  最小功率: {np.min(predictions):.4f} MW")

    # 物理约束验证报告
    zero_power = (predictions < 0.1).sum()
    max_power = (predictions > 199.9).sum()
    if zero_power > 0:
        print(f"  零功率预测: {zero_power} 个 ({zero_power/len(predictions)*100:.1f}%)")
    if max_power > 0:
        print(f"  满功率预测: {max_power} 个 ({max_power/len(predictions)*100:.1f}%)")
    
    # 8. 保存结果
    print("保存预测结果...")
    
    # 创建结果DataFrame
    results_df = pd.DataFrame()
    if '时间' in test_df.columns:
        results_df['时间'] = test_df['时间']
    else:
        results_df['序号'] = range(1, len(predictions) + 1)
    
    #results_df['时间']格式为2024/4/1 0:00:00
    results_df['时间'] = results_df['时间'].dt.strftime('%Y/%m/%d %H:%M:%S')
    
    results_df['pred'] = predictions
    # results_df['预测时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 保存到daily_predictions目录
    output_dir = Path("daily_predictions")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = output_dir / f"今日预测_{timestamp}.csv"
    
    results_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f" 预测结果已保存: {output_file}")
    
    # 9. 显示部分结果
    print(f"\n预测结果预览:")
    print(results_df.head(10).to_string(index=False))
    
    if len(results_df) > 10:
        print(f"... (共 {len(results_df)} 条记录)")
    
    print(f"\n" + "=" * 60)
    print("今日预测完成!")
    print(f"结果文件: {output_file}")
    print("=" * 60)

def main():
    """
    主函数
    """
    try:
        # 检查是否启用多点位模式
        config = ConfigManager()
        multi_location_config = config.get('multi_location', {})

        if multi_location_config.get('enable', False):
            # 多点位模式：使用虚拟测试文件路径
            print("检测到多点位模式，将使用多点位数据进行预测...")
            test_file = Path("data/testing/virtual_test_file.csv")  # 虚拟路径，实际不使用

            # 加载模型并预测
            load_model_and_predict(test_file)
        else:
            # 单点位模式：使用传统测试文件
            test_file = get_test_file_from_config()
            if test_file is None:
                return

            print(f"使用测试文件: {test_file}")

            # 加载模型并预测
            load_model_and_predict(test_file)

    except Exception as e:
        print(f" 预测失败: {e}")
        print("\n可能的解决方案:")
        print("1. 确保已训练模型: python train_and_export_model.py")
        print("2. 配置正确的测试文件: python configure.py")
        print("3. 检查测试数据文件是否在data目录中")
        print("4. 运行详细版本: python daily_prediction.py --test_file 文件名.csv")

if __name__ == "__main__":
    main()
