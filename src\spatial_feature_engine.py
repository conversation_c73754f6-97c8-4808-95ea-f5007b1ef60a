"""
空间特征工程器
实现分层空间特征工程，包括基础层、融合层、差分层、统计层、模式层
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler

warnings.filterwarnings('ignore')

class SpatialFeatureEngine:
    """
    空间特征工程器
    
    实现分层空间特征工程：
    1. 基础层：原始多点位特征
    2. 融合层：加权平均和空间梯度
    3. 差分层：点位间差值特征
    4. 统计层：空间统计量
    5. 模式层：气象模式识别特征
    """
    
    def __init__(self, time_col: str = '时间'):
        self.time_col = time_col
        
        # 气象特征列表
        self.weather_features = [
            'wind_speed_10m', 'wind_speed_80m', 'wind_direction_10m', 
            'wind_direction_80m', 'wind_gusts_10m', 'temperature_2m', 
            'rain', 'apparent_temperature'
        ]
        
        # 特征统计
        self.feature_stats = {}
        
    def create_wind_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建风场模式特征
        
        Args:
            df: 包含空间特征的数据框
            
        Returns:
            添加风场模式特征的数据框
        """
        print("创建风场模式特征...")
        
        result_df = df.copy()
        
        # 1. 风速一致性指标
        if 'wind_speed_10m_spatial_std' in df.columns and 'wind_speed_10m_spatial_mean' in df.columns:
            # 风速变异系数
            result_df['wind_speed_consistency'] = (
                df['wind_speed_10m_spatial_std'] / (df['wind_speed_10m_spatial_mean'] + 1e-8)
            )
            
            # 风速梯度强度
            if 'wind_speed_10m_gradient_magnitude' in df.columns:
                result_df['wind_gradient_intensity'] = df['wind_speed_10m_gradient_magnitude']
        
        # 2. 风向一致性指标
        if 'wind_direction_10m_spatial_std' in df.columns:
            # 风向变异性（考虑角度的周期性）
            result_df['wind_direction_variability'] = df['wind_direction_10m_spatial_std']
            
            # 风向一致性（低变异性表示高一致性）
            result_df['wind_direction_consistency'] = 1.0 / (df['wind_direction_10m_spatial_std'] + 1.0)
        
        # 3. 温度分层特征
        if 'temperature_2m_spatial_std' in df.columns and 'temperature_2m_spatial_mean' in df.columns:
            # 温度梯度强度
            result_df['temperature_gradient_strength'] = df['temperature_2m_spatial_std']
            
            # 温度均匀性
            result_df['temperature_uniformity'] = 1.0 / (df['temperature_2m_spatial_std'] + 0.1)
        
        # 4. 降水模式特征
        if 'rain_spatial_max' in df.columns and 'rain_spatial_mean' in df.columns:
            # 降水集中度
            result_df['rain_concentration'] = (
                df['rain_spatial_max'] / (df['rain_spatial_mean'] + 1e-8)
            )
            
            # 降水覆盖率（非零降水点位比例的近似）
            result_df['rain_coverage'] = np.where(
                df['rain_spatial_mean'] > 0.1,
                df['rain_spatial_mean'] / (df['rain_spatial_max'] + 1e-8),
                0.0
            )
        
        print(f"  创建了 8 个风场模式特征")
        return result_df
    
    def create_atmospheric_stability_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建大气稳定性特征
        
        Args:
            df: 包含空间特征的数据框
            
        Returns:
            添加大气稳定性特征的数据框
        """
        print("创建大气稳定性特征...")
        
        result_df = df.copy()
        
        # 1. 风切变指标
        if 'wind_speed_10m' in df.columns and 'wind_speed_80m' in df.columns:
            # 垂直风切变
            result_df['wind_shear_vertical'] = df['wind_speed_80m'] - df['wind_speed_10m']
            
            # 风切变比率
            result_df['wind_shear_ratio'] = (
                df['wind_speed_80m'] / (df['wind_speed_10m'] + 1e-8)
            )
        
        # 2. 温度稳定性
        if 'temperature_2m' in df.columns and 'apparent_temperature' in df.columns:
            # 温度-体感温度差异
            result_df['temperature_stability'] = df['temperature_2m'] - df['apparent_temperature']
        
        # 3. 湍流强度估计
        if 'wind_gusts_10m' in df.columns and 'wind_speed_10m' in df.columns:
            # 阵风因子
            result_df['gust_factor'] = (
                df['wind_gusts_10m'] / (df['wind_speed_10m'] + 1e-8)
            )
            
            # 湍流强度
            result_df['turbulence_intensity'] = (
                (df['wind_gusts_10m'] - df['wind_speed_10m']) / (df['wind_speed_10m'] + 1e-8)
            )
        
        print(f"  创建了 5 个大气稳定性特征")
        return result_df
    
    def create_weather_front_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建天气锋面特征
        
        Args:
            df: 包含空间特征的数据框
            
        Returns:
            添加天气锋面特征的数据框
        """
        print("创建天气锋面特征...")
        
        result_df = df.copy()
        
        # 1. 温度锋面强度
        if 'temperature_2m_gradient_magnitude' in df.columns:
            result_df['temperature_front_strength'] = df['temperature_2m_gradient_magnitude']
        
        # 2. 压力锋面（通过温度和风速变化近似）
        if ('temperature_2m_gradient_magnitude' in df.columns and 
            'wind_speed_10m_gradient_magnitude' in df.columns):
            # 综合锋面强度
            result_df['combined_front_strength'] = (
                df['temperature_2m_gradient_magnitude'] * 0.6 + 
                df['wind_speed_10m_gradient_magnitude'] * 0.4
            )
        
        # 3. 风向切变（锋面指示器）
        if 'wind_direction_10m_spatial_std' in df.columns:
            # 风向切变强度
            result_df['wind_direction_shear'] = df['wind_direction_10m_spatial_std']
            
            # 锋面概率（基于风向变化）
            result_df['front_probability'] = np.where(
                df['wind_direction_10m_spatial_std'] > 30,  # 风向变化超过30度
                1.0,
                df['wind_direction_10m_spatial_std'] / 30.0
            )
        
        print(f"  创建了 4 个天气锋面特征")
        return result_df
    
    def create_spatial_correlation_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建空间相关性特征
        
        Args:
            df: 包含空间特征的数据框
            
        Returns:
            添加空间相关性特征的数据框
        """
        print("创建空间相关性特征...")
        
        result_df = df.copy()
        
        # 1. 多变量空间一致性
        consistency_features = []
        for feature in ['wind_speed_10m', 'temperature_2m', 'wind_direction_10m']:
            cv_col = f'{feature}_spatial_cv'
            if cv_col in df.columns:
                consistency_features.append(cv_col)
        
        if consistency_features:
            # 综合空间一致性指标
            consistency_values = df[consistency_features].values
            result_df['overall_spatial_consistency'] = np.mean(consistency_values, axis=1)
            
            # 空间异质性指标
            result_df['spatial_heterogeneity'] = np.std(consistency_values, axis=1)
        
        # 2. 风速-温度相关性（局地环流指示器）
        if ('wind_speed_10m_spatial_std' in df.columns and 
            'temperature_2m_spatial_std' in df.columns):
            # 风温耦合强度
            result_df['wind_temperature_coupling'] = (
                df['wind_speed_10m_spatial_std'] * df['temperature_2m_spatial_std']
            )
        
        print(f"  创建了 3 个空间相关性特征")
        return result_df
    
    def process_spatial_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        执行完整的空间特征工程
        
        Args:
            df: 包含基础空间特征的数据框
            
        Returns:
            添加所有空间特征的数据框
        """
        print("=" * 60)
        print("开始空间特征工程处理")
        print("=" * 60)
        
        result_df = df.copy()
        original_features = len(df.columns) - 1  # 减去时间列
        
        # 1. 风场模式特征
        result_df = self.create_wind_pattern_features(result_df)
        
        # 2. 大气稳定性特征
        result_df = self.create_atmospheric_stability_features(result_df)
        
        # 3. 天气锋面特征
        result_df = self.create_weather_front_features(result_df)
        
        # 4. 空间相关性特征
        result_df = self.create_spatial_correlation_features(result_df)
        
        # 5. 特征统计
        final_features = len(result_df.columns) - 1  # 减去时间列
        new_features = final_features - original_features
        
        self.feature_stats = {
            'original_features': original_features,
            'new_features': new_features,
            'final_features': final_features,
            'feature_categories': {
                'wind_pattern': 8,
                'atmospheric_stability': 5,
                'weather_front': 4,
                'spatial_correlation': 3
            }
        }
        
        print(f"\n空间特征工程统计:")
        print(f"  原始特征数: {original_features}")
        print(f"  新增特征数: {new_features}")
        print(f"  最终特征数: {final_features}")
        print(f"  特征增长率: {(new_features/original_features)*100:.1f}%")
        
        print("\n" + "=" * 60)
        print("空间特征工程处理完成!")
        print("=" * 60)
        
        return result_df
