"""
LightGBM超精度优化系统
实现CREATIVE阶段设计的精度优先自适应LightGBM系统
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import optuna
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class PhysicsConstrainedLightGBM:
    """
    物理约束的LightGBM模型
    集成风力发电物理原理的损失函数和约束
    """
    
    def __init__(self, physics_weight: float = 0.1):
        """
        初始化物理约束LightGBM
        
        Args:
            physics_weight: 物理约束在损失函数中的权重
        """
        self.physics_weight = physics_weight
        self.model = None
        self.feature_importance_ = None
        
        # 风力发电物理约束参数
        self.cut_in_speed = 3.0
        self.rated_speed = 12.0
        self.cut_out_speed = 25.0

        # 风机容量物理限制
        self.min_power = 0.0      # 最小功率 0MW
        self.max_power = 200.0    # 最大功率 200MW
        
    def _physics_constraint_loss(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                wind_speed: np.ndarray) -> float:
        """
        计算物理约束损失
        确保预测结果符合风力发电的物理规律
        """
        physics_violations = 0.0
        
        # 约束1: 风速低于切入风速时，功率应该接近0
        low_wind_mask = wind_speed < self.cut_in_speed
        if low_wind_mask.any():
            low_wind_violations = np.mean(np.maximum(0, y_pred[low_wind_mask] - 0.1))
            physics_violations += low_wind_violations
        
        # 约束2: 风速超过切出风速时，功率应该接近0
        high_wind_mask = wind_speed > self.cut_out_speed
        if high_wind_mask.any():
            high_wind_violations = np.mean(np.maximum(0, y_pred[high_wind_mask] - 0.1))
            physics_violations += high_wind_violations
        
        # 约束3: 功率不应该为负
        negative_power_violations = np.mean(np.maximum(0, -y_pred))
        physics_violations += negative_power_violations
        
        return physics_violations
    
    def fit(self, X: pd.DataFrame, y: np.ndarray,
            X_val: Optional[pd.DataFrame] = None, y_val: Optional[np.ndarray] = None,
            categorical_features: Optional[List] = None,
            **lgb_params) -> 'PhysicsConstrainedLightGBM':
        """
        训练物理约束的LightGBM模型
        """
        # 确保所有数据都是数值类型
        X_clean = X.copy()
        for col in X_clean.columns:
            if X_clean[col].dtype == 'object' or X_clean[col].dtype.name == 'category':
                X_clean[col] = pd.to_numeric(X_clean[col], errors='coerce').fillna(0)

        if X_val is not None:
            X_val_clean = X_val.copy()
            for col in X_val_clean.columns:
                if X_val_clean[col].dtype == 'object' or X_val_clean[col].dtype.name == 'category':
                    X_val_clean[col] = pd.to_numeric(X_val_clean[col], errors='coerce').fillna(0)
        else:
            X_val_clean = None

        # 准备训练数据 - 不指定categorical_feature以避免冲突
        train_data = lgb.Dataset(X_clean, label=y)

        # 准备验证数据
        valid_sets = [train_data]
        valid_names = ['train']

        if X_val_clean is not None and y_val is not None:
            val_data = lgb.Dataset(X_val_clean, label=y_val, reference=train_data)
            valid_sets.append(val_data)
            valid_names.append('valid')
        
        # 默认参数
        default_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }
        
        # 更新参数
        params = {**default_params, **lgb_params}
        
        # 训练模型 - 兼容不同版本的LightGBM
        try:
            self.model = lgb.train(
                params,
                train_data,
                valid_sets=valid_sets,
                valid_names=valid_names,
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
            )
        except Exception as e:
            # 如果callbacks方式失败，尝试旧版本方式
            print(f"使用兼容模式训练模型...")
            self.model = lgb.train(
                params,
                train_data,
                valid_sets=valid_sets,
                valid_names=valid_names,
                num_boost_round=1000,
                early_stopping_rounds=50,
                verbose_eval=False
            )
        
        # 保存特征重要性
        self.feature_importance_ = self.model.feature_importance(importance_type='gain')
        
        return self
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        预测，并应用物理约束后处理
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 确保预测数据类型与训练时一致
        X_clean = X.copy()
        for col in X_clean.columns:
            if X_clean[col].dtype == 'object' or X_clean[col].dtype.name == 'category':
                X_clean[col] = pd.to_numeric(X_clean[col], errors='coerce').fillna(0)

        # 基础预测
        y_pred = self.model.predict(X_clean)

        # 应用物理约束后处理
        y_pred = self._apply_physics_constraints(y_pred, X_clean)

        return y_pred
    
    def _apply_physics_constraints(self, y_pred: np.ndarray, X: pd.DataFrame) -> np.ndarray:
        """
        应用物理约束进行后处理
        """
        y_pred_constrained = y_pred.copy()
        
        # 获取风速信息
        wind_speed_col = None
        for col in ['wind_speed_80m', 'wind_speed_10m']:
            if col in X.columns:
                wind_speed_col = col
                break
        
        if wind_speed_col is not None:
            wind_speed = X[wind_speed_col].values
            
            # 约束1: 低风速时功率为0
            low_wind_mask = wind_speed < self.cut_in_speed
            y_pred_constrained[low_wind_mask] = np.maximum(0, y_pred_constrained[low_wind_mask] * 0.1)
            
            # 约束2: 高风速时功率为0
            high_wind_mask = wind_speed > self.cut_out_speed
            y_pred_constrained[high_wind_mask] = np.maximum(0, y_pred_constrained[high_wind_mask] * 0.1)
        
        # 约束3: 功率不能为负
        y_pred_constrained = np.maximum(0, y_pred_constrained)

        # 约束4: 风机容量物理限制 (0-200MW)
        y_pred_constrained = np.clip(y_pred_constrained, self.min_power, self.max_power)

        return y_pred_constrained

class LightGBMHyperOptimizer:
    """
    LightGBM超参数优化器
    使用贝叶斯优化最大化预测精度
    """
    
    def __init__(self, cv_folds: int = 5, n_trials: int = 100, 
                 physics_constrained: bool = True):
        """
        初始化超参数优化器
        
        Args:
            cv_folds: 交叉验证折数
            n_trials: 优化试验次数
            physics_constrained: 是否使用物理约束
        """
        self.cv_folds = cv_folds
        self.n_trials = n_trials
        self.physics_constrained = physics_constrained
        self.best_params = None
        self.best_score = None
        self.study = None
        
    def optimize(self, X: pd.DataFrame, y: np.ndarray) -> Dict:
        """
        执行超参数优化
        """
        print(f"开始LightGBM超参数优化 (试验次数: {self.n_trials})")
        
        # 创建优化研究
        self.study = optuna.create_study(
            direction='minimize',  # 最小化RMSE
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        # 定义目标函数
        def objective(trial):
            return self._objective_function(trial, X, y)
        
        # 执行优化
        self.study.optimize(objective, n_trials=self.n_trials, show_progress_bar=True)
        
        # 保存最佳参数
        self.best_params = self.study.best_params
        self.best_score = self.study.best_value
        
        print(f"优化完成!")
        print(f"最佳RMSE: {self.best_score:.4f}")
        print(f"最佳参数: {self.best_params}")
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'study': self.study
        }
    
    def _objective_function(self, trial, X: pd.DataFrame, y: np.ndarray) -> float:
        """
        优化目标函数
        """
        # 定义超参数搜索空间
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': trial.suggest_int('num_leaves', 10, 300),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
            'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
            'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
            'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
            'random_state': 42,
            'verbose': -1
        }
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=self.cv_folds)
        scores = []
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # 确保数据类型一致
            X_train_clean = X_train.copy()
            X_val_clean = X_val.copy()

            for col in X_train_clean.columns:
                if X_train_clean[col].dtype == 'object' or X_train_clean[col].dtype.name == 'category':
                    X_train_clean[col] = pd.to_numeric(X_train_clean[col], errors='coerce').fillna(0)
                    X_val_clean[col] = pd.to_numeric(X_val_clean[col], errors='coerce').fillna(0)

            # 训练模型
            if self.physics_constrained:
                model = PhysicsConstrainedLightGBM()
                model.fit(X_train_clean, y_train, X_val_clean, y_val, **params)
                y_pred = model.predict(X_val_clean)
            else:
                train_data = lgb.Dataset(X_train_clean, label=y_train)
                val_data = lgb.Dataset(X_val_clean, label=y_val, reference=train_data)

                # 兼容不同版本的LightGBM
                try:
                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=1000,
                        callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
                    )
                except Exception:
                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=1000,
                        early_stopping_rounds=50,
                        verbose_eval=False
                    )

                y_pred = model.predict(X_val_clean)
            
            # 计算RMSE
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            scores.append(rmse)
        
        return np.mean(scores)
    
    def get_feature_importance_analysis(self, X: pd.DataFrame, y: np.ndarray) -> Dict:
        """
        使用最佳参数分析特征重要性
        """
        if self.best_params is None:
            raise ValueError("请先运行optimize方法")
        
        # 确保数据类型一致
        X_clean = X.copy()
        for col in X_clean.columns:
            if X_clean[col].dtype == 'object' or X_clean[col].dtype.name == 'category':
                X_clean[col] = pd.to_numeric(X_clean[col], errors='coerce').fillna(0)

        # 使用最佳参数训练模型
        if self.physics_constrained:
            model = PhysicsConstrainedLightGBM()
            model.fit(X_clean, y, **self.best_params)
            importance = model.feature_importance_
        else:
            train_data = lgb.Dataset(X_clean, label=y)
            # 兼容不同版本的LightGBM
            try:
                model = lgb.train(
                    self.best_params,
                    train_data,
                    num_boost_round=1000,
                    callbacks=[lgb.log_evaluation(0)]
                )
            except Exception:
                model = lgb.train(
                    self.best_params,
                    train_data,
                    num_boost_round=1000,
                    verbose_eval=False
                )
            importance = model.feature_importance(importance_type='gain')
        
        # 创建特征重要性DataFrame
        feature_importance_df = pd.DataFrame({
            'feature': X.columns,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return {
            'feature_importance': feature_importance_df,
            'top_10_features': feature_importance_df.head(10)['feature'].tolist(),
            'model': model
        }

class ModelEvaluator:
    """
    模型评估器
    提供全面的模型性能评估
    """
    
    @staticmethod
    def evaluate_model(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """
        全面评估模型性能
        """
        # 基础回归指标
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        
        # MAPE (处理零值)
        mask = y_true != 0
        mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100 if mask.any() else np.inf
        
        # 分功率区间评估
        power_ranges = {
            'zero_power': (y_true == 0),
            'low_power': (y_true > 0) & (y_true <= 1),
            'medium_power': (y_true > 1) & (y_true <= 5),
            'high_power': (y_true > 5)
        }
        
        range_metrics = {}
        for range_name, mask in power_ranges.items():
            if mask.any():
                range_rmse = np.sqrt(mean_squared_error(y_true[mask], y_pred[mask]))
                range_mae = mean_absolute_error(y_true[mask], y_pred[mask])
                range_metrics[range_name] = {
                    'count': mask.sum(),
                    'rmse': range_rmse,
                    'mae': range_mae
                }
        
        return {
            'overall_metrics': {
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'mape': mape
            },
            'range_metrics': range_metrics,
            'prediction_stats': {
                'pred_mean': np.mean(y_pred),
                'pred_std': np.std(y_pred),
                'pred_min': np.min(y_pred),
                'pred_max': np.max(y_pred)
            }
        }
    
    @staticmethod
    def print_evaluation_report(evaluation_results: Dict):
        """
        打印评估报告
        """
        print("\n" + "="*50)
        print("模型性能评估报告")
        print("="*50)
        
        # 整体指标
        overall = evaluation_results['overall_metrics']
        print(f"\n整体性能指标:")
        print(f"  RMSE: {overall['rmse']:.4f}")
        print(f"  MAE:  {overall['mae']:.4f}")
        print(f"  R²:   {overall['r2']:.4f}")
        print(f"  MAPE: {overall['mape']:.2f}%")
        
        # 分区间指标
        print(f"\n分功率区间性能:")
        for range_name, metrics in evaluation_results['range_metrics'].items():
            print(f"  {range_name}:")
            print(f"    样本数: {metrics['count']}")
            print(f"    RMSE: {metrics['rmse']:.4f}")
            print(f"    MAE:  {metrics['mae']:.4f}")
        
        # 预测统计
        pred_stats = evaluation_results['prediction_stats']
        print(f"\n预测值统计:")
        print(f"  均值: {pred_stats['pred_mean']:.4f}")
        print(f"  标准差: {pred_stats['pred_std']:.4f}")
        print(f"  范围: [{pred_stats['pred_min']:.4f}, {pred_stats['pred_max']:.4f}]")
