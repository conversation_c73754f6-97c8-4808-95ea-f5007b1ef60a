"""
风机参数数据处理模块
基于华锐科技SL1500/82和金风科技GW82/1500的真实风机参数
实现物理约束增强特征工程的基础设施
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
from pathlib import Path
from scipy import interpolate
import json

warnings.filterwarnings('ignore')

class TurbineParameterProcessor:
    """
    风机参数数据处理器
    
    处理两种风机类型的参数曲线：
    1. 华锐科技SL1500/82: 功率曲线 + 功率系数曲线 + 推力系数曲线
    2. 金风科技GW82/1500: 功率曲线 + 推力系数曲线 (缺失功率系数曲线)
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化风机参数处理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # 风机配置信息
        self.turbine_config = self.config.get('turbine_parameters', {})
        
        # 风机参数数据
        self.huarui_params = {}
        self.goldwind_params = {}
        
        # 插值函数
        self.huarui_interpolators = {}
        self.goldwind_interpolators = {}
        
        # 加载风机参数数据
        self._load_turbine_parameters()
        
        print("风机参数处理器初始化完成")
        print(f"华锐科技SL1500/82: {self.turbine_config.get('huarui_sl1500', {}).get('count', 0)}台")
        print(f"金风科技GW82/1500: {self.turbine_config.get('goldwind_gw82', {}).get('count', 0)}台")
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"警告：无法加载配置文件 {self.config_path}: {e}")
            return {}
    
    def _load_turbine_parameters(self):
        """加载风机参数数据"""
        print("正在加载风机参数数据...")
        
        # 加载华锐科技参数
        self._load_huarui_parameters()
        
        # 加载金风科技参数
        self._load_goldwind_parameters()
        
        # 创建插值函数
        self._create_interpolators()
        
        print("风机参数数据加载完成")
    
    def _load_huarui_parameters(self):
        """加载华锐科技SL1500/82参数"""
        huarui_config = self.turbine_config.get('huarui_sl1500', {})
        
        try:
            # 功率曲线
            power_curve_file = huarui_config.get('power_curve_file')
            if power_curve_file:
                self.huarui_params['power_curve'] = self._read_csv_with_encoding(power_curve_file)
                print(f"  华锐功率曲线加载成功: {len(self.huarui_params['power_curve'])}个数据点")
            
            # 功率系数曲线
            power_coeff_file = huarui_config.get('power_coefficient_file')
            if power_coeff_file:
                self.huarui_params['power_coefficient'] = self._read_csv_with_encoding(power_coeff_file)
                print(f"  华锐功率系数曲线加载成功: {len(self.huarui_params['power_coefficient'])}个数据点")
            
            # 推力系数曲线
            thrust_coeff_file = huarui_config.get('thrust_coefficient_file')
            if thrust_coeff_file:
                self.huarui_params['thrust_coefficient'] = self._read_csv_with_encoding(thrust_coeff_file)
                print(f"  华锐推力系数曲线加载成功: {len(self.huarui_params['thrust_coefficient'])}个数据点")
                
        except Exception as e:
            print(f"警告：华锐科技参数加载失败: {e}")
    
    def _load_goldwind_parameters(self):
        """加载金风科技GW82/1500参数"""
        goldwind_config = self.turbine_config.get('goldwind_gw82', {})
        
        try:
            # 功率曲线
            power_curve_file = goldwind_config.get('power_curve_file')
            if power_curve_file:
                self.goldwind_params['power_curve'] = self._read_csv_with_encoding(power_curve_file)
                print(f"  金风功率曲线加载成功: {len(self.goldwind_params['power_curve'])}个数据点")
            
            # 推力系数曲线
            thrust_coeff_file = goldwind_config.get('thrust_coefficient_file')
            if thrust_coeff_file:
                self.goldwind_params['thrust_coefficient'] = self._read_csv_with_encoding(thrust_coeff_file)
                print(f"  金风推力系数曲线加载成功: {len(self.goldwind_params['thrust_coefficient'])}个数据点")
            
            # 金风科技缺失功率系数曲线，需要估算
            print("  金风科技缺失功率系数曲线，将基于功率曲线估算")
            
        except Exception as e:
            print(f"警告：金风科技参数加载失败: {e}")
    
    def _read_csv_with_encoding(self, file_path: str) -> pd.DataFrame:
        """使用多种编码尝试读取CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                # 标准化列名
                df.columns = df.columns.str.strip()
                if len(df.columns) >= 2:
                    df.columns = ['wind_speed', 'value']
                return df
            except (UnicodeDecodeError, ValueError):
                continue
        
        raise ValueError(f"无法用常见编码读取文件: {file_path}")
    
    def _create_interpolators(self):
        """创建插值函数"""
        print("创建插值函数...")
        
        # 华锐科技插值函数
        if 'power_curve' in self.huarui_params:
            df = self.huarui_params['power_curve']
            self.huarui_interpolators['power'] = interpolate.interp1d(
                df['wind_speed'], df['value'], 
                kind='linear', bounds_error=False, fill_value=0
            )
        
        if 'power_coefficient' in self.huarui_params:
            df = self.huarui_params['power_coefficient']
            self.huarui_interpolators['power_coefficient'] = interpolate.interp1d(
                df['wind_speed'], df['value'],
                kind='linear', bounds_error=False, fill_value=0
            )
        
        if 'thrust_coefficient' in self.huarui_params:
            df = self.huarui_params['thrust_coefficient']
            self.huarui_interpolators['thrust_coefficient'] = interpolate.interp1d(
                df['wind_speed'], df['value'],
                kind='linear', bounds_error=False, fill_value=0
            )
        
        # 金风科技插值函数
        if 'power_curve' in self.goldwind_params:
            df = self.goldwind_params['power_curve']
            self.goldwind_interpolators['power'] = interpolate.interp1d(
                df['wind_speed'], df['value'],
                kind='linear', bounds_error=False, fill_value=0
            )
        
        if 'thrust_coefficient' in self.goldwind_params:
            df = self.goldwind_params['thrust_coefficient']
            self.goldwind_interpolators['thrust_coefficient'] = interpolate.interp1d(
                df['wind_speed'], df['value'],
                kind='linear', bounds_error=False, fill_value=0
            )
        
        # 为金风科技估算功率系数曲线
        self._estimate_goldwind_power_coefficient()
        
        print("插值函数创建完成")
    
    def _estimate_goldwind_power_coefficient(self):
        """为金风科技估算功率系数曲线"""
        if 'power_curve' not in self.goldwind_params:
            return
        
        print("  正在为金风科技估算功率系数曲线...")
        
        # 基于功率曲线和理论公式估算功率系数
        # Cp = P / (0.5 * ρ * A * V³)
        # 其中：P=功率，ρ=空气密度(1.225)，A=扫风面积，V=风速
        
        power_df = self.goldwind_params['power_curve']
        
        # 金风GW82/1500的扫风面积 (直径82m)
        rotor_diameter = 82  # 米
        swept_area = np.pi * (rotor_diameter / 2) ** 2  # 平方米
        air_density = 1.225  # kg/m³
        
        # 计算功率系数
        wind_speeds = power_df['wind_speed'].values
        powers = power_df['value'].values * 1000  # MW转换为kW
        
        # 理论最大功率
        theoretical_power = 0.5 * air_density * swept_area * (wind_speeds ** 3) / 1000  # kW
        
        # 功率系数 = 实际功率 / 理论最大功率
        power_coefficients = np.where(theoretical_power > 0, powers / theoretical_power, 0)
        
        # 限制功率系数在合理范围内 (0-0.59，贝茨极限)
        power_coefficients = np.clip(power_coefficients, 0, 0.59)
        
        # 创建插值函数
        self.goldwind_interpolators['power_coefficient'] = interpolate.interp1d(
            wind_speeds, power_coefficients,
            kind='linear', bounds_error=False, fill_value=0
        )
        
        print(f"  金风科技功率系数曲线估算完成，最大Cp={power_coefficients.max():.3f}")
    
    def get_huarui_power(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取华锐科技风机的功率输出"""
        if 'power' not in self.huarui_interpolators:
            return np.zeros_like(wind_speeds)
        return self.huarui_interpolators['power'](wind_speeds)
    
    def get_goldwind_power(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取金风科技风机的功率输出"""
        if 'power' not in self.goldwind_interpolators:
            return np.zeros_like(wind_speeds)
        return self.goldwind_interpolators['power'](wind_speeds)
    
    def get_huarui_power_coefficient(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取华锐科技风机的功率系数"""
        if 'power_coefficient' not in self.huarui_interpolators:
            return np.zeros_like(wind_speeds)
        return self.huarui_interpolators['power_coefficient'](wind_speeds)
    
    def get_goldwind_power_coefficient(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取金风科技风机的功率系数（估算值）"""
        if 'power_coefficient' not in self.goldwind_interpolators:
            return np.zeros_like(wind_speeds)
        return self.goldwind_interpolators['power_coefficient'](wind_speeds)
    
    def get_huarui_thrust_coefficient(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取华锐科技风机的推力系数"""
        if 'thrust_coefficient' not in self.huarui_interpolators:
            return np.zeros_like(wind_speeds)
        return self.huarui_interpolators['thrust_coefficient'](wind_speeds)
    
    def get_goldwind_thrust_coefficient(self, wind_speeds: np.ndarray) -> np.ndarray:
        """获取金风科技风机的推力系数"""
        if 'thrust_coefficient' not in self.goldwind_interpolators:
            return np.zeros_like(wind_speeds)
        return self.goldwind_interpolators['thrust_coefficient'](wind_speeds)
    
    def get_combined_power(self, wind_speeds: np.ndarray, 
                          huarui_weight: float = 0.5, 
                          goldwind_weight: float = 0.5) -> np.ndarray:
        """
        获取两种风机的组合功率输出
        
        Args:
            wind_speeds: 风速数组
            huarui_weight: 华锐科技风机权重
            goldwind_weight: 金风科技风机权重
        """
        huarui_power = self.get_huarui_power(wind_speeds)
        goldwind_power = self.get_goldwind_power(wind_speeds)
        
        return huarui_power * huarui_weight + goldwind_power * goldwind_weight
    
    def get_turbine_counts(self) -> Tuple[int, int]:
        """获取风机数量"""
        huarui_count = self.turbine_config.get('huarui_sl1500', {}).get('count', 67)
        goldwind_count = self.turbine_config.get('goldwind_gw82', {}).get('count', 67)
        return huarui_count, goldwind_count
    
    def get_parameter_summary(self) -> Dict:
        """获取参数摘要信息"""
        huarui_count, goldwind_count = self.get_turbine_counts()
        
        summary = {
            'huarui_sl1500': {
                'count': huarui_count,
                'has_power_curve': 'power' in self.huarui_interpolators,
                'has_power_coefficient': 'power_coefficient' in self.huarui_interpolators,
                'has_thrust_coefficient': 'thrust_coefficient' in self.huarui_interpolators
            },
            'goldwind_gw82': {
                'count': goldwind_count,
                'has_power_curve': 'power' in self.goldwind_interpolators,
                'has_power_coefficient': 'power_coefficient' in self.goldwind_interpolators,
                'has_thrust_coefficient': 'thrust_coefficient' in self.goldwind_interpolators
            },
            'total_turbines': huarui_count + goldwind_count,
            'total_capacity_mw': (huarui_count + goldwind_count) * 1.5
        }
        
        return summary
