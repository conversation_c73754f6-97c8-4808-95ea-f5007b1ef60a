"""
风力发电功率预测系统 - 启动脚本
作者：国网缙云县供电公司 韩剑
"""

import subprocess
import sys
from pathlib import Path

# 导入配置管理器
sys.path.append(str(Path(__file__).parent / "src"))
from config_manager import ConfigManager

def run_script(script_name, description):
    """运行脚本并处理错误"""
    print(f"\n{'='*60}")
    print(f"正在运行: {description}")
    print(f"脚本: {script_name}")
    print('='*60)
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True, 
                              check=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误代码: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出现错误: {e}")
        return False

def check_data_files():
    """检查配置的数据文件是否存在"""
    try:
        config = ConfigManager()

        # 从配置获取文件信息
        data_dir = Path(config.get('data_files.data_directory', 'data'))

        print("检查配置的数据文件...")

        if not data_dir.exists():
            print(f"❌ 数据目录不存在: {data_dir}")
            return False

        # 检查是否启用多点位模式
        multi_location_config = config.get('multi_location', {})
        if multi_location_config.get('enable', False):
            return check_multi_location_data_files(config)
        else:
            return check_single_location_data_files(config, data_dir)

    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def check_single_location_data_files(config, data_dir):
    """检查单点位模式的数据文件"""
    train_file = config.get('data_files.train_file')
    test_file = config.get('data_files.test_file')

    train_path = data_dir / train_file if train_file else None
    test_path = data_dir / test_file if test_file else None

    issues = []

    if not train_file:
        issues.append("未配置训练文件")
    elif not train_path.exists():
        issues.append(f"训练文件不存在: {train_file}")
    else:
        print(f"✅ 训练文件: {train_file}")

    if not test_file:
        issues.append("未配置测试文件")
    elif not test_path.exists():
        issues.append(f"测试文件不存在: {test_file}")
    else:
        print(f"✅ 测试文件: {test_file}")

    if issues:
        print("⚠️  发现配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        print("\n建议运行: python configure.py 来配置数据文件")
        return False

    print("✅ 单点位数据文件检查通过")

    # 检查多源风速数据
    check_multi_source_wind_data(config)

    return True

def check_multi_location_data_files(config):
    """检查多点位模式的数据文件"""
    multi_location_config = config.get('multi_location', {})

    # 获取训练和测试数据目录，如果未配置则使用data_directory作为默认值
    default_data_dir = config.get('data_files.data_directory', 'data')
    train_data_dir = Path(config.get('data_files.train_data_directory', default_data_dir))
    test_data_dir = Path(config.get('data_files.test_data_directory', default_data_dir))

    # 检查目标功率文件
    target_power_file = multi_location_config.get('target_power_file', '点位c_发电功率.csv')
    power_path = train_data_dir / target_power_file

    # 检查训练和测试文件模式
    train_pattern = multi_location_config.get('train_file_pattern', '15min_历史气象20250228之前')
    test_pattern = multi_location_config.get('test_file_pattern', '15min_历史气象202503')

    # 检查点位配置
    locations = multi_location_config.get('locations', {})

    issues = []

    # 检查目标功率文件
    if not power_path.exists():
        issues.append(f"目标功率文件不存在: {target_power_file}")
    else:
        print(f"✅ 目标功率文件: {target_power_file}")

    # 检查各点位的训练数据文件
    print(f"检查训练数据文件 (目录: {train_data_dir})...")
    missing_train_files = []
    for loc_id in locations.keys():
        train_file = f"点位{loc_id}_{train_pattern}.csv"
        train_path = train_data_dir / train_file
        if not train_path.exists():
            missing_train_files.append(train_file)
        else:
            print(f"  ✅ {train_file}")

    if missing_train_files:
        issues.append(f"缺少训练数据文件: {', '.join(missing_train_files)}")

    # 检查各点位的测试数据文件
    print(f"检查测试数据文件 (目录: {test_data_dir})...")
    missing_test_files = []
    for loc_id in locations.keys():
        test_file = f"点位{loc_id}_{test_pattern}.csv"
        test_path = test_data_dir / test_file
        if not test_path.exists():
            missing_test_files.append(test_file)
        else:
            print(f"  ✅ {test_file}")

    if missing_test_files:
        issues.append(f"缺少测试数据文件: {', '.join(missing_test_files)}")

    if issues:
        print("⚠️  发现配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        print("\n请确保所有多点位数据文件都存在于data目录中")
        return False

    print("✅ 多点位数据文件检查通过")

    # 检查多源风速数据
    check_multi_source_wind_data(config)

    return True

def check_multi_source_wind_data(config):
    """检查多源风速数据配置和文件"""
    print("\n检查多源风速数据...")
    print("-" * 40)

    multi_source_config = config.get('multi_source_wind', {})

    if not multi_source_config.get('enable', False):
        print("⚠️  多源风速数据功能未启用")
        print("   如需启用，请在config.json中设置 multi_source_wind.enable = true")
        return False

    print("✅ 多源风速数据功能已启用")

    # 检查配置项
    train_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
    test_file = multi_source_config.get('test_file', '站点c_多网站历史风速202503.csv')

    print(f"   训练文件: {train_file}")
    print(f"   测试文件: {test_file}")

    # 检查文件存在性
    train_data_dir = config.get('data_files.train_data_directory', 'data/training')
    test_data_dir = config.get('data_files.test_data_directory', 'data/testing')

    train_path = Path(train_data_dir) / train_file
    test_path = Path(test_data_dir) / test_file

    train_exists = train_path.exists()
    test_exists = test_path.exists()

    print(f"   训练文件存在: {'✅' if train_exists else '❌'} {train_path}")
    print(f"   测试文件存在: {'✅' if test_exists else '❌'} {test_path}")

    if train_exists and test_exists:
        print("✅ 多源风速数据文件检查通过")
        print("   预期效果: 准确率从0.894提升到0.95+ (34个气象模型融合)")
        return True
    else:
        print("⚠️  部分多源风速数据文件缺失")
        print("   系统将使用现有特征工程，但无法获得多源数据的精度提升")
        return False

def main():
    """主启动流程"""
    print("🚀 风力发电功率预测系统")
    print("=" * 60)
    print("基于LightGBM的高精度风力发电功率预测")
    print("集成物理约束和创新特征工程")

    # 显示多源风速数据状态
    try:
        config = ConfigManager()
        multi_source_config = config.get('multi_source_wind', {})
        if multi_source_config.get('enable', False):
            print("🌟 多源风速数据融合已启用 (34个气象模型)")
        else:
            print("⚠️  多源风速数据融合未启用")
    except:
        pass

    print("=" * 60)
    
    # 检查配置的数据文件
    if not check_data_files():
        print("\n请配置数据文件:")
        print("  python configure.py")
        print("或选择选项9进行配置")
        return
    
    # 询问用户是否要运行完整流程
    print("\n选择运行模式:")
    print("=" * 40)
    print("1. 训练并导出生产模型")
    print("2. 预测")
    print("3. 下载多点位天气预报数据")

    try:
        choice = input("\n请选择 (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        return
    
    if choice == "1":
        # 训练并导出生产模型
        run_script("train_and_export_model.py", "训练并导出生产模型")

    elif choice == "2":
        # 今日预测
        run_script("predict_today.py", "预测")

    elif choice == "3":
        # 下载多点位天气预报数据
        run_script("下载15min天气预报.py", "下载多点位天气预报数据")


    else:
        print("❌ 无效选择，请选择 1-3 中的一个")
        return
    
    print(f"\n" + "="*60)
    print("感谢使用风力发电功率预测系统!")
    print("作者：国网缙云县供电公司 韩剑")
    print("="*60)

if __name__ == "__main__":
    main()
