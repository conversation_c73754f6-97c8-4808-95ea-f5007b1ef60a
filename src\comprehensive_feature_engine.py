"""
综合特征工程引擎
集成所有方案的特征工程和数据优化
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

from .advanced_physics_features import AdvancedPhysicsFeatures
from .advanced_time_features import AdvancedTimeFeatures
from .breakthrough_features import BreakthroughFeatures
from .data_quality_optimizer import DataQualityOptimizer
from .multi_source_wind_fusion import MultiSourceWindFusion

class ComprehensiveFeatureEngine:
    """
    综合特征工程引擎
    整合方案1、2、4、5的所有特征工程和数据优化
    """
    
    def __init__(self, config_manager=None, time_col: str = '时间'):
        self.time_col = time_col
        self.config_manager = config_manager

        # 初始化各个特征工程器
        self.physics_features = AdvancedPhysicsFeatures()
        self.time_features = AdvancedTimeFeatures(time_col)
        self.breakthrough_features = BreakthroughFeatures()
        self.data_optimizer = DataQualityOptimizer()

        # 初始化多源风速融合器（如果配置管理器可用）
        if config_manager:
            self.multi_source_fusion = MultiSourceWindFusion(config_manager, time_col)
        else:
            self.multi_source_fusion = None

        # 特征统计
        self.feature_stats = {}
        
    def process_comprehensive_features(self, df: pd.DataFrame,
                                     multi_source_data: Optional[pd.DataFrame] = None,
                                     enable_data_quality: bool = True,
                                     enable_physics: bool = True,
                                     enable_time: bool = True,
                                     enable_breakthrough: bool = True,
                                     enable_multi_source: bool = True) -> pd.DataFrame:
        """
        执行综合特征工程处理
        
        Args:
            df: 输入数据框
            enable_data_quality: 是否启用数据质量优化
            enable_physics: 是否启用深度物理建模
            enable_time: 是否启用时间序列深度挖掘
            enable_breakthrough: 是否启用突破性特征工程
        """
        print("=" * 60)
        print("开始综合特征工程处理")
        print("=" * 60)
        
        df_processed = df.copy()
        original_columns = set(df_processed.columns)
        
        # 1. 数据质量优化 (方案5)
        if enable_data_quality:
            print("\n1. 数据质量优化 (方案5)")
            print("-" * 30)
            df_processed = self.data_optimizer.optimize_data_quality(df_processed)
            quality_columns = set(df_processed.columns) - original_columns
            self.feature_stats['data_quality'] = {
                'new_features': len(quality_columns),
                'feature_names': list(quality_columns)
            }
            print(f" 数据质量优化完成，新增 {len(quality_columns)} 个特征")
        
        # 2. 深度物理建模增强 (方案1)
        if enable_physics:
            print("\n2. 深度物理建模增强 (方案1)")
            print("-" * 30)
            before_physics = set(df_processed.columns)
            df_processed = self.physics_features.create_all_advanced_physics_features(df_processed)
            physics_columns = set(df_processed.columns) - before_physics
            self.feature_stats['physics'] = {
                'new_features': len(physics_columns),
                'feature_names': list(physics_columns)
            }
            print(f" 深度物理建模完成，新增 {len(physics_columns)} 个特征")
        
        # 3. 时间序列深度挖掘 (方案2)
        if enable_time:
            print("\n3. 时间序列深度挖掘 (方案2)")
            print("-" * 30)
            before_time = set(df_processed.columns)
            df_processed = self.time_features.create_all_advanced_time_features(df_processed)
            time_columns = set(df_processed.columns) - before_time
            self.feature_stats['time_series'] = {
                'new_features': len(time_columns),
                'feature_names': list(time_columns)
            }
            print(f" 时间序列深度挖掘完成，新增 {len(time_columns)} 个特征")
        
        # 4. 突破性特征工程 (方案4)
        if enable_breakthrough:
            print("\n4. 突破性特征工程 (方案4)")
            print("-" * 30)
            before_breakthrough = set(df_processed.columns)
            df_processed = self.breakthrough_features.create_all_breakthrough_features(df_processed)
            breakthrough_columns = set(df_processed.columns) - before_breakthrough
            self.feature_stats['breakthrough'] = {
                'new_features': len(breakthrough_columns),
                'feature_names': list(breakthrough_columns)
            }
            print(f" 突破性特征工程完成，新增 {len(breakthrough_columns)} 个特征")

        # 5. 多源风速数据融合 (新增方案)
        if enable_multi_source and multi_source_data is not None and self.multi_source_fusion is not None:
            print("\n5. 多源风速数据融合 (新增方案)")
            print("-" * 30)
            before_multi_source = set(df_processed.columns)

            # 融合多源风速数据
            multi_source_features = self.multi_source_fusion.fuse_multi_source_data(multi_source_data)

            # 将多源特征按时间合并到主数据集
            # 确保时间列格式一致
            if self.time_col in df_processed.columns:
                df_processed[self.time_col] = pd.to_datetime(df_processed[self.time_col])
                multi_source_features.index = pd.to_datetime(multi_source_features.index)

                # 使用时间索引进行合并
                df_processed = df_processed.set_index(self.time_col)
                df_processed = pd.concat([df_processed, multi_source_features], axis=1, join='inner')
                df_processed = df_processed.reset_index()
            else:
                # 如果没有时间列，直接按索引合并
                df_processed = pd.concat([df_processed, multi_source_features], axis=1, join='inner')

            multi_source_columns = set(df_processed.columns) - before_multi_source
            self.feature_stats['multi_source'] = {
                'new_features': len(multi_source_columns),
                'feature_names': list(multi_source_columns)
            }
            print(f" 多源风速数据融合完成，新增 {len(multi_source_columns)} 个特征")

        # 6. 特征后处理
        df_processed = self._post_process_features(df_processed)
        
        # 7. 生成特征报告
        self._generate_feature_report(df, df_processed)

        print("\n" + "=" * 60)
        print("综合特征工程处理完成!")
        print("=" * 60)
        
        return df_processed
    
    def _post_process_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特征后处理
        """
        print("\n6. 特征后处理")
        print("-" * 30)
        
        # 处理无穷值和NaN
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        # 替换无穷值
        df[numeric_columns] = df[numeric_columns].replace([np.inf, -np.inf], np.nan)
        
        # 填充剩余的NaN值
        for col in numeric_columns:
            if df[col].isna().sum() > 0:
                # 使用中位数填充
                median_val = df[col].median()
                if pd.isna(median_val):
                    median_val = 0
                df[col] = df[col].fillna(median_val)
        
        # 特征缩放 (对于极值特征)
        extreme_columns = [col for col in df.columns if any(keyword in col.lower() 
                          for keyword in ['fractal', 'lyapunov', 'reynolds', 'spectral'])]
        
        for col in extreme_columns:
            if col in df.columns:
                # 使用robust scaling
                q25 = df[col].quantile(0.25)
                q75 = df[col].quantile(0.75)
                iqr = q75 - q25
                
                if iqr > 0:
                    df[col] = (df[col] - df[col].median()) / iqr
                    df[col] = np.clip(df[col], -5, 5)  # 限制极值
        
        print(" 特征后处理完成")
        
        return df
    
    def _generate_feature_report(self, original_df: pd.DataFrame, processed_df: pd.DataFrame):
        """
        生成特征工程报告
        """
        print("\n6. 特征工程报告")
        print("-" * 30)
        
        original_features = len(original_df.columns)
        final_features = len(processed_df.columns)
        new_features = final_features - original_features
        
        print(f"原始特征数: {original_features}")
        print(f"最终特征数: {final_features}")
        print(f"新增特征数: {new_features}")
        print(f"特征增长率: {(new_features / original_features * 100):.1f}%")
        
        # 各模块特征统计
        print("\n各模块特征贡献:")
        for module, stats in self.feature_stats.items():
            print(f"  {module}: {stats['new_features']} 个特征")
        
        # 数据质量统计
        if 'overall_data_quality' in processed_df.columns:
            avg_quality = processed_df['overall_data_quality'].mean()
            print(f"\n整体数据质量评分: {avg_quality:.3f}")
        
        # 内存使用统计
        original_memory = original_df.memory_usage(deep=True).sum() / 1024 / 1024
        final_memory = processed_df.memory_usage(deep=True).sum() / 1024 / 1024
        print(f"\n内存使用:")
        print(f"  原始数据: {original_memory:.1f} MB")
        print(f"  处理后数据: {final_memory:.1f} MB")
        print(f"  内存增长: {((final_memory - original_memory) / original_memory * 100):.1f}%")
    
    def get_feature_importance_groups(self) -> Dict[str, list]:
        """
        获取特征重要性分组
        用于模型训练时的特征选择
        """
        groups = {
            'core_features': [
                'wind_speed_80m', 'wind_speed_10m', 'temperature_2m', 'rain',
                'wind_direction_80m', 'wind_direction_10m'
            ],
            'physics_features': [],
            'time_features': [],
            'breakthrough_features': [],
            'quality_features': []
        }
        
        # 根据特征统计填充分组
        for module, stats in self.feature_stats.items():
            if module == 'physics':
                groups['physics_features'] = stats['feature_names']
            elif module == 'time_series':
                groups['time_features'] = stats['feature_names']
            elif module == 'breakthrough':
                groups['breakthrough_features'] = stats['feature_names']
            elif module == 'data_quality':
                groups['quality_features'] = stats['feature_names']
        
        return groups
    
    def get_recommended_feature_selection(self, importance_threshold: float = 0.8) -> Dict[str, list]:
        """
        获取推荐的特征选择策略
        """
        groups = self.get_feature_importance_groups()
        
        recommendations = {
            'essential': groups['core_features'],  # 必需特征
            'high_priority': [],  # 高优先级特征
            'medium_priority': [],  # 中优先级特征
            'experimental': []  # 实验性特征
        }
        
        # 高优先级：物理特征和时间特征的核心部分
        physics_high_priority = [f for f in groups['physics_features'] 
                               if any(keyword in f for keyword in [
                                   'weibull', 'reynolds', 'tip_speed', 'power_density',
                                   'wind_shear', 'atmospheric_stability'
                               ])]
        
        time_high_priority = [f for f in groups['time_features']
                            if any(keyword in f for keyword in [
                                'cycle_', 'weather_front', 'wind_persistence',
                                'trend_component', 'seasonal_component'
                            ])]
        
        recommendations['high_priority'] = physics_high_priority + time_high_priority
        
        # 中优先级：突破性特征的稳定部分
        breakthrough_medium = [f for f in groups['breakthrough_features']
                             if any(keyword in f for keyword in [
                                 'phase_', 'wind_shear_', 'turbulence_',
                                 'temperature_gradient', 'multivariate_'
                             ])]
        
        recommendations['medium_priority'] = breakthrough_medium
        
        # 实验性：高级和复杂特征
        experimental_features = [f for f in groups['breakthrough_features']
                               if any(keyword in f for keyword in [
                                   'fractal', 'lyapunov', 'spectral_entropy',
                                   'nonlinear', 'chaos'
                               ])]
        
        recommendations['experimental'] = experimental_features + groups['quality_features']
        
        return recommendations
    
    def save_feature_config(self, filepath: str):
        """
        保存特征工程配置
        """
        import json
        
        config = {
            'feature_stats': self.feature_stats,
            'feature_groups': self.get_feature_importance_groups(),
            'recommendations': self.get_recommended_feature_selection(),
            'processing_info': {
                'time_column': self.time_col,
                'modules_used': list(self.feature_stats.keys())
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2, default=str)
        
        print(f" 特征工程配置已保存到: {filepath}")
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要
        """
        return {
            'modules_enabled': list(self.feature_stats.keys()),
            'total_new_features': sum(stats['new_features'] for stats in self.feature_stats.values()),
            'feature_breakdown': self.feature_stats,
            'recommendations': self.get_recommended_feature_selection()
        }
