"""
配置管理器
处理项目配置文件的读取、验证和更新
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any

class ConfigManager:
    """
    配置管理器
    负责配置文件的加载、验证、更新和交互式配置
    """
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"配置文件已加载: {self.config_file}")
            except Exception as e:
                print(f"配置文件加载失败: {e}")
                self.create_default_config()
        else:
            print(f"配置文件不存在，创建默认配置...")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config = {
            "data_files": {
                "train_file": "训练集20250228之前.csv",
                "test_file": "测试集202503.csv",
                "data_directory": "data"
            },
            "features": {
                "time_column": "时间",
                "target_column": "理论功率 (MW)",
                "feature_columns": [
                    "wind_speed_10m", "wind_speed_80m", 
                    "wind_direction_10m", "wind_direction_80m",
                    "wind_gusts_10m", "temperature_2m",
                    "rain", "apparent_temperature"
                ]
            },
            "model_settings": {
                "cv_folds": 5,
                "n_trials": 50,
                "physics_constrained": True,
                "random_state": 42
            },
            "output_directories": {
                "processed_data": "processed_data",
                "production_model": "production_model",
                "daily_predictions": "daily_predictions",
                "results": "results",
                "visualizations": "visualizations"
            },
            "encoding": {
                "preferred_encodings": ["utf-8", "gbk", "gb2312", "utf-8-sig", "cp1252"],
                "auto_detect": True
            }
        }
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值，支持嵌套键如 'data_files.train_file'"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """设置配置值，支持嵌套键"""
        keys = key_path.split('.')
        config_ref = self.config
        
        # 导航到最后一级
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
        
        # 设置值
        config_ref[keys[-1]] = value
        self.save_config()
    
    def get_data_files(self) -> Dict[str, str]:
        """获取数据文件配置"""
        return {
            'train_file': self.get('data_files.train_file'),
            'test_file': self.get('data_files.test_file'),
            'data_directory': self.get('data_files.data_directory', 'data')
        }
    
    def get_train_file_path(self) -> Path:
        """获取训练文件完整路径"""
        data_dir = self.get('data_files.data_directory', 'data')
        train_file = self.get('data_files.train_file')
        return Path(data_dir) / train_file
    
    def get_test_file_path(self) -> Path:
        """获取测试文件完整路径"""
        data_dir = self.get('data_files.data_directory', 'data')
        test_file = self.get('data_files.test_file')
        return Path(data_dir) / test_file
    
    def list_available_files(self, file_type: str = "csv") -> List[str]:
        """列出data目录中可用的文件"""
        data_dir = Path(self.get('data_files.data_directory', 'data'))
        
        if not data_dir.exists():
            return []
        
        files = list(data_dir.glob(f"*.{file_type}"))
        return [f.name for f in files]
    
    def interactive_config(self):
        """交互式配置"""
        print("=" * 60)
        print(" 交互式配置")
        print("=" * 60)
        
        # 显示当前配置
        self.show_current_config()
        
        print("\n选择要配置的项目:")
        print("1. 数据文件设置")
        print("2. 特征列设置")
        print("3. 模型参数设置")
        print("4. 输出目录设置")
        print("5. 查看所有可用文件")
        print("0. 退出配置")
        
        try:
            choice = input("\n请选择 (0-5): ").strip()
            
            if choice == "1":
                self.config_data_files()
            elif choice == "2":
                self.config_features()
            elif choice == "3":
                self.config_model_settings()
            elif choice == "4":
                self.config_output_directories()
            elif choice == "5":
                self.show_available_files()
            elif choice == "0":
                print("配置完成")
                return
            else:
                print("无效选择")
                
        except KeyboardInterrupt:
            print("\n配置已取消")
    
    def config_data_files(self):
        """配置数据文件"""
        print("\n📁 数据文件配置")
        print("-" * 30)
        
        # 显示可用文件
        available_files = self.list_available_files()
        if available_files:
            print("可用的CSV文件:")
            for i, file in enumerate(available_files, 1):
                print(f"  {i}. {file}")
        else:
            print("data目录中没有找到CSV文件")
        
        # 配置训练文件
        current_train = self.get('data_files.train_file')
        print(f"\n当前训练文件: {current_train}")
        
        new_train = input("输入新的训练文件名 (回车保持不变): ").strip()
        if new_train:
            self.set('data_files.train_file', new_train)
            print(f" 训练文件已更新: {new_train}")
        
        # 配置测试文件
        current_test = self.get('data_files.test_file')
        print(f"\n当前测试文件: {current_test}")
        
        new_test = input("输入新的测试文件名 (回车保持不变): ").strip()
        if new_test:
            self.set('data_files.test_file', new_test)
            print(f" 测试文件已更新: {new_test}")
        
        # 验证文件是否存在
        self.validate_data_files()
    
    def config_features(self):
        """配置特征列"""
        print("\n 特征列配置")
        print("-" * 30)
        
        current_target = self.get('features.target_column')
        print(f"当前目标列: {current_target}")
        
        new_target = input("输入新的目标列名 (回车保持不变): ").strip()
        if new_target:
            self.set('features.target_column', new_target)
            print(f" 目标列已更新: {new_target}")
        
        current_features = self.get('features.feature_columns', [])
        print(f"\n当前特征列 ({len(current_features)} 个):")
        for i, feature in enumerate(current_features, 1):
            print(f"  {i}. {feature}")
        
        modify = input("\n是否修改特征列? (y/n): ").strip().lower()
        if modify == 'y':
            print("输入新的特征列 (每行一个，空行结束):")
            new_features = []
            while True:
                feature = input().strip()
                if not feature:
                    break
                new_features.append(feature)
            
            if new_features:
                self.set('features.feature_columns', new_features)
                print(f" 特征列已更新: {len(new_features)} 个特征")
    
    def config_model_settings(self):
        """配置模型参数"""
        print("\n 模型参数配置")
        print("-" * 30)
        
        settings = [
            ('cv_folds', '交叉验证折数', int),
            ('n_trials', '超参数优化试验次数', int),
            ('physics_constrained', '是否使用物理约束', bool),
            ('random_state', '随机种子', int)
        ]
        
        for key, desc, dtype in settings:
            current_value = self.get(f'model_settings.{key}')
            print(f"\n{desc}: {current_value}")
            
            new_value = input(f"输入新值 (回车保持不变): ").strip()
            if new_value:
                try:
                    if dtype == bool:
                        new_value = new_value.lower() in ['true', 'yes', 'y', '1']
                    else:
                        new_value = dtype(new_value)
                    
                    self.set(f'model_settings.{key}', new_value)
                    print(f" {desc}已更新: {new_value}")
                except ValueError:
                    print(f" 无效值，保持原值: {current_value}")
    
    def config_output_directories(self):
        """配置输出目录"""
        print("\n 输出目录配置")
        print("-" * 30)
        
        directories = self.get('output_directories', {})
        for key, current_dir in directories.items():
            print(f"\n{key}: {current_dir}")
            new_dir = input(f"输入新目录 (回车保持不变): ").strip()
            if new_dir:
                self.set(f'output_directories.{key}', new_dir)
                print(f" {key}目录已更新: {new_dir}")
    
    def show_available_files(self):
        """显示所有可用文件"""
        print("\n 可用文件列表")
        print("-" * 30)
        
        data_dir = Path(self.get('data_files.data_directory', 'data'))
        if not data_dir.exists():
            print(f"数据目录不存在: {data_dir}")
            return
        
        csv_files = list(data_dir.glob("*.csv"))
        if csv_files:
            print("CSV文件:")
            for i, file in enumerate(csv_files, 1):
                size = file.stat().st_size / 1024 / 1024  # MB
                print(f"  {i:2d}. {file.name} ({size:.1f} MB)")
        else:
            print("未找到CSV文件")
        
        # 显示其他文件
        other_files = [f for f in data_dir.iterdir() if f.is_file() and f.suffix != '.csv']
        if other_files:
            print(f"\n其他文件:")
            for file in other_files[:10]:  # 只显示前10个
                print(f"      {file.name}")
    
    def show_current_config(self):
        """显示当前配置"""
        print("\n 当前配置:")
        print("-" * 30)
        
        data_files = self.get_data_files()
        print(f"训练文件: {data_files['train_file']}")
        print(f"测试文件: {data_files['test_file']}")
        print(f"数据目录: {data_files['data_directory']}")
        
        print(f"\n目标列: {self.get('features.target_column')}")
        feature_count = len(self.get('features.feature_columns', []))
        print(f"特征列数: {feature_count}")
        
        print(f"\n模型设置:")
        print(f"  交叉验证: {self.get('model_settings.cv_folds')} 折")
        print(f"  优化试验: {self.get('model_settings.n_trials')} 次")
        print(f"  物理约束: {self.get('model_settings.physics_constrained')}")
    
    def validate_data_files(self):
        """验证数据文件是否存在"""
        print("\n🔍 验证数据文件...")
        
        train_path = self.get_train_file_path()
        test_path = self.get_test_file_path()
        
        if train_path.exists():
            print(f" 训练文件存在: {train_path}")
        else:
            print(f" 训练文件不存在: {train_path}")
        
        if test_path.exists():
            print(f" 测试文件存在: {test_path}")
        else:
            print(f" 测试文件不存在: {test_path}")
    
    def quick_setup(self):
        """快速设置 - 只配置文件名"""
        print(" 快速文件配置")
        print("-" * 30)
        
        # 显示可用文件
        available_files = self.list_available_files()
        if available_files:
            print("可用文件:")
            for i, file in enumerate(available_files, 1):
                print(f"  {i}. {file}")
            
            # 选择训练文件
            try:
                train_choice = input(f"\n选择训练文件编号 (1-{len(available_files)}) 或直接输入文件名: ").strip()
                
                if train_choice.isdigit():
                    train_idx = int(train_choice) - 1
                    if 0 <= train_idx < len(available_files):
                        train_file = available_files[train_idx]
                    else:
                        train_file = input("输入训练文件名: ").strip()
                else:
                    train_file = train_choice
                
                self.set('data_files.train_file', train_file)
                print(f" 训练文件: {train_file}")
                
                # 选择测试文件
                test_choice = input(f"\n选择测试文件编号 (1-{len(available_files)}) 或直接输入文件名: ").strip()
                
                if test_choice.isdigit():
                    test_idx = int(test_choice) - 1
                    if 0 <= test_idx < len(available_files):
                        test_file = available_files[test_idx]
                    else:
                        test_file = input("输入测试文件名: ").strip()
                else:
                    test_file = test_choice
                
                self.set('data_files.test_file', test_file)
                print(f" 测试文件: {test_file}")
                
                self.validate_data_files()
                
            except (ValueError, KeyboardInterrupt):
                print("配置已取消")
        else:
            print("未找到CSV文件，请手动输入:")
            train_file = input("训练文件名: ").strip()
            test_file = input("测试文件名: ").strip()
            
            if train_file:
                self.set('data_files.train_file', train_file)
            if test_file:
                self.set('data_files.test_file', test_file)

# 全局配置实例
config = ConfigManager()
